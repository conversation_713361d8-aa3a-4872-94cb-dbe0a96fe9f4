FROM python:3.9
# clearml sidecar执行镜像
# COPY requirements_ide.txt /home/<USER>

COPY . /home/

WORKDIR /home

RUN pip install -r requirements_ide.txt

COPY dist/clearml_agent-1.7.2-py3-none-any.whl /home/
COPY dist/clearml-1.13.3rc0-py2.py3-none-any.whl /home/

RUN pip install  clearml_agent-1.7.2-py3-none-any.whl --force-reinstall
RUN pip install clearml-1.13.3rc0-py2.py3-none-any.whl --force-reinstall


# clearml验证相关环境变量，启动时需另外指定
ENV CLEARML_API_HOST http://************:8008
ENV CLEARML_WEB_HOST http://************:28080
ENV CLEARML_FILES_HOST http://************:8081


# RUN apt update && apt install vim -y 
# TODO 配置环境变量


CMD cd /home && python -m examples.k8s_glue_example --ide-agent
