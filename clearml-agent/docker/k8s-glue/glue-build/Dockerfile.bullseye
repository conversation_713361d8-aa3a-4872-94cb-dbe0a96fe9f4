ARG TAG=3.7.17-slim-bullseye

FROM python:${TAG} as target

ARG KUBECTL_VERSION=1.22.4

WORKDIR /app

RUN python3 \
    -m pip \
    install \
    --no-cache-dir \
    -U \
    clearml-agent \
    cryptography>=2.9

# Not sure about these ENV vars
# ENV LC_ALL=en_US.UTF-8
# ENV LANG=en_US.UTF-8
# ENV LANGUAGE=en_US.UTF-8
# ENV PYTHONIOENCODING=UTF-8

ADD https://storage.googleapis.com/kubernetes-release/release/v${KUBECTL_VERSION}/bin/linux/amd64/kubectl /usr/bin/

RUN chmod +x /usr/bin/kubectl

COPY k8s_glue_example.py .

CMD ["python3", "k8s_glue_example.py"]

FROM target as aws

# https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html
# https://docs.aws.amazon.com/eks/latest/userguide/install-aws-iam-authenticator.html

RUN apt-get update -qqy && \
    apt-get install -qqy \
    unzip && \
    rm -rf /var/lib/apt/lists/*

ADD https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip awscliv2.zip
ADD https://amazon-eks.s3.us-west-2.amazonaws.com/1.21.2/2021-07-05/bin/linux/amd64/aws-iam-authenticator /usr/local/bin/aws-iam-authenticator

RUN unzip awscliv2.zip && \
    ./aws/install && \
    rm -r awscliv2.zip aws/ && \
    chmod +x /usr/local/bin/aws-iam-authenticator && \
    aws --version && \
    aws-iam-authenticator version

# https://github.com/GoogleCloudPlatform/cloud-sdk-docker/blob/master/debian_slim/Dockerfile

FROM target as gcp

ARG CLOUD_SDK_VERSION=371.0.0
ENV CLOUD_SDK_VERSION=$CLOUD_SDK_VERSION

ENV PATH "$PATH:/opt/google-cloud-sdk/bin/"

ARG INSTALL_COMPONENTS
RUN mkdir -p /usr/share/man/man1/
RUN apt-get update -qqy && \
    apt-get install -qqy \
        curl \
        gcc \
        python3-dev \
        python3-pip \
        apt-transport-https \
        lsb-release \
        openssh-client \
        git \
        gnupg && \
        rm -rf /var/lib/apt/lists/* && \
    pip3 install -U crcmod && \
    export CLOUD_SDK_REPO="cloud-sdk-$(lsb_release -c -s)" && \
    echo "deb https://packages.cloud.google.com/apt $CLOUD_SDK_REPO main" > /etc/apt/sources.list.d/google-cloud-sdk.list && \
    curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key add - && \
    apt-get update && apt-get install -y google-cloud-sdk=${CLOUD_SDK_VERSION}-0 $INSTALL_COMPONENTS && \
    gcloud config set core/disable_usage_reporting true && \
    gcloud config set component_manager/disable_update_check true && \
    gcloud config set metrics/environment github_docker_image && \
    gcloud --version
