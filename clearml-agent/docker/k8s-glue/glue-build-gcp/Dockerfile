FROM ubuntu:18.04

USER root
WORKDIR /root

ENV LC_ALL=en_US.UTF-8
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US.UTF-8
ENV PYTHONIOENCODING=UTF-8

COPY ./setup.sh /root/setup.sh
RUN /root/setup.sh

COPY clearml_agent-1.7.1-py3-none-any.whl /root/
RUN pip install clearml_agent-1.7.1-py3-none-any.whl 

COPY ./setup_gcp.sh /root/setup_gcp.sh
RUN chmod +x /root/setup_gcp.sh && /root/setup_gcp.sh

COPY ./entrypoint.sh /root/entrypoint.sh
COPY ./provider_entrypoint.sh /root/provider_entrypoint.sh
COPY ./k8s_glue_example.py /root/k8s_glue_example.py
COPY ./clearml.conf /root/clearml.conf

ENTRYPOINT ["/root/entrypoint.sh"]
