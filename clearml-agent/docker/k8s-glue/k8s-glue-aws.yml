apiVersion: v1
kind: Pod
metadata:
  name: k8s-glue
spec:
  serviceAccountName: ""
  containers:
    - name: k8s-glue-container
      image: allegroai/clearml-agent-k8s:aws-latest-1.21
      imagePullPolicy: Always
      command: [
          "/bin/bash",
          "-c",
          "source /root/.bashrc && /root/entrypoint.sh"
      ]
      volumeMounts:
        - name: pod-template
          mountPath: /root/template
      env:
        - name: CLEARML_API_HOST
          value: ""
        - name: CLEARML_WEB_HOST
          value: ""
        - name: CLEARML_FILES_HOST
          value: ""
#        - name: K8S_GLUE_MAX_PODS
#          value: "2"
        - name: K8S_GLUE_QUEUE
          value: "k8s-glue"
        - name: K8S_GLUE_EXTRA_ARGS
          value: "--template-yaml /root/template/pod_template.yml"
        - name: CLEARML_API_ACCESS_KEY
          value: ""
        - name: CLEARML_API_SECRET_KEY
          value: ""
        - name: CLEARML_WORKER_ID
          value: "k8s-glue-agent"
        - name: CLEARML_AGENT_UPDATE_REPO
          value: ""
        - name: FORCE_CLEARML_AGENT_REPO
          value: ""
        - name: CLEARML_DOCKER_IMAGE
          value: "ubuntu:18.04"
  volumes:
    - name: pod-template
      secret:
        secretName: k8s-glue-pod-template
