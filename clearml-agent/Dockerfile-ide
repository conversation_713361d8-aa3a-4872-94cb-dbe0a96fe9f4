FROM python:3.9

# COPY requirements_ide.txt /home/<USER>

COPY . /home/

WORKDIR /home

RUN pip install -r requirements_ide.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

ENV IDE_NODE ************
ENV IDE_URL_PREFIX http://************:46052

# clearml验证相关环境变量，启动时需另外指定
ENV CLEARML_API_HOST http://************:8008
ENV CLEARML_WEB_HOST http://************:28080
ENV CLEARML_FILES_HOST http://************:8081
ENV CLEARML_API_ACCESS_KEY xxxxxxxx
ENV CLEARML_API_SECRET_KEY XXXXXXXX


# RUN apt update && apt install vim -y 
# TODO 配置环境变量


CMD cd /home && python -m examples.k8s_glue_example --ide-agent
