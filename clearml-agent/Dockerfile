FROM python:3.9

COPY clearml-agent /home/<USER>
COPY template /home/<USER>

WORKDIR /home

RUN pip install -r /home/<USER>/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple \
    && cd /home/<USER>/ && python setup.py install

COPY clearml-agent/kubectl /home
RUN install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

CMD ["python", "/home/<USER>/clearml_agent/exmaples/k8s_glue_example.py"]