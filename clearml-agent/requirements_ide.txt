annotated-types==0.7.0
anyio==4.4.0
argcomplete==3.5.0
async-timeout==4.0.3
attrs==22.2.0
azure-core==1.29.7
azure-storage-blob==12.19.0
backoff==2.2.1
bcrypt==4.1.2
black==24.8.0
blinker==1.7.0
boltons==23.1.1
boto3==1.34.31
boto3-stubs==1.34.31
botocore==1.34.31
botocore-stubs==1.34.31
Brotli==1.1.0
cachetools==5.3.2
certifi==2023.11.17
cffi==1.16.0
charset-normalizer==3.3.2
clearml==1.16.2
click==8.1.7
cryptography==42.0.2
datamodel-code-generator==0.25.9
dnspython==2.5.0
docker==7.1.0
dpath==1.5.0
elasticsearch==7.17.9
email_validator==2.2.0
exceptiongroup==1.2.2
fastjsonschema==2.19.0
Flask==3.0.2
Flask-Compress==1.14
Flask-Cors==4.0.0
furl==2.1.3
genson==1.3.0
google-api-core==2.15.0
google-auth==2.29.0
google-cloud-core==2.4.1
google-cloud-storage==2.14.0
google-crc32c==1.5.0
google-resumable-media==2.7.0
googleapis-common-protos==1.62.0
gunicorn==21.2.0
h11==0.14.0
harborapi==0.25.2
httpcore==1.0.5
httpx==0.27.0
humanfriendly==10.0
idna==3.6
importlib-metadata==7.0.1
inflect==5.6.2
isodate==0.6.1
isort==5.13.2
itsdangerous==2.1.2
Jinja2==3.1.3
jmespath==1.0.1
jsonmodels==2.7.0
jsonschema==4.20.0
jsonschema-specifications==2023.12.1
kubernetes==30.1.0
luqum==0.13.0
MarkupSafe==2.1.5
mongoengine==0.27.0
mypy-boto3-s3==1.34.0
mypy-extensions==1.0.0
nested-dict==1.61
numpy==1.26.3
oauthlib==3.2.2
orderedmultidict==1.0.1
packaging==24.1
pathlib2==2.3.7.post1
pathspec==0.12.1
pillow==10.2.0
platformdirs==4.2.2
ply==3.11
protobuf==4.25.3
psutil==5.9.7
pyasn1==0.5.1
pyasn1-modules==0.3.0
pycparser==2.21
pydantic==2.8.2
pydantic_core==2.20.1
pyhocon==0.3.60
PyJWT==2.8.0
pymongo==4.4.0
pyparsing==3.1.1
python-dateutil==2.8.2
python-gitlab==4.9.0
python-rapidjson==1.14
PyYAML==6.0.1
redis==4.6.0
referencing==0.32.1
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rpds-py==0.17.1
rsa==4.9
s3transfer==0.10.0
semantic-version==2.10.0
six==1.16.0
sniffio==1.3.1
toml==0.10.2
tomli==2.0.1
types-awscrt==0.20.9
types-s3transfer==0.10.1
typing_extensions==4.12.1
urllib3==1.26.18
validators==0.28.1
websocket-client==1.8.0
Werkzeug==3.0.1
zipp==3.19.2
git-python==1.0.3
rarfile==4.2