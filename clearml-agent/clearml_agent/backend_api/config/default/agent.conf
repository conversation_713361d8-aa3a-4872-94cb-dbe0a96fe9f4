{
    # unique name of this worker, if None, created based on hostname:process_id
    # Override with os environment: CLEARML_WORKER_ID
    # worker_id: "clearml-agent-machine1:gpu0"
    worker_id: ""

    # worker name, replaces the hostname when creating a unique name for this worker
    # Override with os environment: CLEARML_WORKER_NAME
    # worker_name: "clearml-agent-machine1"
    worker_name: ""

    # Set GIT user/pass credentials (if user/pass are set, GIT protocol will be set to https)
    # leave blank for GIT SSH credentials (set force_git_ssh_protocol=true to force SSH protocol)
    # **Notice**: GitHub personal token is equivalent to password, you can put it directly into `git_pass`
    # To learn how to generate git token GitHub/Bitbucket/GitLab:
    # https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token
    # https://support.atlassian.com/bitbucket-cloud/docs/app-passwords/
    # https://docs.gitlab.com/ee/user/profile/personal_access_tokens.html
    # git_user: ""
    # git_pass: ""
    # Limit credentials to a single domain, for example: github.com,
    # all other domains will use public access (no user/pass). Default: always send user/pass for any VCS domain
    # git_host: ""

    # Force GIT protocol to use SSH regardless of the git url (Assumes GIT user/pass are blank)
    force_git_ssh_protocol: false
    # Force a specific SSH port when converting http to ssh links (the domain is kept the same)
    # force_git_ssh_port: 0
    # Force a specific SSH username when converting http to ssh links (the default username is 'git')
    # force_git_ssh_user: git

    # Set the python version to use when creating the virtual environment and launching the experiment
    # Example values: "/usr/bin/python3" or "/usr/local/bin/python3.6"
    # The default is the python executing the clearml_agent
    python_binary: ""
    # ignore any requested python version (Default: False, if a Task was using a
    # specific python version and the system supports multiple python the agent will use the requested python version)
    # ignore_requested_python_version: true

    # Force the root folder of the git repository (instead of the working directory) into the PYHTONPATH
    # default false, only the working directory will be added to the PYHTONPATH
    # force_git_root_python_path: false

    # if set, use GIT_ASKPASS to pass user/pass when cloning / fetch repositories
    # it solves passing user/token to git submodules.
    # this is a safer way to ensure multiple users using the same repository will
    # not accidentally leak credentials
    # Note: this is only supported on Linux systems
    # enable_git_ask_pass: true

    # in docker mode, if container's entrypoint automatically activated a virtual environment
    # use the activated virtual environment and install everything there
    # set to False to disable, and always create a new venv inheriting from the system_site_packages
    # docker_use_activated_venv: true

    # select python package manager:
    # currently supported: pip, conda and poetry
    # if "pip" or "conda" are used, the agent installs the required packages
    # based on the "installed packages" section of the Task. If the "installed packages" is empty,
    # it will revert to using `requirements.txt` from the repository's root directory.
    # If Poetry is selected and the root repository contains `poetry.lock` or `pyproject.toml`,
    # the "installed packages" section is ignored, and poetry is used.
    # If Poetry is selected and no lock file is found, it reverts to "pip" package manager behaviour.
    package_manager: {
        # supported options: pip, conda, poetry
        type: pip,

        # specify pip version to use (examples "<20.2", "==19.3.1", "", empty string will install the latest version)
        pip_version: ["<20.2 ; python_version < '3.10'", "<22.3 ; python_version >= '3.10'"],
        # specify poetry version to use (examples "<2", "==1.1.1", "", empty string will install the latest version)
        # poetry_version: "<2",
        # poetry_install_extra_args: ["-v"]

        # virtual environment inherits packages from system
        system_site_packages: false,

        # install with --upgrade
        force_upgrade: false,

        # additional artifact repositories to use when installing python packages
        # extra_index_url: ["https://allegroai.jfrog.io/clearml/api/pypi/public/simple"]

        # control the pytorch wheel resolving algorithm, options are: "pip", "direct", "none"
        # Override with environment variable CLEARML_AGENT_PACKAGE_PYTORCH_RESOLVE
        # "pip" (default): would automatically detect the cuda version, and supply pip with the correct
        # extra-index-url, based on pytorch.org tables
        # "direct": would resolve a direct link to the pytorch wheel by parsing the pytorch.org pip repository
        # and matching the automatically detected cuda version with the required pytorch wheel.
        # if the exact cuda version is not found for the required pytorch wheel, it will try
        # a lower cuda version until a match is found
        # "none": No resolver used, install pytorch like any other package
        # pytorch_resolve: "pip"

        # additional conda channels to use when installing with conda package manager
        conda_channels: ["pytorch", "conda-forge", "defaults", ]

        # If set to true, Task's "installed packages" are ignored,
        # and the repository's "requirements.txt" is used instead
        # force_repo_requirements_txt: false

        # set the priority packages to be installed before the rest of the required packages
        # Note: this only controls the installation order of existing requirement packages (and does not add additional packages)
        # priority_packages: ["cython", "numpy", "setuptools", ]

        # set the optional priority packages to be installed before the rest of the required packages,
        # In case a package installation fails, the package will be ignored,
        # and the virtual environment process will continue
        # Note: this only controls the installation order of existing requirement packages (and does not add additional packages)
        priority_optional_packages: ["pygobject", ]

        # set the post packages to be installed after all the rest of the required packages
        # Note: this only controls the installation order of existing requirement packages (and does not add additional packages)
        # post_packages: ["horovod", ]

        # set the optional post packages to be installed after all the rest of the required packages,
        # In case a package installation fails, the package will be ignored,
        # and the virtual environment process will continue
        # Note: this only controls the installation order of existing requirement packages (and does not add additional packages)
        # post_optional_packages: []

        # set to True to support torch nightly build installation,
        # notice: torch nightly builds are ephemeral and are deleted from time to time
        torch_nightly: false,

        # if set to true, the agent will look for the "poetry.lock" file 
        # in the passed current working directory instead of the repository's root directory.
        poetry_files_from_repo_working_dir: false
    },

    # target folder for virtual environments builds, created when executing experiment
    venvs_dir = ~/.clearml/venvs-builds

    # cached virtual environment folder
    venvs_cache: {
        # maximum number of cached venvs
        max_entries: 10
        # minimum required free space to allow for cache entry, disable by passing 0 or negative value
        free_space_threshold_gb: 2.0
        # unmark to enable virtual environment caching
        path: ~/.clearml/venvs-cache
    },

    # cached git clone folder
    vcs_cache: {
        enabled: true,
        path: ~/.clearml/vcs-cache
    },

    # use venv-update in order to accelerate python virtual environment building
    # Still in beta, turned off by default
    venv_update: {
        enabled: false,
    },

    # cached folder for specific python package download (used for pytorch package caching)
    pip_download_cache {
        enabled: true,
        path: ~/.clearml/pip-download-cache
    },

    translate_ssh: true,

    # set "disable_ssh_mount: true" to disable the automatic mount of ~/.ssh folder into the docker containers
    # default is false, automatically mounts ~/.ssh
    # Must be set to True if using "clearml-session" with this agent!
    # disable_ssh_mount: false

    # reload configuration file every daemon execution
    reload_config: false,

    # pip cache folder mapped into docker, used for python package caching
    docker_pip_cache = ~/.clearml/pip-cache
    # apt cache folder mapped into docker, used for ubuntu package caching
    docker_apt_cache = ~/.clearml/apt-cache

    # optional arguments to pass to docker image
    # these are local for this agent and will not be updated in the experiment's docker_cmd section
    # extra_docker_arguments: ["--ipc=host", ]

    # Allow the extra docker arg to override task level docker arg (if the same argument is passed on both),
    # if set to False, a task docker arg will override the docker extra arg
    # docker_args_extra_precedes_task: true

    # allows the following task docker args to be overridden by the extra_docker_arguments
    # protected_docker_extra_args: ["privileged", "security-opt", "network", "ipc"]

    # optional shell script to run in docker when started before the experiment is started
    # extra_docker_shell_script: ["apt-get install -y bindfs", ]

    # Install the required packages for opencv libraries (libsm6 libxext6 libxrender-dev libglib2.0-0),
    # for backwards compatibility reasons, true as default,
    # change to false to skip installation and decrease docker spin up time
    # docker_install_opencv_libs: true

    # optional uptime configuration, make sure to use only one of 'uptime/downtime' and not both.
    # If uptime is specified, agent will actively poll (and execute) tasks in the time-spans defined here.
    # Outside of the specified time-spans, the agent will be idle.
    # Defined using a list of items of the format: "<hours> <days>".
    # hours - use values 0-23, single values would count as start hour and end at midnight.
    # days - use days in abbreviated format (SUN-SAT)
    # use '-' for ranges and ',' to separate singular values.
    # for example, to enable the workers every Sunday and Tuesday between 17:00-20:00 set uptime to:
    # uptime: ["17-20 SUN,TUE"]

    # optional downtime configuration, can be used only when uptime is not used.
    # If downtime is specified, agent will be idle in the time-spans defined here.
    # Outside of the specified time-spans, the agent will actively poll (and execute) tasks.
    # Use the same format as described above for uptime
    # downtime: []

    # set to true in order to force "docker pull" before running an experiment using a docker image.
    # This makes sure the docker image is updated.
    docker_force_pull: false

    default_docker: {
        # default docker image to use when running in docker mode
        image: "nvidia/cuda:11.0.3-cudnn8-runtime-ubuntu20.04"

        # optional arguments to pass to docker image
        # arguments: ["--ipc=host", ]
    }

    # set the OS environments based on the Task's Environment section before launching the Task process.
    enable_task_env: false

    # set the initial bash script to execute at the startup of any docker.
    # all lines will be executed regardless of their exit code.
    # {python_single_digit} is translated to 'python3' or 'python2' according to requested python version
    # docker_init_bash_script = [
    #     "echo 'Binary::apt::APT::Keep-Downloaded-Packages \"true\";' > /etc/apt/apt.conf.d/docker-clean",
    #     "chown -R root /root/.cache/pip",
    #     "apt-get update",
    #     "apt-get install -y git libsm6 libxext6 libxrender-dev libglib2.0-0",
    #     "(which {python_single_digit} && {python_single_digit} -m pip --version) || apt-get install -y {python_single_digit}-pip",
    # ]

    # set the preprocessing bash script to execute at the startup of any docker.
    # all lines will be executed regardless of their exit code.
    # docker_preprocess_bash_script = [
    #     "echo \"starting docker\"",
    #]

    # If False replace \r with \n and display full console output
    # default is True, report a single \r line in a sequence of consecutive lines, per 5 seconds.
    # suppress_carriage_return: true

    # CUDA versions used for Conda setup & solving PyTorch wheel packages
    # Should be detected automatically. Override with os environment CUDA_VERSION / CUDNN_VERSION
    # cuda_version: 10.1
    # cudnn_version: 7.6

    # Sanitize configuration printout using these settings
    sanitize_config_printout {
        # Hide values of configuration keys matching these regexps
        hide_secrets: ["^sanitize_config_printout$", "secret", "pass", "token", "account_key", "contents"]
        # As above, only show field's value keys if value is a dictionary
        hide_secrets_recursive: ["^environment$"]
        # Do not hide for keys matching these regexps
        dont_hide_secrets: ["^enable_git_ask_pass$"]
        # Hide secrets in docker commands, according to the 'agent.hide_docker_command_env_vars' settings
        docker_commands: ["^extra_docker_arguments$"]
        # Hide password in URLs found in keys matching these regexps (handles single URLs, lists and dictionaries)
        urls: ["^extra_index_url$"]
    }

    # Hide docker environment variables containing secrets when printing out the docker command by replacing their
    # values with "********". Turning this feature on will hide the following environment variables values:
    #   CLEARML_API_SECRET_KEY, CLEARML_AGENT_GIT_PASS, AWS_SECRET_ACCESS_KEY, AZURE_STORAGE_KEY
    # To include more environment variables, add their keys to the "extra_keys" list. E.g. to make sure the value of
    # your custom environment variable named MY_SPECIAL_PASSWORD will not show in the logs when included in the
    # docker command, set:
    #   extra_keys: ["MY_SPECIAL_PASSWORD"]
    hide_docker_command_env_vars {
        enabled: true
        extra_keys: []
        parse_embedded_urls: true
    }

    # Maximum execution time (in seconds) for Task's abort function call
    abort_callback_max_timeout: 1800

    # allow to set internal mount points inside the docker,
    # especially useful for non-root docker container images.
    docker_internal_mounts {
        sdk_cache: "/clearml_agent_cache"
        apt_cache: "/var/cache/apt/archives"
        ssh_folder: "~/.ssh"
        ssh_ro_folder: "/.ssh"
        pip_cache: "/root/.cache/pip"
        poetry_cache: "/root/.cache/pypoetry"
        vcs_cache: "/root/.clearml/vcs-cache"
        venv_build: "~/.clearml/venvs-builds"
        pip_download: "/root/.clearml/pip-download-cache"
    }

    # Name docker containers created by the daemon using the following string format (supported from Docker 0.6.5)
    # Allowed variables are task_id, worker_id and rand_string (random lower-case letters string, up to 32 characters)
    # Custom variables may be specified using the docker_container_name_format_fields option.
    # Note: resulting name must start with an alphanumeric character and
    #       continue with alphanumeric characters, underscores (_), dots (.) and/or dashes (-)
    # docker_container_name_format: "clearml-id-{task_id}-{rand_string:.8}"

    # Specify custom variables for the docker_container_name_format option using a mapping of variable name
    # to a (nested) task field (using "." as a task field separator, digits specify array index)
    # docker_container_name_format_fields: { foo: "bar.moo" }

    # Apply top-level environment section from configuration into os.environ
    apply_environment: true
    # Top-level environment section is in the form of:
    #   environment {
    #     key: value
    #     ...
    #   }
    # and is applied to the OS environment as `key=value` for each key/value pair

    # Apply top-level files section from configuration into local file system
    apply_files: true
    # Top-level files section allows auto-generating files at designated paths with a predefined contents
    # and target format. Options include:
    #  contents: the target file's content, typically a string (or any base type int/float/list/dict etc.)
    #  format: a custom format for the contents. Currently supported value is `base64` to automatically decode a
    #          base64-encoded contents string, otherwise ignored
    #  path: the target file's path, may include ~ and inplace env vars
    #  target_format: format used to encode contents before writing into the target file. Supported values are json,
    #                 yaml, yml and bytes (in which case the file will be written in binary mode). Default is text mode.
    #  overwrite: overwrite the target file in case it exists. Default is true.
    #  mode: file-system mode to be applied to the file after its creation. The mode string will be parsed into an
    #        integer (e.g. "0o777" for -rwxrwxrwx)
    #
    # Example:
    #   files {
    #     myfile1 {
    #       contents: "The quick brown fox jumped over the lazy dog"
    #       path: "/tmp/fox.txt"
    #     }
    #     myjsonfile {
    #       contents: {
    #         some {
    #           nested {
    #             value: [1, 2, 3, 4]
    #           }
    #         }
    #       }
    #       path: "/tmp/test.json"
    #       target_format: json
    #     }
    #   }

    # Specifies a custom environment setup script to be executed instead of installing a virtual environment.
    # If provided, this script is executed following Git cloning. Script command may include environment variable and
    # will be expanded before execution (e.g. "$CLEARML_GIT_ROOT/script.sh").
    # The script can also be specified using the CLEARML_AGENT_CUSTOM_BUILD_SCRIPT environment variable.
    #
    # When running the script, the following environment variables will be set:
    # - CLEARML_CUSTOM_BUILD_TASK_CONFIG_JSON: specifies a path to a temporary files containing the complete task
    #  contents in JSON format
    # - CLEARML_TASK_SCRIPT_ENTRY: task entrypoint script as defined in the task's script section
    # - CLEARML_TASK_WORKING_DIR: task working directory as defined in the task's script section
    # - CLEARML_VENV_PATH: path to the agent's default virtual environment path (as defined in the configuration)
    # - CLEARML_GIT_ROOT: path to the cloned Git repository
    # - CLEARML_CUSTOM_BUILD_OUTPUT: a path to a non-existing file that may be created by the script. If created,
    #  this file must be in the following JSON format:
    #      ```json
    #      {
    #        "binary": "/absolute/path/to/python-executable",
    #        "entry_point": "/absolute/path/to/task-entrypoint-script",
    #        "working_dir": "/absolute/path/to/task-working/dir"
    #      }
    #      ```
    #  If provided, the agent will use these instead of the predefined task script section to execute the task and will
    #  skip virtual environment creation.
    #
    # In case the custom script returns with a non-zero exit code, the agent will fail with the same exit code.
    # In case the custom script is specified but does not exist, or if the custom script does not write valid content
    # into the file specified in CLEARML_CUSTOM_BUILD_OUTPUT, the agent will emit a warning and continue with the
    # standard flow.
    custom_build_script: ""

    # Crash on exception: by default when encountering an exception while running a task,
    # the agent will catch the exception, log it and continue running.
    # Set this to `true` to propagate exceptions and crash the agent.
    # crash_on_exception: true

    # Disable task docker override. If true, the agent will use the default docker image and ignore any docker image
    # and arguments specified in the task's container section (setup shell script from the task container section will
    # be used in any case, if specified).
    disable_task_docker_override: false

    # Choose the default docker based on the Task properties,
    # Examples: 'script.requirements', 'script.binary', 'script.repository', 'script.branch', 'project'
    # Notice: Matching is done via regular expression, for example "^searchme$" will match exactly "searchme$" string
    #
    #     "default_docker": {
    #         "image": "nvidia/cuda:11.0.3-cudnn8-runtime-ubuntu20.04",
    #         # optional arguments to pass to docker image
    #         # arguments: ["--ipc=host", ]
    #         "match_rules": [
    #             {
    #                 "image": "sample_container:tag",
    #                 "arguments": "-e VALUE=1 --ipc=host",
    #                 "match": {
    #                     "script": {
    #                         "requirements": {
    #                             "pip": {
    #                                 "tensorflow": "~=1.6"
    #                             }
    #                         },
    #                         "repository": "",
    #                         "branch": "master"
    #                     },
    #                     "project": "example"
    #                 }
    #             },
    #             {
    #                 "image": "another_container:tag",
    #                 "arguments": "",
    #                 "match": {
    #                     "project": "^examples", # anything that starts with "examples", e.g. "examples", "examples/sub_project"
    #                 }
    #             }
    #         ]
    #     },
    #
}
