import six
import types
from datetime import datetime
import enum

from dateutil.parser import parse as parse_datetime

from ....backend_api.session import Request, BatchRequest, Response, DataModel, NonStrictDataModel, CompoundRequest, schema_property, StringEnum


class Ide(NonStrictDataModel):
    _schema = {
        'properties': {
            # 'company': {'description': 'Company id', 'type': ['string', 'null']},
            'created': {
                'description': 'ide creation time',
                'format': 'date-time',
                'type': ['string', 'null'],
            },
            'entries': {
                'description': 'List of ordered ide entries',
                'items': {'$ref': '#/definitions/entry'},
                'type': ['array', 'null'],
            },
            'id': {'description': 'ide id', 'type': ['string', 'null']},
            'name': {'description': 'ide name', 'type': ['string', 'null']},
            'tags': {
                'description': 'User-defined tags',
                'items': {'type': 'string'},
                'type': ['array', 'null'],
            },
            'user': {
                'description': 'Associated user id',
                'type': ['string', 'null'],
            },
            'cpu': {
                'description': 'cpu count',
                'type': ['integer', '1'],
            },
            'mem': {
                'description': 'Associated user id',
                'type': ['integer', '1024'],
            },
            'gpu': {
                'description': 'Associated user id',
                'type': ['integer', '0'],
            },
            'status': {
                'description': 'ide status',
                'type': ['string', 'null'],
            },
            'image': {
                'description': 'ide image',
                'type': ['string', 'null'],
            },
            'algorithmss': {
                'description': 'Ide algorithmss',
                'items': {'type': 'string'},
                'type': ['array', 'null'],
            },
            'dataset_path': {
                'description': 'dataset path',
                'type': ['string', 'null'],
            }, 
        },
        'type': 'object',
    }
    def __init__(self, id=None, name=None, user=None, created=None, cpu=None, mem=None, gpu=None,status=None,image=None,algorithms=None,dataset_path=None, **kwargs):
        super(Ide, self).__init__(**kwargs)
        self.id = id
        self.user = user
        # self.company = company
        self.name = name
        self.created = created
        self.cpu = cpu
        self.mem = mem
        self.gpu = gpu
        self.status = status
        self.image = image
        self.algorithms = algorithms
        self.dataset_path = dataset_path

    @schema_property('id')
    def id(self):
        return self._property_id

    @id.setter
    def id(self, value):
        if value is None:
            self._property_id = None
            return

        self.assert_isinstance(value, "id", six.string_types)
        self._property_id = value

    @schema_property('name')
    def name(self):
        return self._property_name

    @name.setter
    def name(self, value):
        if value is None:
            self._property_name = None
            return

        self.assert_isinstance(value, "name", six.string_types)
        self._property_name = value

    @schema_property('dataset_path')
    def dataset_path(self):
        return self._property_dataset_path

    @dataset_path.setter
    def dataset_path(self, value):
        if value is None:
            self._property_dataset_path = None
            return

        self.assert_isinstance(value, "dataset_path", six.string_types)
        self._property_dataset_path = value

    @schema_property('user')
    def user(self):
        return self._property_user

    @user.setter
    def user(self, value):
        if value is None:
            self._property_user = None
            return

        self.assert_isinstance(value, "user", six.string_types)
        self._property_user = value

    # @schema_property('company')
    # def company(self):
    #     return self._property_company

    # @company.setter
    # def company(self, value):
    #     if value is None:
    #         self._property_company = None
    #         return

    #     self.assert_isinstance(value, "company", six.string_types)
    #     self._property_company = value

    @schema_property('status')
    def status(self):
        return self._property_status

    @status.setter
    def status(self, value):
        if value is None:
            self._property_status = None
            return

        self.assert_isinstance(value, "status", six.string_types)
        self._property_status = value

    @schema_property('image')
    def image(self):
        return self._property_image

    @image.setter
    def image(self, value):
        if value is None:
            self._property_image = None
            return

        self.assert_isinstance(value, "image", six.string_types)
        self._property_image = value

    @schema_property('cpu')
    def cpu(self):
        return self._property_cpu

    @cpu.setter
    def cpu(self, value):
        if value is None:
            self._property_cpu = None
            return

        self.assert_isinstance(value, "cpu", six.integer_types)
        self._property_cpu = value

    @schema_property('mem')
    def mem(self):
        return self._property_mem

    @mem.setter
    def mem(self, value):
        if value is None:
            self._property_mem = None
            return

        self.assert_isinstance(value, "mem", six.integer_types)
        self._property_mem = value

    @schema_property('gpu')
    def gpu(self):
        return self._property_gpu

    @gpu.setter
    def gpu(self, value):
        if value is None:
            self._property_gpu = None
            return

        self.assert_isinstance(value, "gpu", six.integer_types)
        self._property_gpu = value
    
    @schema_property('algorithms')
    def algorithms(self):
        return self._property_algorithms

    @algorithms.setter
    def algorithms(self, value):
        if value is None:
            self._property_algorithms = None
            return

        self.assert_isinstance(value, "algorithms", (list, tuple))

        self.assert_isinstance(value, "algorithms", six.string_types, is_array=True)
        self._property_algorithms = value


class GetByIdRequest(Request):
    _service = "ides"
    _action = "get_by_id"
    _version = "2.5"
    _schma = {
        'definitions': {},
        'properties': {'ide': {'description': 'Ide ID', 'type': 'string'}},
        'required': ['ide'],
        'type': 'object',
    }
    def __init__(self, ide, **kwargs):
        super(GetByIdRequest, self).__init__(**kwargs)
        self.ide = ide

class GetByIdResponse(Response):
    _service = "ides"
    _action = "get_by_id"
    _version = "2.5"
    _schema = {
        'definitions': {
            'ide': {
                'properties': {
                    'company': {
                        'description': 'Company id',
                        'type': ['string', 'null'],
                    },
                    'created': {
                        'description': 'Ide creation time',
                        'format': 'date-time',
                        'type': ['string', 'null'],
                    },
                    'id': {'description': 'Ide id', 'type': ['string', 'null']},
                    'name': {
                        'description': 'Ide name',
                        'type': ['string', 'null'],
                    },
                    'tags': {
                        'description': 'User-defined tags',
                        'items': {'type': 'string'},
                        'type': ['array', 'null'],
                    },
                    'user': {
                        'description': 'Associated user id',
                        'type': ['string', 'null'],
                    },
                    'image': {
                        'description': 'ide image',
                        'type': ['string', 'null'],
                    },
                    'status': {
                        'description': 'ide status',
                        'type': ['string', 'null'],
                    },
                    'cpu': {
                        'description': 'cpu',
                        'type': ['integer', 'null'],
                    },
                    'mem': {
                        'description': 'mem',
                        'type': ['integer', 'null'],
                    },
                    'gpu': {
                        'description': 'gpu',
                        'type': ['integer', 'null'],
                    },
                },
                'type': 'object',
            },
        },
        'properties': {
            'ide': {
                'description': 'Ide info',
                'oneOf': [{'$ref': '#/definitions/ide'}, {'type': 'null'}],
            },
        },
        'type': 'object',
    }

    def __init__(
        self, ide=None, **kwargs
    ):
        super(GetByIdResponse, self).__init__(**kwargs)
        self.ide = ide

    @schema_property('ide')
    def ide(self):
        return self._property_ide

    @ide.setter
    def ide(self, value):
        if value is None:
            self._property_ide = None
            return
        if isinstance(value, dict):
            value = Ide.from_dict(value)
        else:
            self.assert_isinstance(value, "ide", Ide)
        self._property_ide = value

class IdeStatusEnum(StringEnum):
    tostart = "tostart"
    starting = "starting"
    running = "running"
    stopping = "stopping"
    stopped = "stopped"
    failed = "failed",
    deleting = 'deleting',
    unknown = "unknown"

class GetAllRequest(Request):
    _service = "ides"
    _action = "get_all"
    _version = "2.5"
    _schema = {
        'definitions':{
            'ide_status_enum': {
                'enum': [
                    'tostart',
                    'starting',
                    'running',
                    'stopping',
                    'stopped',
                    'failed',
                    'deleting',
                    'unknown',
                ],
            'type': 'string',
            },
        },
        'properties': {
            'id': {
                'description': 'id',
                'type': ['string', 'null'],
            },
            'name': {
                'description': 'name',
                'type': ['string', 'null'],
            },
            'status': {
                'description': 'ides status.',
                'items': {'type': 'string'},        #这里看下怎么用枚举的
                'type': ['array', 'null'],
            },
        },
        'type': 'object',
    }
    def __init__(
            self, id=None, name=None, status=None, **kwargs):
        super(GetAllRequest, self).__init__(**kwargs)
        self.id = id
        self.name = name
        self.status = status
    
    @schema_property('id')
    def id(self):
        return self._property_id

    @id.setter
    def id(self, value):
        if value is None:
            self._property_id = None
            return

        self.assert_isinstance(value, "id", (list, tuple))

        self.assert_isinstance(value, "id", six.string_types, is_array=True)
        self._property_id = value
    

    @schema_property('name')
    def name(self):
        return self._property_id

    @name.setter
    def name(self, value):
        if value is None:
            self._property_name = None
            return

        self.assert_isinstance(value, "name", (list, tuple))

        self.assert_isinstance(value, "name", six.string_types, is_array=True)
        self._property_name = value

    @schema_property('status')
    def status(self):
        return self._property_status

    @status.setter
    def status(self, value):
        if value is None:
            self._property_status = None
            return
        self.assert_isinstance(value, "status", (list, tuple))

        self.assert_isinstance(value, "status", six.string_types , is_array=True)
        self._property_status = value

class GetAllResponse(Response):
    _service = "ides"
    _action = "get_all"
    _version = "2.5"
    _schema = {
        'definitions': {
            'ide': {
                'properties': {
                    'company': {
                        'description': 'Company id',
                        'type': ['string', 'null'],
                    },
                    'created': {
                        'description': 'Ide creation time',
                        'format': 'date-time',
                        'type': ['string', 'null'],
                    },
                    'id': {'description': 'Ide id', 'type': ['string', 'null']},
                    'name': {
                        'description': 'Ide name',
                        'type': ['string', 'null'],
                    },
                    'tags': {
                        'description': 'User-defined tags',
                        'items': {'type': 'string'},
                        'type': ['array', 'null'],
                    },
                    'user': {
                        'description': 'Associated user id',
                        'type': ['string', 'null'],
                    },
                    'image': {
                        'description': 'ide image',
                        'type': ['string', 'null'],
                    },
                    'status': {
                        'description': 'ide status',
                        'type': ['string', 'null'],
                    },
                    'cpu': {
                        'description': 'cpu',
                        'type': ['integer', 'null'],
                    },
                    'mem': {
                        'description': 'mem',
                        'type': ['integer', 'null'],
                    },
                    'gpu': {
                        'description': 'gpu',
                        'type': ['integer', 'null'],
                    },
                    'algorithms': {
                        'description': 'algorithm list',
                        'items': {'type': 'string'},
                        'type': ['array', 'null'],
                    },
                },
                'type': 'object',
            },
        },
        'properties': {
            'ides': {
                'description': 'Ide info',
                'items': [{'$ref': '#/definitions/ide'}, {'type': 'null'}],
                'type': ['array', 'null'],
            },
        },
        'type': 'object',
    }
    def __init__(
            self, ides=None, **kwargs):
        super(GetAllResponse, self).__init__(**kwargs)
        self.ides = ides
    
    @schema_property('ides')
    def ides(self):
        return self._property_ides

    @ides.setter
    def ides(self, value):
        if value is None:
            self._property_ides = None
            return
        self.assert_isinstance(value, "ides", (list, tuple))
        if any(isinstance(v, dict) for v in value):
            value = [Ide.from_dict(v) if isinstance(v, dict) else v for v in value]
        else:
            self.assert_isinstance(value, "ides", Ide , is_array=True)
        self._property_ides = value

class UpdateRequest(Request):
    _service = "ides"
    _action = "update"
    _version = "2.5"
    _schema = {
        'properties': {
            'status': {
                'description': 'Ide status.',
                'type': 'string',
            },
            'url_vscode': {
                'description': 'vscode url',
                'type': 'string',
            },
            'url_jupyter': {
                'description': 'jupyter url',
                'type': 'string',
            },
            'ide': {'description': 'ID of the ide', 'type': 'string'},
        },
        'required': ['ide'],
        'type': 'object',
    }
    def __init__(self, ide=None, status=None, url_vscode=None, url_jupyter=None, **kwargs):
        super(UpdateRequest, self).__init__(**kwargs)
        self.ide = ide
        self.status = status
        self.url_vscode = url_vscode
        self.url_jupyter = url_jupyter

    @schema_property('ide')
    def ide(self):
        return self._property_ide

    @ide.setter
    def ide(self, value):
        if value is None:
            self._property_ide = None
            return

        self.assert_isinstance(value, "ide", six.string_types)
        self._property_ide = value
    
    @schema_property('status')
    def status(self):
        return self._property_status

    @status.setter
    def status(self, value):
        if value is None:
            self._property_status = None
            return

        self.assert_isinstance(value, "status", six.string_types)
        self._property_status = value

    @schema_property('url_vscode')
    def url_vscode(self):
        return self._property_url_vscode

    @url_vscode.setter
    def url_vscode(self, value):
        if value is None:
            self._property_url_vscode = None
            return

        self.assert_isinstance(value, "url_vscode", six.string_types)
        self._property_url_vscode = value
    
    @schema_property('url_jupyter')
    def url_jupyter(self):
        return self._property_url_jupyter

    @url_jupyter.setter
    def url_jupyter(self, value):
        if value is None:
            self._property_url_jupyter = None
            return

        self.assert_isinstance(value, "url_jupyter", six.string_types)
        self._property_url_jupyter = value

    

class UpdateResponse(Response):
    _service = "ides"
    _action = "update"
    _version = "2.5"
    _schema = {
        'definitions': {},
        'properties': {
            'fields': {
                'additionalProperties': True,
                'description': 'Updated fields names and values',
                'type': ['object', 'null'],
            },
            'updated': {
                'description': 'Number of tasks updated (0 or 1)',
                'enum': [0, 1],
                'type': ['integer', 'null'],
            },
        },
        'type': 'object',
    }
    def __init__(
            self, updated=None, fields=None, **kwargs):
        super(UpdateResponse, self).__init__(**kwargs)
        self.updated = updated
        self.fields = fields
    @schema_property('updated')
    def updated(self):
        return self._property_updated

    @updated.setter
    def updated(self, value):
        if value is None:
            self._property_updated = None
            return
        if isinstance(value, float) and value.is_integer():
            value = int(value)

        self.assert_isinstance(value, "updated", six.integer_types)
        self._property_updated = value

    @schema_property('fields')
    def fields(self):
        return self._property_fields

    @fields.setter
    def fields(self, value):
        if value is None:
            self._property_fields = None
            return

        self.assert_isinstance(value, "fields", (dict,))
        self._property_fields = value

class DeleteRequest(Request):
    _service = "ides"
    _action = "delete"
    _version = "2.5"
    # _schma = {
    #     'definitions': {},
    #     'properties': {'ide': {'description': 'Ide ID', 'type': ['array', 'null'], 'items': {'type': 'string'},}},
    #     'required': ['ide'],
    #     'type': 'object',
    # }

    _schema = {
        'definitions': {},
        'properties': {
            'ide': {
                'description': 'Ide id',
                'type': ['array', 'null'],
                'items': {'type': 'string'},
            },
        },
        'type': 'object',
    }

    def __init__(self, ide, **kwargs):
        super(DeleteRequest, self).__init__(**kwargs)
        self.ide = ide

    @schema_property('ide')
    def ide(self):
        return self._property_ide

    @ide.setter
    def ide(self, value):
        if value is None:
            self._property_ide = None
            return

        self.assert_isinstance(value, "ide", (list, tuple))

        self.assert_isinstance(value, "ide", six.string_types, is_array=True)
        self._property_ide = value
    

class DeleteResponse(Response):
    _service = "ides"
    _action = "delete"
    _version = "2.5"
    _schema = {
        'definitions': {},
        'properties': {
            'deleted': {
                'description': 'Number of tasks updated (0 or 1)',
                'enum': [0, 1],
                'type': ['integer', 'null'],
            },
        },
        'type': 'object',
    }
    def __init__(
            self, deleted=None,  **kwargs):
        super(DeleteResponse, self).__init__(**kwargs)
        self.deleted = deleted
    @schema_property('deleted')
    def deleted(self):
        return self._property_deleted

    @deleted.setter
    def deleted(self, value):
        if value is None:
            self._property_deleted = None
            return
        if isinstance(value, float) and value.is_integer():
            value = int(value)

        self.assert_isinstance(value, "deleted", six.integer_types)
        self._property_deleted = value

response_mapping = {
    GetByIdRequest: GetByIdResponse,
    GetAllRequest: GetAllResponse,
    UpdateRequest: UpdateResponse,
    DeleteRequest: DeleteResponse,
}
