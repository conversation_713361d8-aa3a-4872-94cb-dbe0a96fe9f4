from clearml_agent.commands.worker import Worker, get_task_container, set_task_container, get_next_task
from kubernetes import client, config, utils, watch
from kubernetes.dynamic import DynamicClient
from clearml_agent.backend_api.services import ides as ides_api
from clearml_agent.backend_api.services import workers as workers_api
import time
import logging
import yaml
import os
import threading
import requests
from os import path


class K8sClient():

    def __init__(
        self,
        log,
            # config_file = "/root/.kube/config",
    ):
        config.load_kube_config()
        K8sClient.api_client = client.ApiClient()
        K8sClient.core_api =  client.CoreV1Api()
        K8sClient.apps_api = client.AppsV1Api()
        K8sClient.dyn_client = DynamicClient(client.ApiClient())
        self.log = log

    # 通用k8s资源创建
    def create_from_yaml(self, namespace, yaml_object):
        try:
            utils.create_from_yaml(self.api_client,yaml_objects = yaml_object)
        except Exception as err:
            self.log.warning('K8sClient create_from_yaml failed : {}',err)

    def create_crd_from_yaml(self, version, kind, namesapce, yaml):
        try:
            crd_resource = self.dyn_client.resources.get(api_version=version, kind=kind)
            created_crd = crd_resource.create(api_version=version, kind=kind ,body=yaml)
            # print(created_crd)
        except Exception as err:
            self.log.warning('K8sClient create_crd_from_yaml failed : {}',err)

    def delete_deploy(self, name, namespace):
        print(f'delete deploy {name} in {namespace}')
        try:
            self.apps_api.delete_namespaced_deployment(name=name, namespace=namespace)
        except Exception as err:
            self.log.warning('K8sClient delete_deploy failed : {}',err)

    def delete_svc(self, name, namespace):
        print(f'delete svc {name} in {namespace}')
        try:
            self.core_api.delete_namespaced_service(name=name, namespace=namespace)
        except Exception as err:
            self.log.warning('K8sClient delete_svc failed : {}',err)
        
    def delete_crd(self, version, kind, name, namespace):
        print(f'delete crd {name} in {namespace}')
        try:
            my_resource = self.dyn_client.resources.get(api_version=version, kind=kind)
            my_resource.delete(name=name, namespace=namespace)
        except Exception as err:
             self.log.warning('K8sClient delete_crd failed : {}',err)

    def watch_event(self, namespace='tiduai', running_func=None, stopping_func=None):
        while True:
            w = watch.Watch()
            try:
                # for event in w.stream(self.core_api.list_pod_for_all_namespaces, namespace=namespace, timeout_seconds=1000):
                for event in w.stream(self.core_api.list_pod_for_all_namespaces, timeout_seconds=100000000):
                    labels = event['object'].metadata.labels
                    if 'app' in  labels and labels['app'] == 'notebook':
                        id = labels['id']
                        # print(event['object'].metadata)
                        print('Event Phase:', event["object"].status.phase)
                        print('Event type: ', event["type"])
                        if event["object"].status.phase == "Running":
                            running_func(id)
                        if event["type"] == "DELETED":
                            stopping_func(id)

            except Exception as ex:
                self.log.warning('K8sClient watch failed : {}',ex)
            time.sleep(10)
        
class IdeIntegration(Worker):
    DEPLOY_NAME_TEMPLATE='notebook-{}'
    SVC_NAME_TEMPLATE='notebook-svc-{}'
    LABLE_APP_TEMPLATE='notebook-{}'
    NOTEBOOK_ROUTE_NAME_TEMPLATE='notebook-route-{}'

    def __init__(
            self,
            template_path = '/root/template/ide_deploy_temp.yaml',
            route_template_path = '/root/template/apisixroute_temp.yaml',
            debug=False,
            ide_url_prefix=None,
             *args, 
             **kwargs
    ):
        super(IdeIntegration, self).__init__(*args, **kwargs)
        self.log = self._session.get_logger(__name__)
        
        self.template_path = template_path  # ide 创建模板
        self.route_template_path = route_template_path
        self.k8s_client = K8sClient(log=self.log)
        self.ide_url_prefix = os.environ['IDE_URL_PREFIX']

        if debug:
            self.log.logger.disabled = False
            self.log.logger.setLevel(logging.DEBUG)
            self.log.logger.addHandler(logging.StreamHandler())
        

    def k8s_daemon(self, queue, **kwargs):
        queues = queue if isinstance(queue, (list, tuple)) else ([queue] if queue else None)

        return self.daemon(
            queues=[ObjectID(name=q) for q in queues] if queues else None,
            log_level=logging.INFO, foreground=True, docker=False, **kwargs,
        )

    def daemon(self, queues=None, log_level=logging.INFO, foreground=False, docker=False, detached=False, order_fairness=False, **kwargs) :
        print("Ide daemon start ... ") 
        # 启动watch线程
        t = threading.Thread(target=self.ide_watch)
        t.start()

        # 检查IDE实例是否可用
        check_thread = threading.Thread(target=self.check_available)
        check_thread.start()

        while True:
            # 从队列获取需要启动的IDE并创建实例
            try:
                response =  self._session.send_api(ides_api.GetAllRequest(status=['tostart','cc']))
                for ide in response.ides:
                    print(f'Starting ide {ide.id}')
                    try:
                        self.deploy_ide(ide)
                        self._session.send_api(ides_api.UpdateRequest(ide=ide.id, status = 'starting'))
                    except Exception as e:
                        print(f'starting ide {ide.id} fail: {e}')

                # 从队列获取需要停止的IDE并删除实例
                response =  self._session.send_api(ides_api.GetAllRequest(status=['stopping', 'deleting']))
                for ide in response.ides:
                    print(f'Stopping ide {ide.id}')
                    try:
                        self.delete_ide(ide.id)
                    except Exception as ex:
                        # print(f'delete ide k8s resources failed: {ex}')
                        pass
                    if ide.status == 'deleting':
                        self._session.send_api(ides_api.UpdateRequest(ide=ide.id, status = 'stopped'))
                        self._session.send_api(ides_api.DeleteRequest(ide=[ide.id]))
                    else:
                        self._session.send_api(ides_api.UpdateRequest(ide=ide.id, status = 'stopped'))
            except Exception as ex:
                print("Exception in daemon: ", ex)
            time.sleep(15)
        

    def ide_watch(self,namespace='tiduai'):
        # ide变为运行中状态
        def on_running(id):
            self._session.send_api(ides_api.UpdateRequest(ide=id, status = 'running'))
            print(f'running {id}')

        
        # ide变为停止状态
        def on_stop(id):
            self._session.send_api(ides_api.UpdateRequest(ide=id, status = 'stopped'))
            self.delete_ide(id)
            print(f'stopping {id}')
        
        self.k8s_client.watch_event(namespace=namespace, running_func=on_running, stopping_func=on_stop)

    # 根据模板文件生成ide的yaml
    def deploy_ide(self, ide, namespace='tiduai'):
        class Loader(yaml.loader.SafeLoader):
            yaml_implicit_resolvers = yaml.loader.SafeLoader.yaml_implicit_resolvers.copy()
            if "=" in yaml_implicit_resolvers:
                yaml_implicit_resolvers.pop("=")
        
        print('[cc] delpoying ide ', ide)
        # 创建notebook deployment
        yaml_content = yaml.load_all(self.generate_yaml(self.template_path, ide, ), Loader=Loader,)
        self.k8s_client.create_from_yaml(namespace = namespace, yaml_object=yaml_content)

        # 创建apisix route
        yaml_content = self.generate_yaml(self.route_template_path,ide, )
        self.k8s_client.create_crd_from_yaml('apisix.apache.org/v2','ApisixRoute' ,namespace,yaml.load(yaml_content, Loader=Loader))


    # 通过ID删除IDe
    def delete_ide(self, ide_id):
        ide_id = ide_id[:6]     # id取6位
        # TODO: get namespace from sdk 
        self.k8s_client.delete_deploy(name=self.DEPLOY_NAME_TEMPLATE.format(ide_id), namespace='tiduai')
        self.k8s_client.delete_svc(name=self.SVC_NAME_TEMPLATE.format(ide_id), namespace='tiduai')
        self.k8s_client.delete_crd(version='apisix.apache.org/v2',kind='ApisixRoute', name=self.NOTEBOOK_ROUTE_NAME_TEMPLATE.format(ide_id), namespace='tiduai')

    # cpu ：核数
    # mem ： Gi
    def generate_yaml(self, template_path, ide):
        self.log.info('Using Ide generate yaml : {}',ide)
        ide_id_prefix = ide.id[:6]     # id取6位
        with open(template_path,'r',encoding='utf-8') as f:
            yaml_temp = f.read()
            deploy_yaml = yaml_temp.format(
                IDE_ID_PREFIX=ide_id_prefix,
                IDE_ID = ide.id,
                IDE_NAME = ide.name, 
                CLEARML_BASEURL = ide_id_prefix,
                PASSWORD =  '', #ide_id_prefix,
                CPU_COUNT = f'{ide.cpu}',
                MEMORY_SIZE = f'{ide.mem}Mi',
                NODE_NAME = os.environ['IDE_NODE'],     # export IDE_NODE=192.168.1.49
                DEPLOY_NAME = self.DEPLOY_NAME_TEMPLATE.format(ide_id_prefix),
                SVC_NAME = self.SVC_NAME_TEMPLATE.format(ide_id_prefix),
                NOTEBOOK_ROUTE_NAME = self.NOTEBOOK_ROUTE_NAME_TEMPLATE.format(ide_id_prefix),
                ALGORITHM_NAME = str(ide.algorithms[0]),
                IDE_IMAGE = ide.image,
                )
            self.log.info('deploy yaml \n {}', deploy_yaml)  
            return deploy_yaml
    
    def check_available(self):
        #检查jupyter和vscode链接是否可用
        while True:
            response =  self._session.send_api(ides_api.GetAllRequest(status=['running']))
            for ide in response.ides:
                print(f'Check ide {ide.id}')

                id = ide.id[:6]
                url_prefix = os.environ['IDE_URL_PREFIX']       #export IDE_URL_PREFIX=http://192.168.1.42:46052
                vscode_url = f"{url_prefix}/vscode-{id}/?folder=/code/"
                print(vscode_url)

                try:
                    vscode_available = requests.get(vscode_url).status_code == 200

                    if vscode_available == 200 :
                        print('vscode ready ')
                    if vscode_available:
                        self._session.send_api(ides_api.UpdateRequest(ide=ide.id, status = 'available', url_vscode=vscode_url))
                except Exception as ex:
                    print(f'cehck url except {ex}')
            time.sleep(5)
