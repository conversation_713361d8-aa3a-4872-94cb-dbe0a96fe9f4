import unittest
import time
from clearml_agent.glue.ideagent import K8sClient, IdeIntegration
import logging

class TestIdeMethods(unittest.TestCase):
    def test_yaml(self):
        ide = IdeIntegration()
        # ide.generate_yaml('22d6830ea5824c98b8ff0608ec98dfca','/root/ide_deploy_temp.yaml')
        # ide.delpoy_ide('22d6830ea5824c98b8ff0608ec98dfca')
        # ide.delete_ide('22d6830ea5824c98b8ff0608ec98dfca')
        # ide.ide_watch()
        ide.daemon('cc-test', log_level=logging.INFO,)


if __name__ == '__main__':
    unittest.main()