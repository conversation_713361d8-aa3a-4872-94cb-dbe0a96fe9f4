import unittest
import time
from clearml_agent.glue.ideagent import K8sClient, IdeIntegration
from clearml_agent.utils.xml_format import create_xml_file
import logging

class TestS3Methods(unittest.TestCase):
    def test_s3(self):
        args = {'param1': 'value1', 'param2': 'value2'}
        config = {'agent.s3.access_key': 'juicefs', 'agent.s3.secret_key': 'tiduJuicefs123', 'agent.s3.url': 'http://192.168.1.35:8999'}
        create_xml_file(args=args, config=config,id='1234567890')

if __name__ == '__main__':
    unittest.main()