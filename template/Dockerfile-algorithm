# 用户基础镜像
ARG BASE_IMAGE

#FROM harbor.tiduyun.com/openfaas/of-watchdog:v1  AS watchdog
FROM hub.tiduyun.com:5000/clearml/arm64/fwatchdog:v2 AS watchdog
#ARG TARGETPLATFORM
#TODO: 用户镜像
FROM ${BASE_IMAGE}

# 启动指令
ARG RUN_CMD

RUN mkdir -p /home/<USER>

USER root 

COPY --from=watchdog /fwatchdog /usr/bin/fwatchdog
RUN chmod +x /usr/bin/fwatchdog

# Add non root user
# RUN addgroup -S app && adduser app -S -G app
RUN groupadd app && useradd -r -g app app || id app
RUN chown app /home/<USER>

RUN if [! -d /data]; then mkdir /data/ && chown app /data/ && chmod 777 -R /data/; fi

# TODO: 需要对路径进行说明
WORKDIR /home/<USER>/algorithm

USER root 

# Populate example here - i.e. "cat", "sha512sum" or "node index.js"
ENV fprocess $RUN_CMD
ENV mode streaming
# Set to true to see request in function logs
ENV write_debug="true"

EXPOSE 8080

HEALTHCHECK --interval=3s CMD [ -e /tmp/.lock ] || exit 1

CMD ["fwatchdog"]

