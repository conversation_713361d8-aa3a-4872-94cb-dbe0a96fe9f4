import shlex
import os
import sys
import signal
import time
import threading
from subprocess import Pope<PERSON>, PIPE
from clearml import Task
from clearml import Logger
import argparse
import re
from datetime import datetime
import xml.etree.ElementTree as ET

# eg: [metrics] accuracy x:1 y:90.1
pattern = r'\[metrics\]\s*[a-zA-Z1-9\-\_]+\s*x:(-?\d+\.?\d*)\s*y:(-?\d+\.?\d*)\s*'
reg = re.compile('^\[metrics\]\s*(?P<metric_name>[a-zA-Z1-9\-\_]+)\s*x:(?P<x>(-?\d+\.?\d*))\s*y:(?P<y>(-?\d+\.?\d*))')

def pre_hadnle():
    return

def post_handle(logger , complete_time):
    # 1、获取生成的output.xml 并解析数据
    params = Task.current_task().get_parameters()
    output_path = params.get('Args/outPath', None)
    #print(output_path) 
    for file_name in os.listdir(output_path):
            #print(f'file name {file_name}') 
            # 检查文件名是否以指定前缀开头
            #if file_name.startswith('output'):
            if file_name.startswith('output') and not os.path.isdir(os.path.join(output_path, file_name)): 
                output_file = os.path.join(output_path, file_name)
    if os.path.exists(output_file):
        with open(output_file) as f:
            xml_data = f.read()
            print(f' output file name {output_file} ')
            # 解析XML
            root = ET.fromstring(xml_data)
            # 获取creationTime
            creation_time = root.find('.//creationTime').text
            # 获取completeRate
            complete_rate = root.find('.//completeRate').text
            # 获取分数
            scheme_score = root.find('.//schemeScore').text
            # 分数变化曲线
            try:
              history_scores = root.find('.//historyScores').text
            except Exception as e:
              print('historyScores not found')

            # 打印结果
            print(f"Complete Time: {complete_time}")
            print(f"Complete Rate: {complete_rate}")
            print(f"Scheme Score:  {scheme_score}")
            print(f"History Scores:  {history_scores}")
        

            logger.report_single_value(name = 'scheme_score', value = scheme_score)
            logger.report_single_value(name = 'complete_time', value = complete_time)
            logger.report_single_value(name = 'complete_rate', value = str(float(complete_rate) * 100))  
 
            #在这画2个线性的图出来
            for i in range(0,101,10):
                c_time = round(complete_time*i*0.01, 2)
                c_rate = round(float(complete_rate)*i,2)
                logger.report_scalar(title='complete_time',iteration=i, series='complete_time', value=c_time)
                #logger.report_scalar(title='complete_rate',iteration=i ,series='complete_rate', value=c_rate)
            
            if history_scores:
                scores = history_scores.split(',')
                if len(scores) > 100 :
                    import numpy as np
                    scores_indexes = np.linspace(0,len(scores)-1, 100, dtype=int)
                    last_scores = [int(scores[i]) for i in scores_indexes]
                else :
                    last_scores = [int(score) for score in scores] 
                for i in range(0,len(last_scores)): 
                    iteration = int(i / (len(last_scores)-1) * 100)
                    logger.report_scalar(title='scheme_score',iteration=iteration ,series='scheme_score', value=int(last_scores[i]))
                    logger.report_scalar(title='complete_rate',iteration=iteration ,series='complete_rate', value= round(float(last_scores[i]/last_scores[len(last_scores)-1] * float(complete_rate)),3))
    
    return

def run_command(command, task):
    logger = Logger.current_logger()
    start_time = datetime.now() 
    process = Popen(shlex.split(command), stdout=PIPE)
    pre_hadnle()
    
    while True:
        output = process.stdout.readline().rstrip().decode('utf-8')
        if output == '' and process.poll() is not None or output == 'Exit':
            break
        if output:
            print(output.strip(),flush=True)
            if output.startswith('[complete_time]'):
                complete_time = int(output.replace('[complete_time] ',''))
            match = re.search(pattern, output)
            if match:
                regMatch = reg.match(output)
                linebits = regMatch.groupdict()
                print(linebits)
                print('report scalar ... ', output)
                logger.report_scalar(
                    title=linebits.get('metric_name'), series=linebits.get('metric_name'), value=float(linebits.get('y')), iteration=int(linebits.get('x'))
                )
            if output.startswith('CLEARML_ERROR'):
                print(f'Execute failed: {output}')
                task.mark_failed() 
                return 


    # 运行完成，根据输出xml 上报指标数据
    post_handle(logger, complete_time)
    rc = process.poll()
    return rc

if __name__ == "__main__":

    task = Task.init("cc-project", "java-project", auto_resource_monitoring=True)
    params = task.get_parameters()
    
    cmd = 'tail -f /share/output.log -n 2000'
    #print(cmd, flush=True)
    t = threading.Thread(target=run_command, args=(cmd,task))
    t.start()
