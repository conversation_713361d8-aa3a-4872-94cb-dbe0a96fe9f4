apiVersion: v1
kind: Service
metadata:
  name: {SVC_NAME}
  namespace: tiduai
  labels:
    app: notebook
    id: {IDE_ID}
spec:
  selector:
    app: notebook
    id: {IDE_ID}
  ports:
  - protocol: TCP
    port: 8018
    targetPort: 80
    name: vscode-nginx-port
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {DEPLOY_NAME}
  namespace: tiduai
  labels:
    app: notebook
    id: {IDE_ID}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: notebook
      id: {IDE_ID}
  template:
    metadata:
      labels:
        app: notebook
        id: {IDE_ID}
    spec:
      containers:
       - name: vscode-nginx
         image: hub.tiduyun.com:5000/ide/ide-nginx:latest
         env:
         - name: CLEARML_BASEURL
           value: {CLEARML_BASEURL}
       - name: clearml-vscode-vnc
         image: {IDE_IMAGE}
         command: ["/bin/bash"]
         env:
         - name: USER
           value: tidu
         - name: HTTP_PASSWORD
           value: tidu123
         - name: CLEARML_BASEURL
           value: {CLEARML_BASEURL}
         - name: PASSWORD
           value: {PASSWORD}
         args: ["-c", "code-server --host 0.0.0.0 --auth none "]
         resources:
           limits:
             cpu: {CPU_COUNT}
             memory: {MEMORY_SIZE}
           requests:
             cpu: 1
             memory: 1024Mi
         volumeMounts:
          - mountPath: /code
            name: algo-pvc
            subPath: {IDE_NAME}/ 
          - mountPath: /data/
            name: dataset-pvc
      volumes:
        - name: dataset-pvc
          persistentVolumeClaim:
            claimName: cml-dataset-pvc 
        - name: algo-pvc
          persistentVolumeClaim:
            claimName: algorithm-pvc 
      nodeName: {NODE_NAME}
