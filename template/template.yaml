apiVersion: v1
metadata:
  namespace: tiduai
  labels:
    {}
  annotations:
    {}
spec:
  shareProcessNamespace: true
  volumes:
    - emptyDir:
        medium: Memory
      name: shmvolume
    - emptyDir:
      name: logvolume
  securityContext: {}
  priorityClassName: 
  initContainers:
    []
  dnsPolicy: Default
  nodeName: ************ 
  containers:
  - resources: {}
    securityContext:
      capabilities:
        add:
        - SYS_PTRACE
    ports:
    - containerPort: 10022
    image: hub.tiduyun.com:5000/clearml/clearml-executor:v1
    volumeMounts:
      - mountPath: /dev/shm
        name: shmvolume
      - mountPath: /share
        name: logvolume
    env:
    - name: CLEARML_API_HOST
      value: http://************:28080
    - name: CLEARML_WEB_HOST
      value: http://************:28000
    - name: CLEARML_FILES_HOST
      value: http://************:8081
    - name: CLEARML_API_ACCESS_KEY
      value: T5A2OY8N081K7DRNVA51 
    - name: CLEARML_API_SECRET_KEY
      value: U4EmYnHa4IY4CXYF5bDabM4hfCejoxLckK482pRYBhyAcnUQnb 
    - name: CLEARML_AGENT_GIT_USER
      value: root
    - name: CLEARML_AGENT_GIT_PASS
      value: qwe!23456
    - name: ENV_AGENT_USE_OLD_ENV_INSTALLED # 适配通用算法（sfyj agent配置）
      value: "false"
    - name: ENV_AGENT_INSTALL_CONFIG_PYTHON
      value: "true"
    - name: CLEARML_AGENT__AGENT__RUN_NORMAL_SCRIPT
      value: "true"
    - name: CLEARML_CONFIG_FILE  # 把agent和sdk的配置区分开来
      value: "/root/clearml-agent.conf"
    - name: REPORT_START_SEC     # 资源监控起始时间
      value: "0"
    - name: REPORT_FREQUENCY_SEC # 监控频率
      value: "10"
    - name: RUN_NORMAL_SCRIPT    # 适配通用算法(sfyj)
      value: "true"
    - name: CLEARML__SDK__DEVELOPMENT__WORKER__REPORT_START_SEC
      value: "0"
    - name: CLEARML__SDK_DEVELOPMENT__WORKER__REPORTFREQUENCY_SEC
      value: "5"
  - name: executor
    image: hub.tiduyun.com:5000/clearml/clearml-executor:v1
    imagePullPolicy: IfNotPresent
    command: ["/bin/bash"]
    args: ["-c", "sleep 1000 && echo aaaa > /share/output.log && echo clearml done > /share/output.log"]
    volumeMounts:
      - mountPath: /dev/shm
        name: shmvolume
      - mountPath: /share
        name: logvolume 
