version: "3.6"
services:

  elasticsearch:
    container_name: clearml-elastic
    environment:
      bootstrap.memory_lock: "true"
      cluster.name: clearml
      cluster.routing.allocation.node_initial_primaries_recoveries: "500"
      cluster.routing.allocation.disk.watermark.low: 500mb
      cluster.routing.allocation.disk.watermark.high: 500mb
      cluster.routing.allocation.disk.watermark.flood_stage: 500mb
      discovery.type: "single-node"
      http.compression_level: "7"
      node.name: clearml
      reindex.remote.whitelist: "'*.*'"
      xpack.security.enabled: "false"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.18
    restart: unless-stopped
    volumes:
      - /home/<USER>/coding/tidu/clearml/storage/elasticsearch/data:/usr/share/elasticsearch/data
    network_mode: host

  mongo:
    container_name: clearml-mongo
    image: mongo:4.4.29
    restart: unless-stopped
    command: --setParameter internalQueryMaxBlockingSortMemoryUsageBytes=*********
    volumes:
    - /home/<USER>/coding/tidu/clearml/storage/mongo_4/db:/data/db
    - /home/<USER>/coding/tidu/clearml/storage/mongo_4/configdb:/data/configdb
    network_mode: host

  redis:
    container_name: clearml-redis
    image: redis:6.2
    restart: unless-stopped
    volumes:
    - /home/<USER>/coding/tidu/clearml/storage/redis:/data
    network_mode: host


  # clearml前端服务
  webserver:
    entrypoint:
      - /opt/clearml/wrapper.sh
      - webserver
    command:
    - webserver
    # 原版 allegroai/clearml:1.4.0
    container_name: clearml-webserver
    image: harbor.tiduyun.com/ai/clearml:1.4.0
    environment:
      NGINX_APISERVER_ADDRESS: "http://************:8008"
      NGINX_FILESERVER_ADDRESS: "http://************:8081"
      # 需要修改 /etc/nginx/clearml.conf.template在监听的端口80部分替换为环境变量${NGINX_WEB_PORT}
      # 修改启动脚本:/opt/clearml/wrapper.sh，在通过环境变量模板化配置文件中添加一个新的环境变量${NGINX_WEB_PORT}，envsubst '${NGINX_APISERVER_ADDR} ${NGINX_FILESERVER_ADDR} ${NGINX_WEB_PORT}' < /etc/nginx/clearml.conf.template
      NGINX_WEB_PORT: "8080"
    # environment:
    #  ClearML_SERVER_SUB_PATH : clear8080
    network_mode: host

  web:
    container_name: web
    image: harbor.tiduyun.com/ai/kd-a2e@sha256:973616db49d5a0360571457424bc8510edd05fc07d2f8613b0c6906411c5ff59
    environment:
      FSS_UPSTREAM: "************:8008"
      FSS_PORT: "81"
    network_mode: host

  # clearml 文件服务器
  fileserver:
    command:
    - /opt/clearml/wrapper.sh
    - fileserver
    container_name: clearml-fileserver
    # 原版 allegroai/clearml:1.4.0
    image: harbor.tiduyun.com/ai/clearml:1.4.0
    environment:
      ClearML__fileserver__delete__allow_batch: "true"
    restart: unless-stopped
    volumes:
    - /home/<USER>/coding/tidu/clearml/storage/fileserver:/mnt/fileserver
    network_mode: host

  # 异步删除服务
  async_delete:

    network_mode: host
    depends_on:
      - redis
      - mongo
      - elasticsearch
      - fileserver
    container_name: async_delete
    # 原版 allegroai/clearml:1.4.0
    image: harbor.tiduyun.com/ai/clearml:1.4.0
    restart: unless-stopped
    environment:
      ClearML_ELASTIC_SERVICE_HOST: ************
      ClearML_ELASTIC_SERVICE_PORT: 9200
      ClearML_MONGODB_SERVICE_HOST: ************
      ClearML_MONGODB_SERVICE_PORT: 27017
      ClearML_REDIS_SERVICE_HOST: ************
      ClearML_REDIS_SERVICE_PORT: 6379
      PYTHONPATH: /opt/clearml/apiserver
      ClearML__services__async_urls_delete__fileserver__url_prefixes: "[************]"
    entrypoint:
      - python3
      - -m
      - jobs.async_urls_delete
      - --fileserver-host
      - http://************:8081



#  # agent
#  agent-services:
#    network_mode: host
#    container_name: clearml-agent-services
#    image: allegroai/clearml-agent-services:latest
#    deploy:
#      restart_policy:
#        condition: on-failure
#    privileged: true
#    environment:
#      ClearML_HOST_IP: ${ClearML_HOST_IP}
#      ClearML_WEB_HOST: ${ClearML_WEB_HOST:-}
#      ClearML_API_HOST: http://apiserver:8008
#      ClearML_FILES_HOST: ${ClearML_FILES_HOST:-}
#      ClearML_API_ACCESS_KEY: ${ClearML_AGENT_ACCESS_KEY:-$ClearML_API_ACCESS_KEY}
#      ClearML_API_SECRET_KEY: ${ClearML_AGENT_SECRET_KEY:-$ClearML_API_SECRET_KEY}
#      ClearML_AGENT_GIT_USER: ${ClearML_AGENT_GIT_USER}
#      ClearML_AGENT_GIT_PASS: ${ClearML_AGENT_GIT_PASS}
#      ClearML_AGENT_UPDATE_VERSION: ${ClearML_AGENT_UPDATE_VERSION:->=0.17.0}
#      ClearML_AGENT_DEFAULT_BASE_DOCKER: "ubuntu:18.04"
#      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID:-}
#      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY:-}
#      AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION:-}
#      AZURE_STORAGE_ACCOUNT: ${AZURE_STORAGE_ACCOUNT:-}
#      AZURE_STORAGE_KEY: ${AZURE_STORAGE_KEY:-}
#      GOOGLE_APPLICATION_CREDENTIALS: ${GOOGLE_APPLICATION_CREDENTIALS:-}
#      ClearML_WORKER_ID: "clearml-services"
#      ClearML_AGENT_DOCKER_HOST_MOUNT: "/opt/clearml/agent:/root/.clearml"
#      SHUTDOWN_IF_NO_ACCESS_KEY: 1
#    volumes:
#      - /var/run/docker.sock:/var/run/docker.sock
#    entrypoint: >
#      bash -c "curl --retry 10 --retry-delay 10 --retry-connrefused 'http://apiserver:8008/debug.ping' && /usr/agent/entrypoint.sh"

