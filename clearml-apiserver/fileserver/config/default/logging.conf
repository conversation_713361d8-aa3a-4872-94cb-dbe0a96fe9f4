{
    version: 1
    disable_existing_loggers: false
    formatters: {
        default: {
            format: "[%(asctime)s] [%(process)d] [%(levelname)s] [%(name)s] %(message)s"
        }
    }
    handlers {
        console {
            formatter: default
            class: "logging.StreamHandler"
        }
        text_file: {
            formatter: default,
            backupCount: 3
            maxBytes: 10240000,
            class: "logging.handlers.RotatingFileHandler",
            filename: "/var/log/clearml/fileserver.log"
        }
    }
    root {
        handlers: [console, text_file]
        level: INFO
    }
    loggers {
        urllib3 {
            handlers: [console, text_file]
            level: WARN
            propagate: false
        }
        werkzeug {
            handlers: [console, text_file]
            level: WARN
            propagate: false
        }
    }
}
