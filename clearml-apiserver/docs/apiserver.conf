auth {
    # Fixed users login credentials
    # No other user will be able to login
    # Note: password may be bcrypt-hashed (generate using `python3 -c 'import bcrypt,base64; print(base64.b64encode(bcrypt.hashpw("password".encode(), bcrypt.gensalt())))'`)
    fixed_users {
        enabled: true
        pass_hashed: false
        users: [
            {
                username: "jane"
                password: "12345678"
                name: "<PERSON>"
            },
            {
                username: "john"
                password: "12345678"
                name: "<PERSON>"
            },
        ]
    }
}
