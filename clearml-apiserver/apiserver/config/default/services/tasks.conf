non_responsive_tasks_watchdog {
    enabled: true

    # In-progress tasks older than this value in seconds will be stopped by the watchdog
    threshold_sec: 7200

    # Watchdog will sleep for this number of seconds after each cycle
    watch_interval_sec: 900
}

multi_task_histogram_limit: 100

hyperparam_values {
    # max allowed outdate time for the cashed result
    cache_allowed_outdate_sec: 60

    # cache ttl sec
    cache_ttl_sec: 86400
}

# the maximum amount of unique last metrics/variants combinations
# for which the last values are stored in a task
max_last_metrics: 2000

# if set then call to tasks.delete/cleanup does not wait for ES events deletion
async_events_delete: true
# do not use async_delete if the deleted task has amount of events lower than this threshold
async_events_delete_threshold: 100000
