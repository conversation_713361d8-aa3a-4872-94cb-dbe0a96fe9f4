es_index_prefix: "events"

ignore_iteration {
    metrics: [":monitor:machine", ":monitor:gpu"]
}


events_retrieval {
    state_expiration_sec: 3600

    # max number of concurrent queries to ES when calculating events metrics
    # should not exceed the amount of concurrent connections set in the ES driver
    max_metrics_concurrency: 4

    # If set then max_metrics_count and max_variants_count are calculated dynamically on user data
    dynamic_metrics_count: true

    # The percentage from the ES aggs limit (10000) to use for the max_metrics and max_variants calculation
    dynamic_metrics_count_threshold: 80

    # the max amount of metrics to aggregate on
    max_metrics_count: 100

    # the max amount of variants to aggregate on
    max_variants_count: 100

    debug_images {
        # Allow to return the debug images for the variants with uninitialized valid iterations border
        allow_uninitialized_variants: true
    }

    max_raw_scalars_size: 200000

    scroll_id_key: "cTN5VEtWEC6QrHvUl0FTx9kNyO0CcCK1p57akxma"

    multi_plots_batch_size: 1000
}

# if set then plot str will be checked for the valid json on plot add
# and the result of the check is written to the db
validate_plot_str: false

# If not 0 then the plots equal or greater to the size will be stored compressed in the DB
plot_compression_threshold: 100000

# async events delete threshold
max_async_deleted_events_per_sec: 1000