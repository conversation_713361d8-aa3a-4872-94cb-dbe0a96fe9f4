# if set to true then on task delete/reset external file urls for known storage types are scheduled for async delete
# otherwise they are returned to a client for the client side delete
enabled: true
max_retries: 3
retry_timeout_sec: 60

fileserver {
    # fileserver url prefixes. Evaluated in the order of priority
    # Can be in the form <schema>://host:port/path or /path
    url_prefixes: ["https://files.community-master.hosted.allegro.ai/"]
    timeout_sec: 300
}
