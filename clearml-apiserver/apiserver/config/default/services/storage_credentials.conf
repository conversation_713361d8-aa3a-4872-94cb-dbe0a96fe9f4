aws {
    s3 {
        # S3 credentials, used for read/write access by various SDK elements
        # default, used for any bucket not specified below
        key: ""
        secret: ""
        region: ""
        use_credentials_chain: false
        # Additional ExtraArgs passed to boto3 when uploading files. Can also be set per-bucket under "credentials".
        extra_args: {}
        credentials: [
            # specifies key/secret credentials to use when handling s3 urls (read or write)
            # {
            #     bucket: "my-bucket-name"
            #     key: "my-access-key"
            #     secret: "my-secret-key"
            # },
            {
                # This will apply to all buckets in this host (unless key/value is specifically provided for a given bucket)
                host: "localhost:9000"
                key: "minioadmin"
                secret: "minioadmin"
                # region: my-server
                multipart: false
                secure: false
            }
        ]
    }
}
google.storage {
    # Default project and credentials file
    # Will be used when no bucket configuration is found
//    project: "clearml"
//    credentials_json: "/path/to/credentials.json"
//
//    # Specific credentials per bucket and sub directory
//    credentials = [
//        {
//            bucket: "my-bucket"
//            subdir: "path/in/bucket" # Not required
//            project: "clearml"
//            credentials_json: "/path/to/credentials.json"
//        },
//    ]
}
azure.storage {
    # containers: [
    #     {
    #         account_name: "clearml"
    #         account_key: "secret"
    #         # container_name:
    #     }
    # ]
}
