fileserver = "http://localhost:8081"

elastic {
  events {
    hosts: [{host: "************", port:9200}]
    args {
      timeout: 60
      dead_timeout: 10
      max_retries: 3
      retry_on_timeout: true
    }
    index_version: "1"
  }

  workers {
    hosts: [{host:"************", port:9200}]
    args {
      timeout: 60
      dead_timeout: 10
      max_retries: 3
      retry_on_timeout: true
    }
    index_version: "1"
  }
}

mongo {
  backend {
    host: "mongodb://************:27017/backend"
  }
  auth {
    host: "mongodb://************:27017/backend"
  }
}

redis {
  apiserver {
      host: "************"
      port: 6379
      db: 0
  }
  workers {
    host: "************"
    port: 6379
    db: 0
  }
}
