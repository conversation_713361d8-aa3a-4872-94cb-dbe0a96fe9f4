{
    watch: false            # Watch for changes (dev only)
    debug: true            # Debug mode
    pretty_json: false      # prettify json response
    return_stack: true      # return stack trace on error
    return_stack_to_caller: true # top-level control on whether to return stack trace in an API response

    # if 'return_stack' is true and error contains a status code, return stack trace only for these status codes
    # valid values are:
    #  - an integer number, specifying a status code
    #  - a tuple of (code, subcode or list of subcodes)
    return_stack_on_code: [
        [500, 0]  # raise on internal server error with no subcode
    ]

    listen {
        ip : "0.0.0.0"
        port: 8008
    }

    version {
        required: false
        default: 1.0
        # if set then calls to endpoints with the version
        # greater that the current max version will be rejected
        check_max_version: false
    }

    pre_populate {
        enabled: false
        zip_files: ["/path/to/export.zip"]
        fail_on_error: false
        # artifacts_path: "/mnt/fileserver"
    }

    # time in seconds to take an exclusive lock to init es and mongodb
    # not including the pre_populate
    db_init_timout: 120

    mongo {
        # controls whether FieldDoesNotExist exception will be raised for any extra attribute existing in stored data
        # but not declared in a data model
        strict: false
    }

    elastic {
        probing {
            # settings for inital probing of elastic connection
            max_retries: 4
            timeout: 30
        }
        upgrade_monitoring {
            v16_migration_verification: true
        }
    }

    auth {
        # verify user tokens
        verify_user_tokens: false
        # TODO: fix later
        # fake token for test
        # fake_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdXRoX3R5cGUiOiJCZWFyZXIiLCJlbnYiOiI8dW5rbm93bj4iLCJleHAiOjE3MzIzMjQ0MTMsImlhdCI6MTcyOTczMjQxMywiaWRlbnRpdHkiOnsiY29tcGFueSI6ImQxYmQ5MmEzYjAzOTQwMGNiYWZjNjBhN2E1YjFlNTJiIiwidXNlciI6ImVmOTc5NjE4ODk0YTRmMjZiNjZjZDNiZmExNDc5MjBmIiwicm9sZSI6InVzZXIiLCJjb21wYW55X25hbWUiOiJjbGVhcm1sIiwidXNlcl9uYW1lIjoiY2MifSwiYXBpX3ZlcnNpb24iOiIyLjI4Iiwic2VydmVyX3ZlcnNpb24iOiIxLjE0LjAiLCJzZXJ2ZXJfYnVpbGQiOiIiLCJmZWF0dXJlX3NldCI6ImJhc2ljIn0.JkmcoIjcWsdSk5p1T8hEAMtnuS-NiFFaww50w2eFdKo
        # fake_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZGVudGl0eSI6eyJjb21wYW55IjoiZDFiZDkyYTNiMDM5NDAwY2JhZmM2MGE3YTViMWU1MmIiLCJ1c2VyX25hbWUiOiJjYyIsInJvbGUiOiJ1c2VyIiwiY29tcGFueV9uYW1lIjoiY2xlYXJtbCIsInVzZXIiOiJlZjk3OTYxODg5NGE0ZjI2YjY2Y2QzYmZhMTQ3OTIwZiJ9LCJhdXRoX3R5cGUiOiJCZWFyZXIiLCJlbnYiOiI8dW5rbm93bj4iLCJleHAiOjE3NjM4Njc0NTAsImlhdCI6MTczMjMzMTQ1MCwiYXBpX3ZlcnNpb24iOiIyLjI4Iiwic2VydmVyX3ZlcnNpb24iOiIxLjE0LjAiLCJzZXJ2ZXJfYnVpbGQiOiIiLCJmZWF0dXJlX3NldCI6ImJhc2ljIn0.jHj-uxU-ygMyIJCtmNPoMx9D562mrZ5yY3OIbaf0IE4
        fake_token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZGVudGl0eSI6eyJ1c2VyIjoiX19yb290X18iLCJ1c2VyX25hbWUiOiJyb290IiwiY29tcGFueSI6ImQxYmQ5MmEzYjAzOTQwMGNiYWZjNjBhN2E1YjFlNTJiIiwiY29tcGFueV9uYW1lIjoiY2xlYXJtbCIsInJvbGUiOiJyb290In0sImVudiI6Ijx1bmtub3duPiIsImlhdCI6MTc1MDIzNjg4MywiZXhwIjoxNzgxNzcyODgzLCJhdXRoX3R5cGUiOiJCZWFyZXIiLCJhcGlfdmVyc2lvbiI6IjIuMjgiLCJzZXJ2ZXJfdmVyc2lvbiI6IjEuMTQuMCIsInNlcnZlcl9idWlsZCI6IiIsImZlYXR1cmVfc2V0IjoiYmFzaWMifQ.ZrNbjdw163FS7QCLnlb8qmhZAKCQb3coeWvaJ1ur1eM
        # max token expiration timeout in seconds (1 year)
        max_expiration_sec: 31536000

        # default token expiration timeout in seconds (30 days)
        default_expiration_sec: 31536000

        # cookie containing auth token, for requests arriving from a web-browser
        session_auth_cookie_name: "clearml_token_basic"

        # cookie configuration for authorization cookies generated by auth.login
        cookies {
            httponly: true  # allow only http to access the cookies (no JS etc)
            secure: false   # not using HTTPS
            domain: null    # Limit to localhost is not supported
            samesite: Lax
            max_age: 99999999999
        }

        auth_module {
            # 登录验证类型 local sso token jwt
            local: {
                module: "apiserver.bll.bll.ml_auth.authentication.local.local_login_bll.LocalLoginBLL"
                defaultBackend: None
                backendModule: {}
            }
        }

        # 默认的认证类型 sso/cas local
        default_auth_type: "local"

        login {
            # 是否保存登录, 默认为 all不保持登录
            clear_all_session: "all"

            # 图片验证码失败次数限制
            login_fail_limit_for_captcha_image: 3

            # 登录失败次数限制
            login_fail_limit: 5
             # 登录失败账号冻结的时间
            login_fail_freeze_seconds: 600

            #
            image_captcha_age: 60
            captcha_2fa_age: 180
            verify_code_path: "/tmp/ai/verify_code/"
            # 验证码存储方式local、s3
            verify_code_save_type: "local"

            session_cookie_age: 43140
            # 是否允许账号多地同时登录
            allow_user_login_more: false
            # 是否开启双因子认证
            enable_2fa: false

        }

        email {
            smtp_server: "smtp.163.com"
            smtp_port: 0
            smtp_username: ""
            smtp_password: ""
            smtp_ssl: 1
        }

        #IAM配置
        iam {
            iam_url: "http://10.58.3.45:8760"
            iam_http_timeout: 30
            iam_type: "td_iam"
            app_code: "7c755e27c2b04e7d883eb7846e6734df"
        }
        # SSO配置
        sso {
            sso_url: "http://10.58.3.45:8760"
            sso_http_timeout: 30
            sso_ticket_name: "ticket"
            # default sso token jwt
            sso_ticket_position: "query"
        }
        token {
            # token验证路径
            token_validate_url: "http://gateway.dataapp.com/output/dispatcher"
            # token验证方法
            token_validate_method: "POST"
            # token参数位置
            token_validate_param_position: "body"
            # token参数名称
            token_validate_param_token_name: "admin_token"
            # token参数类型
            token_validate_param_type: "dict"
            # 服务ID, 用户提供
            token_validate_service_id: "2658868dc3094520812b1cb6a566d334"
            # token验证返回码
            token_validate_result_code: 200
            # token验证用户服务返回码
            token_validate_user_info_code: 0
            # token验证用户服务数据名
            token_validate_user_info_key: "user"
            # token验证用户服务数据是否嵌套
            token_validate_user_info_level: "true"
            # token验证用户类型位置
            token_validate_user_type_position: "outer"

            # bigdata服务ID, 用户提供
            token_validate_model_code: "567"
            # token验证超时时间
            token_http_timeout: 30
            # 退出登录重定向url
            token_redirect_url: "http://www.dataapp.com/"

            # 前端传给后端 token 所使用的名称，默认放在请求头 Header 中，通过认证后会被后端存放在 cookie 中返回
            token_name: "Steamer-Token"
            # 前端传给后端 token 位置 header cookie body
            token_position: "header"
        }

        jwt: {
           jwt_secret: "abcde"
           jwt_name: "token"
           jwt_expire_time: 600
           # header cookie body
           jwt_position: "cookie"
        }

        auth {
            # 登录验证类型, 第一次传递在auth/session接口的header中, 后端保存到cookie返回, 退出登录清除
            auth_type: "Auth-Type"
            # 用户登录状态信息, 退出登录清除
            auth_status: "AuthST"
        }

        third {
            # 第三方登录默认密码
            third_default_user_password: "abc1234"
            # 第三方登录默认租户ID
            third_default_tenant_id: 6
            # 第三方登录默认租户角色ID
            third_default_tenant_role_id: 12
            # 第三方登录默认管理员角色ID
            third_default_admin_role_id: 6

            cubestudio {
                url = "http://192.168.0.5:18080"
                user = "admin"
                password = "admin"
                timeout = 60
            }
        }
        
        password {
            default: "123@Pwd"
        }
        # provide a cookie domain override per company
#        cookies_domain_override {
#            <company-id>: <domain>
#        }

#        # A list of fixed users
#        # Note: password may be bcrypt-hashed (generate using `python -c 'import bcrypt; print(bcrypt.hashpw("password", bcrypt.gensalt()))'`)
#        fixed_users {
#            enabled: true
#            pass_hashed: false
#            users: [
#                {
#                    username: "john"
#                    password: "123456"
#                    name: "john doe"
#                }
#
#            ]
#        }
    }

    cors {
        origins: "*"

        # Not supported when origins is "*"
        supports_credentials: true
    }

    default_company: "d1bd92a3b039400cbafc60a7a5b1e52b"

    workers {
        # Auto-register unknown workers on status reports and other calls
        auto_register: true
        # Assume unknow workers have unregistered (i.e. do not raise unregistered error)
        auto_unregister: true
        # Timeout in seconds on task status update. If exceeded
        # then task can be stopped without communicating to the worker
        task_update_timeout: 600

        # Timeout in seconds for worker registration (or status report). If a worker did not report for this long,
        # it is discarded from the server's table
        default_timeout: 600
    }

    check_for_updates {
        enabled: true

        # Check for updates every 24 hours
        check_interval_sec: 86400

        url: "https://updates.clear.ml/updates"

        component_name: "clearml-server"

        # GET request timeout
        request_timeout_sec: 3.0
    }

    statistics {
        # Note: statistics are sent ONLY if the user has actively opted-in
        supported: true

        url: "https://updates.clear.ml/stats"

        report_interval_hours: 24
        agent_relevant_threshold_days: 30

        max_retries: 5
        max_backoff_sec: 5
    }

    getting_started_info {
        "agentName": "clearml",
        "configure": "clearml-init",
        "install": "pip install clearml",
        "packageName": "clearml"
    }
    
    git {
        address: "http://************:9980"
        private_token: "**************************"
	pull_url: "****************************************/"
    }

    harbor {
        url: "https://************:5000"
        project: "ai"
        username: "admin"
        password: "Harbor12345"
    }

    s3 {
		storage_type = "minio",
        access_key = "juicefs",
		region = "none",
        secret_key = "tiduJuicefs123",
        url = "http://************:8999",
        bucket = "juice",
        pvc = "tiduai-tiduai-pvc",
	    algorithm_pvc = "tiduai-tiduai-algorithm-pvc"
        dataset_pvc = "tiduai-cml-dataset-pvc"
    }

    train {
        max_time_consumption_step = 10      # 最大运行时间步长
        max_iteration_step = 10000           # 最大迭代次数步长
        tabusize_step = 1                   # 禁忌表长度步长
        latesize_step = 1                   # 逾期表长度步长
        delugeratio_step = 0.1              # 涨水系数步长
    }
    # 相关资源
    resource{
        # cpu资源
        cpu = [
            {cores: 1, count: 10},  # 1核CPU可用数量
            {cores: 8, count: 5}
        ]
        # 内存资源
        memory = [1, 12, 24, 32]
        # gpu资源
        gpu = [
            {name: "A100", memory: 40, count: 4},  # 型号名称 / 显存GB / 可用数量
            {name: "V100", memory: 16, count: 2}
        ]
    }
    kubernetes{
        namespace = "tiduai"
    }
    dockerfile_path{
        path = "/home/<USER>/code/clearml/template"
    }
}
