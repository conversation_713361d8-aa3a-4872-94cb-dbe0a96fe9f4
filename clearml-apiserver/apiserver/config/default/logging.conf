{
    version: 1
    disable_existing_loggers: false
    formatters: {
        standard: {
            format: "[%(asctime)s] [%(process)d] [%(levelname)s] [%(name)s] %(message)s"
        }
    }
    handlers {
        console {
            formatter: standard
            class: "logging.StreamHandler"
        }
        text_file: {
            formatter: standard,
            backupCount: 3
            maxBytes: 10240000,
            class: "logging.handlers.RotatingFileHandler",
            filename: "/var/log/clearml/apiserver.log"
        }
    }
    root {
        handlers: [console, text_file]
        level: INFO
    }
    loggers {
        urllib3 {
            handlers: [console, text_file]
            level: WARN
            propagate: false
        }
        werkzeug {
            handlers: [console, text_file]
            level: WARN
            propagate: false
        }
        elasticsearch {
            handlers: [console, text_file]
            level: WARN
            propagate: false
        }
        # disable pep8 auto-gen logging (at least part of it)
        RefactoringTool {
            handlers: []
            level: ERROR
            propagate: false
        }
    }
}
