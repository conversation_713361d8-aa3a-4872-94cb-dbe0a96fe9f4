import unittest
from apiserver.utilities.git import get_projects
from apiserver.utilities.harbor import HarborCli
from apiserver.database.model.project import Project
from apiserver.config_repo import config
from argparse import ArgumentParser

from flask import Flask

from apiserver.config_repo import config
from apiserver.server_init.app_sequence import AppSequence
from apiserver.server_init.request_handlers import RequestHandlers
from datetime import timedelta
import datetime

app = Flask(__name__, static_url_path="/static")
AppSequence(app).start(request_handlers=RequestHandlers())



log = config.logger(__file__)
class TestMongoMethods(unittest.TestCase):
    def test_mongo(self):
        live_data_projects_pipeline = [
            {
                "$match": {
                    "configs.train_data.history_data.scene": {
                        "$elemMatch": {"scenarioCode": "CJ_941069"} 
                    }
                }
            },
            {"$project":{"_id":1, "started":1, "duration":1}}
        ]
        projects = []
        for project in Project.aggregate(live_data_projects_pipeline):
            print(project.get("started") + timedelta(hours=2400))
            print(datetime.datetime.now())
            if project.get("started") + timedelta(hours=2400) > datetime.datetime.now():
                projects.append(project.get("_id"))
        print(projects)

if __name__ == '__main__':
    unittest.main()