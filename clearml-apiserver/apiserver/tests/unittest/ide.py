import unittest
from apiserver.utilities.git import get_projects
from apiserver.utilities.harbor import HarborCli

class TestIdeMethods(unittest.TestCase):
    def test_ide(self):
        print(get_projects())

        harbor_cli = HarborCli()
        print(harbor_cli.get_images(project='system'))
        print(harbor_cli.get_tags('system/redis',project='system'))
        print(harbor_cli.get_tags('busybox'))

        print(harbor_cli.get_image_names(project='system'))

if __name__ == '__main__':
    unittest.main()