import unittest
import time
# from apiserver.utilities.git import 
from apiserver.utilities.faas import *

class TestIdeMethods(unittest.TestCase):
    def test_build_image(self):
        image_build("hub.tiduyun.com:5000/algorithm/cc-build:v1", "ps -ef")
        # deploy_algorithm("618074d3dd1f4b2c9a593eac37a6a130", "1.1.0", "hub.tiduyun.com:5000/algorithm/cc-build:v1", "ls /home/<USER>")
        # svc = get_algorithm_svc("618074d3dd1f4b2c9a593eac37a6a130", "1.1.0")
        # test_algorithm_svc("618074d3dd1f4b2c9a593eac37a6a130", "1.1.0", ["http://192.168.1.93:29000/cc-test/算法导入.xml","http://192.168.1.93:29000/cc-test/output.xml"], "http://192.168.1.49:19090/algo.update_metrics")
        # delete_algorithm_svc("618074d3dd1f4b2c9a593eac37a6a130", "1.1.0")

if __name__ == '__main__':
    unittest.main()