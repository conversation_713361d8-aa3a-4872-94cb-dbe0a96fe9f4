import docker
import unittest
import requests
import json
from apiserver.config_repo import config
from apiserver.services.algo import get_top_three
from apiserver.services.algo import get_algo_call_history

log = config.logger(__file__)

class TestIdeMethods(unittest.TestCase):
    def test_algo(self):
    #    print(get_top_three())
        print('gg')
    
    def test_call_history(self):
        get_algo_call_history(release_id='e5aeb6d9ff4741eab56943222e9fa5bc')

if __name__ == '__main__':
    unittest.main()