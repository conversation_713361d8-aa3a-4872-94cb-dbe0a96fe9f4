import unittest
# from apiserver.utilities.git import get_projects
# from apiserver.utilities.harbor import HarborCli
# from apiserver.bll.auth import *
# from  apiserver.service_repo.auth.identity import *
# from apiserver.service_repo.auth.payload.token import *
# from apiserver.service_repo.service_repo import *
# # import apiserver.config.info.get_version
# from apiserver.config.info import *

from apiserver.services.projects import *

class TestIdeMethods(unittest.TestCase):
    def test_project(self):

        
        print(train_histogram())

if __name__ == '__main__':
    unittest.main()