import unittest
# from apiserver.utilities.git import get_projects
# from apiserver.utilities.harbor import HarborCli
from apiserver.bll.auth import *
from  apiserver.service_repo.auth.identity import *
from apiserver.service_repo.auth.payload.token import *
from apiserver.service_repo.service_repo import *
# import apiserver.config.info.get_version
from apiserver.config.info import *

class TestIdeMethods(unittest.TestCase):
    def test_ide(self):
        # ret = AuthBLL.get_token_for_user('ef979618894a4f26b66cd3bfa147920f','d1bd92a3b039400cbafc60a7a5b1e52b',65535)
        # print(ret)

        identity = Identity(
                user='__root__',
                company='d1bd92a3b039400cbafc60a7a5b1e52b',
                role='root',
                user_name='root',
                company_name='clearml',
            )
        token = Token.create_encoded_token(
                identity=identity,
                entities=None,
                expiration_sec=31536000,
                api_version=str(ServiceRepo.max_endpoint_version()),
                server_version=str(get_version()),
                server_build=str(get_build_number()),
                feature_set="basic",
            )
        
        print(token)

if __name__ == '__main__':
    unittest.main()