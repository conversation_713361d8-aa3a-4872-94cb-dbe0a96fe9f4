import docker
import unittest
import requests
import json
from apiserver.config_repo import config
from apiserver.utilities.sys_dict import get_dict_content

log = config.logger(__file__)

class TestDictMethods(unittest.TestCase):
    def test_dict(self):
       print(get_dict_content('algorithm_types','Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6Ijg4YmQzMDg4LTFhMjctNDcyNC04NjVkLWE5ODVhNDBlMzYwNSIsInVzZXJuYW1lIjoiYWRtaW4ifQ.57TYbFUJwdOiqtzKVs5XLaiIwsIj13ljd4Nmpp33YGTzPwd-1N-pf8QJOxBzXw_wGbR7gpCSwEPxnJaRb5S0bA'))

if __name__ == '__main__':
    unittest.main()