from typing import Sequence, Optional, <PERSON>ple

import attr
from mongoengine import Q
from functools import partial
import json

from apiserver.apierrors import errors
from apiserver.apierrors.errors.bad_request import InvalidProjectId
from apiserver.apimodels.base import UpdateResponse, MakePublicRequest, IdResponse
from apiserver.apimodels.projects import (
    DeleteRequest,
    GetParamsRequest,
    ProjectTagsRequest,
    ProjectTaskParentsRequest,
    ProjectHyperparamValuesRequest,
    ProjectsGetRequest,
    MoveRequest,
    MergeRequest,
    ProjectRequest,
    ProjectModelMetadataValuesRequest,
    ProjectChildrenType,
    GetUniqueMetricsRequest,
    ProjectUserNamesRequest,
    EntityTypeEnum,
    LogEventsRequest,
    SaveProjectRequest,
)
from apiserver.bll.organization import OrgBLL, Tags
from apiserver.bll.project import ProjectBLL, ProjectQueries
from apiserver.bll.algo import AlgoBLL, AlgoReleaseBLL
from apiserver.bll.task import TaskBLL
from apiserver.bll.template import TemplateBLL
from apiserver.bll.event import EventBLL
from apiserver.bll.resourcespec import ResourceSpecBLL
from apiserver.bll.event.event_common import EventType
from apiserver.bll.project.project_bll import pipeline_tag, reports_tag
from apiserver.bll.project.project_cleanup import (
    delete_project,
    validate_project_delete,
)
from apiserver.database.errors import translate_errors_context
from apiserver.database.model import EntityVisibility
from apiserver.database.model.model import Model
from apiserver.database.model.project import Project, Status, Type, ParamCmbType
from apiserver.database.model.task.task import TaskType, Task, TaskStatus
from apiserver.database.model.template import Template
from apiserver.database.utils import (
    parse_from_call,
    get_company_or_none_constraint,
)
from apiserver.service_repo import APICall, endpoint
from apiserver.services.utils import (
    conform_tag_fields,
    conform_output_tags,
    get_tags_filter_dictionary,
    sort_tags_response,
)
from apiserver.bll.task.task_operations import (
    stop_task,
    enqueue_task)
from apiserver.bll.util import run_batch_operation

from apiserver.services.mock import Mock
from apiserver.database.model.algo import Algo, AlgoRelease, AlgoSource, PublishStatus, AlgoCategory
import datetime
from apiserver.apimodels.events import LogOrderEnum
from apiserver.utilities.s3 import *
from apiserver.utilities.harbor import harbor_cli
from apiserver.utilities.params import *
from apiserver.utilities.xml  import generate_dataset_xml, request_external_url
import xml.etree.ElementTree as ET
import os
import requests
from apiserver.config_repo import config
from copy import deepcopy
import threading
# from apiserver.utilities.faas import *
# from apiserver.utilities.sys_dict import get_algosource_by_id
import time

log = config.logger(__file__)

task_bll = TaskBLL()
event_bll = EventBLL()
org_bll = OrgBLL()
project_bll = ProjectBLL()
project_queries = ProjectQueries()

algo_bll = AlgoBLL()

PARAM_NAMES = ['maxTimeConsumption', 'maxIteration', 'algorithmName', 'lateSize']


# 正交实验
PARAM_CMB_ORTH = 1
# 网格搜索
PARAM_CMB_GRID = 2
# 随机搜索
PARAM_CMB_RAND = 3

create_fields = {
    "name": None,
    "description": None,
    "tags": list,
    "system_tags": list,
    "default_output_destination": None,
    "type": None,
    "configs": dict,
    "algo_type":None,
    "algo_category":None
}

configs_description = {
    "start": {"evolution": None},
    "train_data": {
        "history_data": {"history_data": None, "disable": None},
        "live_data": {"live_data": None, "disable": None},
        "mock_data": {"mock_data": None, "disable": None},
    },
    "train_resources": {
        "satellite": {"statistics": None, "disable": None},
        "ground_station": {"statistics": None, "disable": None},
    },
    "configuration": {
        "constraint_setting": None,
        "algo": None,
        "dx_addon": None,
        "xz_addon": None,
    },
    "evaluation_criterion": None,
    "end": {"run_error": None},
}

class s3_config:
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")
    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)
    dataset_pvc = config.get("apiserver.s3.dataset_pvc")


s3_client = s3_config.s3_client

# tag
@endpoint("projects.get_by_id")
def get_by_id(call: APICall, company: str, request: ProjectRequest):
    project_id = request.project
    with translate_errors_context():
        query = Q(id=project_id) & get_company_or_none_constraint(company)
        project = Project.objects(query).first()
        if not project:
            raise errors.bad_request.InvalidProjectId(id=project_id)

        project_dict = project.to_proper_dict()
        conform_output_tags(call, project_dict)
        started = project_dict.get("started")
        finished = project_dict.get("finished")
        configs = project_dict.get("configs")
    
        if project.type == Type.qa or project.type == Type.cmp :
            args = project_dict.get("configs", {}).get("configuration", {}).get("algo", {}).get("hyperparams", {}).get("Args", {})
            if args:
                r = [
                        {
                            "name": value.get("name",""),
                            "value": value.get("value",""),
                            "description": value.get("description",""),
                            "type": value.get("type",""),
                            "section": "Args",
                            "min": value.get("min",""),
                            "max": value.get("max",""),
                            "disabled": False if value.get("name") in ["maxTimeConsumption", "maxIteration", "lateSize", "tabuSize", "annealType", "delugeRatio"] else True
                        }
                        for key, value in args.items()
                    ]
                project_dict["configs"]["configuration"]["algo"]["hyperparams"]["Args"] = r
            else:
                project_dict["configs"]["configuration"]["algo"]["hyperparams"]["Args"] = []    
            configs_description = get_configs_description(project, configs, company, call)
        else: 
            algos = project_dict.get("configs", {}).get("configuration", {}).get("algos", {})
            configs_description = get_configs_description(project, configs, company, call)   
        if started > finished:
            run_time = datetime.datetime.now() - started
            hours, remainder = divmod(run_time.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            formatted_run_time = f"{run_time.days * 24 + hours:0>2}:{minutes:0>2}:{seconds:0>2}"
        else:
            run_time = finished - started
            hours, remainder = divmod(run_time.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            formatted_run_time = f"{run_time.days * 24 + hours:0>2}:{minutes:0>2}:{seconds:0>2}"
        project_dict["run_time"] = str(formatted_run_time)
        project_dict["configs_description"] = configs_description
        call.result.data = {"project": project_dict}


def get_configs_description(project, configs, company, call):
    configs_description["start"]["evolution"] = Mock.get_evolution(
        configs["start"]["evolution"]
    )
    configs_description["train_data"]["history_data"]["history_data"] = configs["train_data"]["history_data"]["scene"]
    
    configs_description["train_data"]["history_data"]["disable"] = configs[
        "train_data"
    ]["history_data"]["disable"]
    # configs_description["train_data"]["live_data"]["live_data"] = Mock.get_live_data(
    #     configs["train_data"]["live_data"]["id"]
    # )
    configs_description["train_data"]["live_data"]["live_data"] = [{"id":live_data.get('scenarioCode'), "name":live_data.get('scenarioName')}  for live_data in configs["train_data"]["live_data"]["scene"]]

    configs_description["train_data"]["live_data"]["disable"] = configs["train_data"][
        "live_data"
    ]["disable"]
    configs_description["train_data"]["mock_data"]["mock_data"] = configs["train_data"]["mock_data"]["scene"]
    
    configs_description["train_data"]["mock_data"]["disable"] = configs["train_data"][
        "mock_data"
    ]["disable"]

    configs_description["train_resources"]["satellite"]["disable"] = configs[
        "train_resources"
    ]["satellite"]["disable"]

    configs_description["train_resources"]["ground_station"]["disable"] = configs[
        "train_resources"
    ]["ground_station"]["disable"]
    configs_description["configuration"]["constraint_setting"] = Mock.get_constraint(
        configs["configuration"]["constraint_setting"]
    )
    if project.type == Type.qa:
        algo_id = configs.get("configuration", {}).get("algo", {}).get("algo_id","")
        if algo_id == "":
            configs_description["configuration"]["algo"] = algo_id
        else:
            algo = AlgoBLL.get_by_id(algo_id, company)
            configs_description["configuration"]["algo"] = algo.name
    else:
        algo_id = configs.get("configuration", {}).get("algo", {}).get("algo_id","")
        if algo_id == "":
            configs_description["configuration"]["algo"] = algo_id
        else:
            algo = AlgoBLL.get_by_id(algo_id, company)
            if algo:
                configs_description["configuration"]["algo"] = {
                        "algo":algo.name,
                        "version":configs['configuration']['algo']["version"],
                        # "source": get_algosource_by_id(algo.source, authorization=call.get_header('Authorization'))
                    }
        algos = configs["configuration"]["algos"]
        configs_description["configuration"]["algos"] = []   
        for algo in algos:
            algo_id = algo["algo_id"]
            args = algo["hyperparams"]["Args"]
            r = [
                    {
                        "name": value.get("name",""),
                        "value": value.get("value",""),
                        "description": value.get("description",""),
                        "type": value.get("type",""),
                        "section": "Args",
                        "min": value.get("min",""),
                        "max": value.get("max",""),
                        "disabled": False if value.get("name") in ["maxTimeConsumption", "maxIteration", "lateSize", "tabuSize", "annealType", "delugeRatio"] else True
                    }
                    for key, value in args.items()
                ]
            algo["hyperparams"]["Args"] = r
            if algo_id:
                a = AlgoBLL.get_by_id(algo_id, company) 
                if a:
                    # source = AlgoSource.get_many(company=company, parameters={"id":a.source}, query_dict={"id":a.source})
                    a = {
                        "algo_id":algo["algo_id"],
                        "algo":a.name,
                        "version":algo["version"],
                        # "source": get_algosource_by_id(a.source, authorization=call.get_header('Authorization'))
                    }
                    configs_description["configuration"]["algos"].append(a)     
    configs_description["configuration"]["dx_addon"] = Mock.get_addon(
        configs["configuration"]["dx_addon"]
    )
    configs_description["configuration"]["xz_addon"] = Mock.get_addon(
        configs["configuration"]["xz_addon"]
    )
    evaluation_criterion = deepcopy(configs["evaluation_criterion"])
    configs_description["evaluation_criterion"] = Mock.get_evaluation(evaluation_criterion)
    configs_description["end"]["run_error"] = Mock.get_run_error(
        configs["end"]["run_error"]
    )
    return configs_description


def _hidden_query(search_hidden: bool, ids: Sequence) -> Q:
    """
    1. Add only non-hidden tasks search condition (unless specifically specified differently)
    """
    if search_hidden or ids:
        return Q()

    return Q(system_tags__ne=EntityVisibility.hidden.value)


def _adjust_search_parameters(data: dict, shallow_search: bool):
    """
    1. Make sure that there is no external query on path
    2. If not shallow_search and parent is provided then parent can be at any place in path
    3. If shallow_search and no parent provided then use a top level parent
    """
    data.pop("path", None)
    if not shallow_search:
        if "parent" in data:
            data["path"] = data.pop("parent")
        return

    if "parent" not in data:
        data["parent"] = [None]


def _get_project_stats_filter(
    request: ProjectsGetRequest,
) -> Tuple[Optional[dict], bool]:
    if request.include_stats_filter or not request.children_type:
        return request.include_stats_filter, request.search_hidden

    if request.children_tags_filter:
        stats_filter = {"tags": request.children_tags_filter}
    elif request.children_tags:
        stats_filter = {"tags": request.children_tags}
    else:
        stats_filter = {}

    if request.children_type == ProjectChildrenType.pipeline:
        return (
            {
                **stats_filter,
                "system_tags": [pipeline_tag],
                "type": [TaskType.controller],
            },
            True,
        )
    if request.children_type == ProjectChildrenType.report:
        return (
            {**stats_filter, "system_tags": [reports_tag], "type": [TaskType.report]},
            True,
        )
    return stats_filter, request.search_hidden


@endpoint("projects.get_all_ex")
def get_all_ex(call: APICall, company_id: str, request: ProjectsGetRequest):
    data = call.data
    conform_tag_fields(call, data)
    allow_public = (
        data["allow_public"]
        if "allow_public" in data
        else not data["non_public"] if "non_public" in data else request.allow_public
    )

    requested_ids = data.get("id")
    if isinstance(requested_ids, str):
        requested_ids = [requested_ids]

    _adjust_search_parameters(
        data,
        shallow_search=request.shallow_search,
    )
    selected_project_ids = None
    if request.active_users or request.children_type:
        ids, selected_project_ids = project_bll.get_projects_with_selected_children(
            company=company_id,
            users=request.active_users,
            project_ids=requested_ids,
            allow_public=allow_public,
            children_type=request.children_type,
            children_tags=request.children_tags,
            children_tags_filter=request.children_tags_filter,
        )
        if not ids:
            return {"projects": []}
        data["id"] = ids

    ret_params = {}

    remove_system_tags = False
    if request.search_hidden:
        only_fields = data.get("only_fields")
        if isinstance(only_fields, list) and "system_tags" not in only_fields:
            only_fields.append("system_tags")
            remove_system_tags = True

    projects: Sequence[dict] = Project.get_many_with_join(
        company=company_id,
        query_dict=data,
        query=_hidden_query(search_hidden=request.search_hidden, ids=requested_ids),
        allow_public=allow_public,
        ret_params=ret_params,
    )
    if not projects:
        return {"projects": projects, **ret_params}

    if request.search_hidden:
        for p in projects:
            system_tags = (
                p.pop("system_tags", [])
                if remove_system_tags
                else p.get("system_tags", [])
            )
            if EntityVisibility.hidden.value in system_tags:
                p["hidden"] = True

    conform_output_tags(call, projects)
    project_ids = list({project["id"] for project in projects})

    stats_filter, stats_search_hidden = _get_project_stats_filter(request)
    if request.check_own_contents:
        if request.children_type == ProjectChildrenType.dataset:
            contents = project_bll.calc_own_datasets(
                company=company_id,
                project_ids=project_ids,
                filter_=stats_filter,
                users=request.active_users,
            )
        else:
            contents = project_bll.calc_own_contents(
                company=company_id,
                project_ids=project_ids,
                filter_=stats_filter,
                specific_state=request.stats_for_state,
                users=request.active_users,
            )

        for project in projects:
            project.update(**contents.get(project["id"], {}))

    if request.include_stats:
        if request.children_type == ProjectChildrenType.dataset:
            stats, children = project_bll.get_project_dataset_stats(
                company=company_id,
                project_ids=project_ids,
                include_children=request.stats_with_children,
                filter_=stats_filter,
                users=request.active_users,
                selected_project_ids=selected_project_ids,
            )
        else:
            stats, children = project_bll.get_project_stats(
                company=company_id,
                project_ids=project_ids,
                specific_state=request.stats_for_state,
                include_children=request.stats_with_children,
                search_hidden=stats_search_hidden,
                filter_=stats_filter,
                users=request.active_users,
                selected_project_ids=selected_project_ids,
            )

        for project in projects:
            project["stats"] = stats[project["id"]]
            project["sub_projects"] = children[project["id"]]

    if request.include_dataset_stats:
        dataset_stats = project_bll.get_dataset_stats(
            company=company_id,
            project_ids=project_ids,
            users=request.active_users,
        )
        for project in projects:
            project["dataset_stats"] = dataset_stats.get(project["id"])

    call.result.data = {"projects": projects, **ret_params}


@endpoint("projects.get_all")
def get_all(call: APICall, company: str, _):
    call.data["order_by"]=["-created"]
    call.data["only_fields"] = ['created', 'description', 'created', 'finished', 'id', 'last_update', 'name', 'started', 'status', 'type']
    data = call.data
    for field in ["status", "type"]:
        if data.get(field) == "":
            data.pop(field, None)
    conform_tag_fields(call, data)
    _adjust_search_parameters(
        data,
        shallow_search=data.get("shallow_search", False),
    )
    ret_params = {}
    projects = Project.get_many(
        company=company,
        query_dict=data,
        query=_hidden_query(
            search_hidden=data.get("search_hidden"), ids=data.get("id")
        ),
        parameters=data,
        allow_public=True,
        ret_params=ret_params,
    )
    for project in projects:
        started = project.get("started", datetime.date(2024, 10, 9))
        finished = project.get("finished", datetime.date(2024, 10, 9))
        if started > finished:
            run_time = datetime.datetime.now() - started
            hours, remainder = divmod(run_time.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            formatted_run_time = f"{run_time.days * 24 + hours:0>2}:{minutes:0>2}:{seconds:0>2}"
        else:
            run_time = finished - started
            hours, remainder = divmod(run_time.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            formatted_run_time = f"{run_time.days * 24 + hours:0>2}:{minutes:0>2}:{seconds:0>2}"  
        project["run_time"] = str(formatted_run_time)
    conform_output_tags(call, projects)
    count = Project.get_count(
        company=company,
        query_dict=call.data,
    )
    page = call.data.get("page", 0)
    page_size = call.data.get("page_size", 0)
    pager = {"page": page, "page_size": page_size, "count": count}
    call.result.data = {"projects": projects, **ret_params, "pager": pager}

# tag
@endpoint(
    "projects.create",
    required_fields=["name"],
    response_data_model=IdResponse,
)
def create(call: APICall, company: str, _):
    name = call.data.get("name")
    count = Project.get_count(company=company, query=Q(name=name))
    if count:
        raise errors.bad_request.HasSameProjectName()
    # 模板处理
    template_id = call.data.get("template", "")
    configs = {}
    if template_id:
        template = Template.get_for_writing(company=company, id=template_id)
        template_dict = template.to_proper_dict()
        configs = template_dict.get("configs", {})

    identity = call.identity
    algo_id = call.data.get('base_algo', None)
    algo_version_id = call.data.get('base_algo_version', None)
    algo_version = None
    algo_category = call.data.get('algo_category', "")
    if algo_category:
        # category_res = AlgoCategory.get_many(
        #         company=company, parameters={"id":algo_category}, query_dict={"id":algo_category}
        #     )
        call.data['type'] = algo_category
    data = {
        "algo":algo_id,
        "id":algo_version_id
    }
    Args = {}
    if algo_id and algo_version_id :
        res = AlgoRelease.get_many(
            company=company, parameters=data, query_dict=data
        )
        args = res[0].get("args") 
        algo_version  = res[0].get("version")
        for arg in args:
            arg["type"] = ""
            arg["min"] = ""
            arg["max"] = ""
            arg["section"] = "Args"
            Args[arg.get("name")] = arg
    with translate_errors_context():
        fields = parse_from_call(call.data, create_fields, Project.get_fields())
        conform_tag_fields(call, fields, validate=True)

        return IdResponse(
            id=ProjectBLL.create(
                user=identity.user,
                company=company,
                algo_id= algo_id,
                algo_version= algo_version,
                Args = Args,
                configs = configs,
                **fields,
            )
        )

@endpoint("projects.start")
def start(call: APICall, company:str, request:ProjectRequest):
    # TODO: 1、获取所有task 2、如果task已有数据，则进行reset 3、启动所有task
    
    project = Project.objects(company=company, id=request.project).first()
    task_list  = Task.get_many(company=company,parameters = {"project":request.project}, query_dict={"project":request.project})
    for t in task_list:
        task_bll.delete(t.get("id"))
    if not project:
        log.info('Project not found...')
        raise errors.bad_request.InvalidProjectId(id=request.project)
    if not project.configs.train_data.history_data.scene and not project.configs.train_data.mock_data.scene and not project.configs.train_data.live_data.scene:
        log.info('datasets for project  none ...')
        # TODO: 改一下返回错误
        raise errors.bad_request.MissDataset()
    
    history_data = project.configs.train_data.history_data.scene
    if project.configs.train_data.history_data.disable:
        history_data_ids = []
    else:
        history_data_ids = [{"type": "history_data", "name": "{}".format(i["scenarioName"]), "id": "{}".format(i["scenarioCode"])} for i in history_data]

    live_data = project.configs.train_data.live_data.scene
    if project.configs.train_data.live_data.disable:
        live_data_ids = []
    else:
        live_data_ids = [{"type": "live_data", "name": "{}".format(i["scenarioName"]), "id": "{}".format(i["scenarioCode"])} for i in live_data]

    mock_data = project.configs.train_data.mock_data.scene
    if not project.configs.train_data.mock_data.create_dataset_finished:
        if mock_data:
            raise errors.bad_request.MockDataNotReady()
    if project.configs.train_data.mock_data.disable:
        mock_data_ids = []
    else:
        mock_data_ids = [{"type": "mock_data", "name": "{}".format(i.dataset_name), "id": "{}".format(i.dataset_id)} for i in mock_data]
    
    datasets = history_data_ids + mock_data_ids + live_data_ids

    if len(datasets) == 0:
        log.warn(f'Start train failed cuz miss dataset. project {project.name}')
        raise errors.bad_request.MissDataset()

    # 校验算法是否为空
    algo = project.configs.configuration.algo
    algos = project.configs.configuration.algos
    log.info(f'===================={algo}  {not algo.algo_id}  {not algo.version} {not algos}')
    if (not algo.algo_id  or not algo.version)  and not algos:
        raise errors.bad_request.MissAlgo()

    # 更新为待启动状态
    fields = {'status':Status.pending, 'started':datetime.datetime.now()}
    ProjectBLL.update(company, request.project, **fields)
    
    thread = threading.Thread(target=generate_task_param, args=(call, company, datasets, project))
    thread.start()
    call.result.data = {"status": "succeed"}    


# TODO:优化入参，不要传call、request这种参数
def generate_task_param(call, company, datasets, project):
    try:

        if project.type == Type.normal:
            algos = project.configs.configuration.algos
            if not algos:
                log.warn(f'could not find algos for project {project.id}, configuration: {project.configs.configuration}')
                fields = {'status':Status.failed}
                ProjectBLL.update(company, project.id, **fields)
                return

            for algo in algos:
                query_dict={"algo":algo.algo_id, "version":algo.version}
                algo_release =  AlgoRelease.get_many(company=company, query_dict=query_dict)[0]
                tasks = _create_tasks(project, algo_release, algo, company=company, user=call.identity.user, datasets=datasets,)
                log.warn('Tasks {}'.format(tasks))
                run_batch_operation(
                    func=partial(
                        enqueue_task,
                        company_id=company,
                        identity=call.identity,
                        queue_id="5c1ae5d03b7e4448aa922c7d0151e06c", 
                        status_message='',
                        status_reason=''
                    ),
                    ids = [task.id for task in tasks]
                )
            fields = {'status':Status.running, 'started':datetime.datetime.now()}
            ProjectBLL.update(company, project.id, **fields) 
            return
        elif project.type == Type.cmp:
            a = project.configs.configuration.algo
            algos = project.configs.configuration.algos
            algos.append(a)
            for algo in algos:
                query_dict={"algo":algo.algo_id, "version":algo.version}
                algo_release =  AlgoRelease.get_many(company=company, query_dict=query_dict)[0]
                tasks = _create_cmp_tasks(project, algo_release, algo, company=company, user=call.identity.user, datasets=datasets, )
                run_batch_operation(
                    func=partial(
                        enqueue_task,
                        company_id=company,
                        identity=call.identity,
                        queue_id="5c1ae5d03b7e4448aa922c7d0151e06c", 
                        status_message='',
                        status_reason=''
                    ),
                    ids = [task.id for task in tasks]
                )
            fields = {'status':Status.running, 'started':datetime.datetime.now()}
            ProjectBLL.update(company, project.id, **fields)  
            return
        # 1、拿到算法信息
        algo = project.configs.configuration.algo
        if not algo:
            log.warn(f'could not find algo for project {project.id}, configuration: {project.configs.configuration}')
            fields = {'status':Status.failed}
            ProjectBLL.update(company, project.id, **fields)
        query_dict={"algo":algo.algo_id, "version":algo.version}
        algo_release =  AlgoRelease.get_many(company=company, query_dict=query_dict)[0]

        # 2、创建task，并放入队列
        tasks = _create_tasks(project, algo_release, algo,  company=company, user=call.identity.user, datasets=datasets, )

        run_batch_operation(
            func=partial(
                enqueue_task,
                company_id=company,
                identity=call.identity,
                queue_id="5c1ae5d03b7e4448aa922c7d0151e06c", #  TODO:走配置的形式
                status_message='',
                status_reason=''
            ),
            ids = [task.id for task in tasks]
        )

        # 状态更新
        fields = {'status':Status.running, 'started':datetime.datetime.now()}
        ProjectBLL.update(company, project.id, **fields)
        return
    except Exception as e:
        log.warn(f'generate xml exception {e}')
        fields = {'status':Status.failed, 'finished':datetime.datetime.now()}
        ProjectBLL.update(company, project.id, **fields)

def _create_tasks(project, algo_release, algo, company, user, datasets=None, ):
    from copy import deepcopy
    # 获取project原始的参数值列表
    def get_project_param_values(project, keys :[], algo_id, algo_version) -> []:
        algos = []
        algos = deepcopy(project.configs.configuration.algos)
        algos.append(project.configs.configuration.algo)
        original_algo = None
        for algo in algos:
            if hasattr(algo, "algo_id"):
                if algo.algo_id == algo_id and algo.version == algo_version:
                    original_algo = algo
        if not original_algo:
            log.warn('Algo not found while get origin value combination !')
            return []

        param_values = []
        for key in keys:
            for k,v in original_algo.hyperparams.Args.items():
                if key == k :
                    param_values.append(v.get('value'))
        log.warn('project {} origin params {} values: {}'.format(project.id, keys, values))
        return param_values

     # 拿到算法名，用作训练名
    log.info(f'company {company} \t id {algo_release.get("algo")}')
    algo_name = Algo.get(company=company,id=algo_release.get('algo')).name + '-V' + algo_release.get('version')

    image_env = algo_release.get('exec_config').get('env')
    image = harbor_cli.get_harbor_domain()+'/'+harbor_cli.get_harbor_project()+'/'+image_env.get('image')+':'+image_env.get('version')
    if project.type == Type.qa:
        hparams = project.configs.configuration.algo.hyperparams.Args
    else:
        hparams = algo.hyperparams.Args
    # 笛卡尔乘积，获得参数组合 迭代次数和最大运行时间先使用配置设置步长
    keys = []
    values = []
    for key,value in hparams.items():
        keys.append(key)

        range_values = []
        if (value.get('min') and value.get('max')):
            if key == 'maxTimeConsumption':
                scan = config.get("apiserver.train.max_time_consumption_step")
            elif key == 'maxIteration':
                scan = config.get("apiserver.train.max_iteration_step")
            else:
                scan = 1
            for v in range(int(value['min']) , int(value['max']) , scan):
                range_values.append(str(v))

            # TODO: 这里可以改成使用字典自行配置步长、枚举取值等形式
             
            values.append(range_values)
        else:
            values.append([value.get('value', 0)])

    if project.configs.start.evolution == PARAM_CMB_ORTH:
        log.info('ORTH PARAMS')
        param_combs = get_orthogonal_params(values)
    elif project.configs.start.evolution ==  PARAM_CMB_RAND:
        log.info('RANDOM PARAMS')
        param_combs = get_random_params(values,10)
    else:
        log.info('GRID PARAMS')
        param_combs = get_grid_params(values)
    
    # 检查原始参数值组合是否已经存在了，如果不存在，则插入
    origin_param_values = get_project_param_values(project, keys, algo_release['algo'], algo_release['version'])
    if origin_param_values not in  param_combs:
        param_combs.append(origin_param_values)
    log.info(f'param combs after check {param_combs}')

    tasks = []
    resource_id = project.configs.start.resource
    resource = ResourceSpecBLL.get_by_id(resource_id, company)
    cpu = resource.cpu if resource else '1'
    mem = resource.mem if resource else '8192'
    task_resource = {
        "cpu":cpu,
        "mem":mem
    }

    log.info(f'project {project.name} datasets {datasets}')
    for dataset in datasets:
        for param_comb in param_combs:
            # TODO:对于参数需要进行类型判断
            log.warn(f'==================   param comb   {param_comb}')
            new_param_comb = [str(x) if isinstance(x, (int, float)) else x for x in list(param_comb)]
            if new_param_comb == origin_param_values:
                original = True
            else:
                original = False 

            body = {"scenarioCodeList":[dataset.get('id')]}
            # SS数据，带上原始xml路径
            if dataset.get('inputPath',None):
                body['inputPath'] = dataset.get('inputPath')
            for name in PARAM_NAMES:
                if name in keys:
                    index = keys.index(name)
                    body[name] = param_comb[index]

            res = generate_dataset_xml(body)
            log.info(f'----------------generate xml body {body}')
            if res.get('code') != 200 :
                log.info(f'---------generate xml for {dataset.get("id")} failed : {res}')
                raise errors.bad_request.XmlGenerateFailed(extra_msg=dataset.get("id"))  
            res_data = res.get("data", {})
            if res_data:
                for res in res_data:
                    input_path = res.get("inputPath").replace('xml/', '')
                    output_path =res.get("outPath").replace('xml/', '')

            log.info('-----------------------[generate xml]--------------------------')
            log.info(f'[keys]\t{keys}')
            log.info(f'[values]\t{param_comb}')

            # project描述：所有参数组合
            description = ''
            for i in range(0, len(keys)):
                description += f'{keys[i]} : {param_comb[i]}'

            task_template = {
                "company": company,
                "user": user,
                "name": algo_name,
                "dataset_type": dataset["type"],
                "dataset_name": dataset["name"],
                "dataset_id": dataset["id"],
                "dataset_path":{
                    "inputPath":input_path,
                    "outPath":output_path,
                },
                "type": "training",
                "comment":description,
                "project": project.id,
                "tags": [
                    "auto-generated"
                ],
                "script": {},
                "container" : {
                    "image" : image,
                    "arguments" : "",
                    "setup_shell_script" : ""
                    },
                "hyperparams": {
                    "Args": {
                        "cmd": {
                            "section": "Args",
                            "name": "cmd",
                            "value": "while true; do echo aaaaa && sleep 1; done ",
                            "type": "",
                            "description": ""
                        },
                        "inputPath": {
                            "section": "Args",
                            "name": "inputPath",
                            "value": input_path,
                            "type": "",
                            "description": ""
                        },
                        "outPath": {
                            "section": "Args",
                            "name": "outPath",
                            "value":output_path,
                            "type": "",
                            "description": ""
                        },
                    }
                },
                "algorithm": {
                    "id": algo_release.get('algo'),
                    "version": algo_release.get('version')
                },
                "enqueue_status": None,
                "original": original,
                "resource": json.dumps(task_resource),
                "generation":dataset.get('generation', 0)
            }

            task_template["hyperparams"]["Args"]["cmd"] = {"name":"cmd","value":algo_release.get('exec_config').get('cmd'), "section":"Arg"}
            for index in  range(len(param_comb)):
                key = keys[index]
                value = param_comb[index]
                print(f'{key}:{value}')
                task_template["hyperparams"]["Args"][key] = {"name":key, "value":str(value), "section":"Params"}

            from apiserver.services.tasks import create_internal as create_task
            task_call = APICall(endpoint_name='tasks.create')
            task_call.data = task_template
            task = create_task(task_call, company, user)
            tasks.append(task)
    return tasks


def _create_cmp_tasks(project, algo_release, algo, company, user, datasets=None):
    algo_name = Algo.get(company=company,id=algo_release.get('algo')).name + '-V' + algo_release.get('version')
    maxTimeConsumption = ''
    maxIteration = ''
    algorithmName = ''
    tasks = []
    release_args = algo_release['args']
    for dataset in datasets:
        body = {"scenarioCodeList":[dataset.get('id')]}
        # TODO： 在这里获取算法的参数配置
        for arg in release_args:
            if arg.get('name') and arg.get('value'):
                body[arg.get('name')] = arg.get('value')
        
        res = generate_dataset_xml(body)
        log.info(f'----------------generate xml request {body} response {res}')
        if res.get('code') != 200 :
                log.info(f'---------generate xml for {dataset.get("id")} failed --------------')
                raise errors.bad_request.XmlGenerateFailed(extra_msg=dataset.get("id"))
        res_data = res.get("data", {})
        if res_data:
            for res in res_data:
                input_path = res.get("inputPath").replace('xml/', '')
                output_path =res.get("outPath").replace('xml/', '')        
        image_env = algo_release.get('exec_config').get('env')
        image = harbor_cli.get_harbor_domain()+'/'+harbor_cli.get_harbor_project()+'/'+image_env.get('image')+':'+image_env.get('version')
        resource_id = project.configs.start.resource
        resource = ResourceSpecBLL.get_by_id(resource_id, company)
        cpu = resource.cpu if resource else '1'
        mem = resource.mem if resource else '8192'
        task_resource = {
            "cpu":cpu,
            "mem":mem
        }
        task_template = {
                "company": company,
                "user": user,
                "name": algo_name,
                "dataset_type": dataset["type"],
                "dataset_name": dataset["name"],
                "dataset_id": dataset["id"],
                "dataset_path":{
                    "inputPath":input_path,
                    "outPath":output_path
                },
                "type": "training",
                "comment": f"算法对比任务,任务名称:{project.name} 算法ID:{algo.algo_id},算法版本:{algo.version}",
                "project": project.id,
                "tags": [
                    "auto-generated"
                ],
                "script": {},
                "container" : {
                    "image" : image,
                    "arguments" : "",
                    "setup_shell_script" : ""
                    },
                "hyperparams": {
                    "Args": {
                        "cmd": {
                            "section": "Args",
                            "name": "cmd",
                            "value": "while true; do echo aaaaa && sleep 1; done ",
                            "type": "",
                            "description": ""
                        },
                        "inputPath": {
                            "section": "Args",
                            "name": "inputPath",
                            "value": input_path,
                            "type": "",
                            "description": ""
                        },
                        "outPath": {
                            "section": "Args",
                            "name": "outPath",
                            "value": output_path,
                            "type": "",
                            "description": ""
                        },
                    }
                },
                "algorithm": {
                    "id": algo_release.get('algo'),
                    "version": algo_release.get('version')
                },
                "enqueue_status": None,
                "resource": json.dumps(task_resource)
            }   
        a= {"algo":algo.algo_id,"version":algo.version}
        res = AlgoRelease.get_many(
                company=company, parameters=a, query_dict=a
            )
        params = res[0].get("args")
        # hparams = algo.hyperparams.Args
        Args = {}
        for arg in params:
            arg["type"] = ""
            arg["section"] = "Args"
            Args[arg.get("name")] = arg
        for key,value in Args.items():
            task_template["hyperparams"]["Args"]["cmd"] = {"name":"cmd","value":algo_release.get('exec_config').get('cmd'), "section":"Arg"}
            task_template["hyperparams"]["Args"][key] = {"name":key, "value":str(value.get("value")), "section":"Arg"}
        from apiserver.services.tasks import create_internal as create_task
        task_call = APICall(endpoint_name='tasks.create')
        task_call.data = task_template
        task = create_task (task_call, company, user)
        tasks.append(task)
    return tasks

@endpoint("projects.stop")
def stop(call:APICall, company: str, request:ProjectRequest):
    # TODO: 1、获取所有task 2、停止所有
    tasks = get_all_tasks(company=company, project= request.project)
    # 分批量停止
    start = 0
    batch_size=30
    while start < len(tasks):
        end = start + batch_size if (start + batch_size) < (len(tasks)-1) else len(tasks)-1
        batch_tasks = tasks[start:end]
        run_batch_operation(
            func=partial(
                stop_task,
                company_id=company,
                identity=call.identity,
                user_name=call.identity.user_name,
                status_reason='',
                force=True
            ),
            ids=[task.get('id') for task in tasks],
        )
        start += batch_size

    fields = {'status':Status.stopped, 'finished':datetime.datetime.now()}
    ProjectBLL.update(company, request.project, **fields)

    call.result.data = {'status':"succeed"}


@endpoint("projects.get_best_task")
def stop(call:APICall, company: str, _):
    best_task = get_best_task(company=company, project=call.data.get("project"), metric_name=call.data.get("metric_name"))
    call.result.data = {'task':best_task}

# TODO:加一个dict
def get_all_tasks(company: str, project: str, tags=None, dataset=None, algo_id=None, algo_version=None, extra_dict=None):
    query_dict = {'project': project}
    if extra_dict:
        query_dict.update(extra_dict)
    if dataset:
        query_dict.update({"dataset_id": dataset})
    if algo_id and algo_version:
        query_dict.update({"algorithm":{'id':algo_id, 'version':algo_version}})
    tasks = Task.get_many(company=company, query_dict=query_dict)
    log.info("project all tasks query={}, task_ids={}".format(query_dict, [i.get("id") for i in tasks]))
    for i in range(0, len(tasks)):
        algo = Algo.get(company=company, id=tasks[i].get('algorithm').get('id'))
        if algo:
            tasks[i]['algorithm']['name'] = algo.to_proper_dict().get('name')
    return tasks

# 获取表现最佳算法
def get_best_task(company, project, dataset=None, algo_id=None, algo_version=None, metric_name=None) -> str:
    if True:
        tasks = get_all_tasks(company=company, project=project, dataset=dataset, algo_id=algo_id, algo_version=algo_version)
        query = Q(id=project) & get_company_or_none_constraint(company)
        project = Project.objects(query).first()
        if not project:
            raise errors.bad_request.InvalidProjectId(id=project)
        
        # 暂时只是简单的找到最指标最大值，TODO：增加权重逻辑
        max_metric = float('-inf')
        best_task = None
        if tasks:
            for task in tasks:
                last_metrics = task['last_metrics']
                for metric_id, metric_data in last_metrics.items():
                    for sub_metric_id, sub_metric_data in metric_data.items():
                        task_metric_name = sub_metric_data.get('metric')

                        if task_metric_name == metric_name:
                            value = float(sub_metric_data.get('value', 0))
                            if value > max_metric:
                                best_task = task
        return best_task


    else:
        # KD算法演进逻辑
        tasks = get_all_tasks(company=company, project=project, dataset=dataset, algo_id=algo_id, algo_version=algo_version)
        query = Q(id=project) & get_company_or_none_constraint(company)
        project = Project.objects(query).first()
        if not project:
            raise errors.bad_request.InvalidProjectId(id=project)
        # 获取评估指标并计算权重

        evaluation_criterion = project["configs"]["evaluation_criterion"]
        weight_rate = 0
        weight_time = 0
        weight_score = 0
        for criterion in evaluation_criterion:
                if criterion['id'] == 1:
                    weight_rate = criterion['weight'] * 0.01
                elif criterion['id'] == 2:
                    weight_time = criterion['weight'] * 0.01
                elif criterion['id'] == 3:
                    weight_score = criterion['weight'] * 0.01
        best_task = None
        best_score = float('-inf')
        if tasks:
            # params = tasks[0].get("hyperparams").get("Args")
            # for k, v in params.items():
            #     if (k == "maxTimeConsumption"):
            #         max_runtime = v.get("value")
            #     else:
            #         max_runtime = 1
            
            # 找到最大的运行时间和分数
            max_runtime = 1
            max_score = 1
            for task in tasks:
                last_metrics = task['last_metrics']
                for metric_id, metric_data in last_metrics.items():
                    for sub_metric_id, sub_metric_data in metric_data.items():
                        metric_name = sub_metric_data.get('metric')
                        value = sub_metric_data.get('value', 0)

                        if metric_name == 'complete_time':
                            complete_time = float(value)
                            if complete_time > max_runtime:
                                max_runtime = complete_time
                        elif metric_name == 'scheme_score':
                            scheme_score = float(value)
                            if scheme_score > max_score:
                                max_score = scheme_score

            log.info(f'max runtime {max_runtime} max score {max_score}')

            log.info(tasks)
            for task in tasks:
                last_metrics = task['last_metrics']
                complete_rate = 0
                complete_time = float('inf')
                scheme_score = 0
                for metric_id, metric_data in last_metrics.items():
                    for sub_metric_id, sub_metric_data in metric_data.items():
                        metric_name = sub_metric_data.get('metric')
                        value = sub_metric_data.get('value', 0)

                        log.info(f'metric name {metric_name}  value {value}')

                        if metric_name == 'complete_rate':
                            complete_rate = float(value)
                        elif metric_name == 'complete_time':
                            complete_time = float(value)
                        elif metric_name == 'scheme_score':
                            scheme_score = float(value)
                # print(f'Task {task.get("id")} complete_rate {complete_rate} complete_time {complete_time} scheme_score {scheme_score} ')
                score = (complete_rate * weight_rate) - (float(complete_time/max_runtime) * weight_time) + (float(scheme_score/max_score) * weight_score)
                print(f'Task {task.get("id")} score {score}')
                log.info(f'===================Task {task.get("id")} score {score}')
                if score > best_score:
                    best_score = score
                    best_task = task
            if best_task:
                return best_task.get('id')
            else:
                log.warn(f'can not get best task ........')
    return None


def get_project_type(project, company):
     with translate_errors_context():
        query = Q(id=project) & get_company_or_none_constraint(company)
        project = Project.objects(query).first().to_proper_dict()
        return project.get('type')

def get_project_status(project, company):
     with translate_errors_context():
        query = Q(id=project) & get_company_or_none_constraint(company)
        project = Project.objects(query).first().to_proper_dict()
        return project.get('status')


@endpoint("projects.update", response_data_model=UpdateResponse)
def update(call: APICall, company: str, request: ProjectRequest):
    """
    update

    :summary: Update project information.
              See `project.create` for parameters.
    :return: updated - `int` - number of projects updated
             fields - `[string]` - updated fields
    """
    project_id = call.data['project']
    query = Q(id=project_id) & get_company_or_none_constraint(company)
    project = Project.objects(query).first()
    fields = parse_from_call(
        call.data, create_fields, Project.get_fields(), discard_none_values=False
    )
    configs = call.data.get("configs", {})
    evaluation_criterion = configs.get("evaluation_criterion", [])
    check_arr = []
    if evaluation_criterion:
        weight_sum = 100
        for criterion in evaluation_criterion:
            check_arr.append(criterion['id'])
            check_res = has_duplicates(check_arr)
            if check_res:
                raise errors.bad_request.HasSameEvaluation
            weight = criterion['weight']
            weight_sum = weight_sum - weight
        if weight_sum != 0:
            raise errors.bad_request.InvalidEvaluationWeight
    if configs:
        if project.type == "qa":
            args = call.data["configs"]["configuration"]["algo"]["hyperparams"]["Args"]
            Args = {}
            for arg in args:
                arg["type"] = ""
                arg["section"] = "Args"
                if arg["name"] in ["maxIteration","maxTimeConsumption","tabuSize","lateSize","annealType","delugeRatio"]:
                    arg["disabled"] = False
                else: 
                    arg["disabled"] = True   
                Args[arg.get("name")] = arg  
            call.data["configs"]["configuration"]["algo"]["hyperparams"]["Args"] = Args 
        elif project.type =="normal":    
            algos = call.data["configs"]["configuration"]["algos"]
            seen_algo_versions = set()
            for algo in algos:
                algo_id = algo.get("algo_id")
                version = algo.get("version")
                if (algo_id, version) in seen_algo_versions:
                    raise errors.bad_request.SameAlgoExists()
                else:
                    seen_algo_versions.add((algo_id, version))
                args = algo["hyperparams"]["Args"]
                Args = {}
                for arg in args:
                    arg["type"] = ""
                    arg["section"] = "Args"
                    if arg["name"] in ["maxIteration","maxTimeConsumption","tabuSize","lateSize","annealType","delugeRatio"]:
                        arg["disabled"] = False
                    else: 
                        arg["disabled"] = True   
                algo["hyperparams"]["Args"] = Args
        else:
            if call.data["configs"]["configuration"].get('algo'):
                args = call.data["configs"]["configuration"]["algo"]["hyperparams"]["Args"]
                Args = {}
                for arg in args:
                    arg["type"] = ""
                    arg["section"] = "Args"
                    if arg["name"] == "maxIteration" or arg["name"] == "maxTimeConsumption":
                        arg["disabled"] = False
                    else: 
                        arg["disabled"] = True   
                    Args[arg.get("name")] = arg  
                call.data["configs"]["configuration"]["algo"]["hyperparams"]["Args"] = Args
                algos = call.data["configs"]["configuration"]["algos"]
                seen_algo_versions = set()
                for algo in algos:
                    algo_id = algo.get("algo_id")
                    version = algo.get("version")
                    if (algo_id, version) in seen_algo_versions:
                        raise errors.bad_request.SameAlgoExists()
                    else:
                        seen_algo_versions.add((algo_id, version))
                for algo in algos:
                    args = algo["hyperparams"]["Args"]
                    Args = {}
                    for arg in args:
                        arg["type"] = ""
                        arg["section"] = "Args"
                        if arg["name"] == "maxIteration" or arg["name"] == "maxTimeConsumption":
                            arg["disabled"] = False
                        else: 
                            arg["disabled"] = True   
                        Args[arg.get("name")] = arg
                    algo["hyperparams"]["Args"] = Args

    if len(project['configs']['train_data']['mock_data']['scene'])> 0 and len(configs['train_data']['mock_data']['scene'])==0:

        project_dict = project.to_proper_dict()
        fields['configs']['train_data']['mock_data']['scene'] =  project_dict['configs']['train_data']['mock_data']['scene']
    conform_tag_fields(call, fields, validate=True)

    log.info(f'------------------update fields {fields}')
    updated = ProjectBLL.update(company=company, project_id=request.project, **fields)
    conform_output_tags(call, fields)
    call.result.data_model = UpdateResponse(updated=updated, fields=fields)


def _reset_cached_tags(company: str, projects: Sequence[str]):
    org_bll.reset_tags(company, Tags.Task, projects=projects)
    org_bll.reset_tags(company, Tags.Model, projects=projects)


@endpoint("projects.move", request_data_model=MoveRequest)
def move(call: APICall, company: str, request: MoveRequest):
    moved, affected_projects = ProjectBLL.move_project(
        company=company,
        user=call.identity.user,
        project_id=request.project,
        new_location=request.new_location,
    )
    _reset_cached_tags(company, projects=list(affected_projects))

    call.result.data = {"moved": moved}


@endpoint("projects.merge", request_data_model=MergeRequest)
def merge(call: APICall, company: str, request: MergeRequest):
    moved_entitites, moved_projects, affected_projects = ProjectBLL.merge_project(
        company, source_id=request.project, destination_id=request.destination_project
    )

    _reset_cached_tags(company, projects=list(affected_projects))

    call.result.data = {
        "moved_entities": moved_entitites,
        "moved_projects": moved_projects,
    }


@endpoint("projects.validate_delete")
def validate_delete(call: APICall, company_id: str, request: ProjectRequest):
    call.result.data = validate_project_delete(
        company=company_id, project_id=request.project
    )


@endpoint("projects.delete", request_data_model=DeleteRequest)
def delete(call: APICall, company_id: str, request: DeleteRequest):
    #  默认强制删除，默认删除task
    res, affected_projects = delete_project(
        company=company_id,
        user=call.identity.user,
        project_id=request.project,
        force=request.force if request.force else True,
        delete_contents=request.delete_contents if request.delete_contents else True,
        delete_external_artifacts=request.delete_external_artifacts,
    )
    _reset_cached_tags(company_id, projects=list(affected_projects))
    call.result.data = {**attr.asdict(res)}


@endpoint(
    "projects.get_unique_metric_variants", request_data_model=GetUniqueMetricsRequest
)
def get_unique_metric_variants(
    call: APICall, company_id: str, request: GetUniqueMetricsRequest
):
    metrics = project_queries.get_unique_metric_variants(
        company_id,
        [request.project] if request.project else None,
        include_subprojects=request.include_subprojects,
        ids=request.ids,
        model_metrics=request.model_metrics,
    )

    call.result.data = {"metrics": metrics}


@endpoint("projects.get_model_metadata_keys")
def get_model_metadata_keys(call: APICall, company_id: str, request: GetParamsRequest):
    total, remaining, keys = project_queries.get_model_metadata_keys(
        company_id,
        project_ids=[request.project] if request.project else None,
        include_subprojects=request.include_subprojects,
        page=request.page,
        page_size=request.page_size,
    )

    call.result.data = {
        "total": total,
        "remaining": remaining,
        "keys": keys,
    }


@endpoint("projects.get_model_metadata_values")
def get_model_metadata_values(
    call: APICall, company_id: str, request: ProjectModelMetadataValuesRequest
):
    total, values = project_queries.get_model_metadata_distinct_values(
        company_id,
        project_ids=request.projects,
        key=request.key,
        include_subprojects=request.include_subprojects,
        allow_public=request.allow_public,
        page=request.page,
        page_size=request.page_size,
    )
    call.result.data = {
        "total": total,
        "values": values,
    }


@endpoint(
    "projects.get_hyper_parameters",
    min_version="2.9",
    request_data_model=GetParamsRequest,
)
def get_hyper_parameters(call: APICall, company_id: str, request: GetParamsRequest):
    total, remaining, parameters = project_queries.get_aggregated_project_parameters(
        company_id,
        project_ids=[request.project] if request.project else None,
        include_subprojects=request.include_subprojects,
        page=request.page,
        page_size=request.page_size,
    )

    call.result.data = {
        "total": total,
        "remaining": remaining,
        "parameters": parameters,
    }


@endpoint(
    "projects.get_hyperparam_values",
    min_version="2.13",
    request_data_model=ProjectHyperparamValuesRequest,
)
def get_hyperparam_values(
    call: APICall, company_id: str, request: ProjectHyperparamValuesRequest
):
    total, values = project_queries.get_task_hyperparam_distinct_values(
        company_id,
        project_ids=request.projects,
        section=request.section,
        name=request.name,
        include_subprojects=request.include_subprojects,
        allow_public=request.allow_public,
        pattern=request.pattern,
        page=request.page,
        page_size=request.page_size,
    )
    call.result.data = {
        "total": total,
        "values": values,
    }


@endpoint("projects.get_project_tags")
def get_tags(call: APICall, company, request: ProjectTagsRequest):
    tags, system_tags = project_bll.get_project_tags(
        company,
        include_system=request.include_system,
        filter_=get_tags_filter_dictionary(request.filter),
        projects=request.projects,
    )
    call.result.data = sort_tags_response({"tags": tags, "system_tags": system_tags})


@endpoint(
    "projects.get_task_tags", min_version="2.8", request_data_model=ProjectTagsRequest
)
def get_tags(call: APICall, company, request: ProjectTagsRequest):
    ret = org_bll.get_tags(
        company,
        Tags.Task,
        include_system=request.include_system,
        filter_=get_tags_filter_dictionary(request.filter),
        projects=request.projects,
    )
    call.result.data = sort_tags_response(ret)


@endpoint(
    "projects.get_model_tags", min_version="2.8", request_data_model=ProjectTagsRequest
)
def get_tags(call: APICall, company, request: ProjectTagsRequest):
    ret = org_bll.get_tags(
        company,
        Tags.Model,
        include_system=request.include_system,
        filter_=get_tags_filter_dictionary(request.filter),
        projects=request.projects,
    )
    call.result.data = sort_tags_response(ret)


@endpoint(
    "projects.make_public", min_version="2.9", request_data_model=MakePublicRequest
)
def make_public(call: APICall, company_id, request: MakePublicRequest):
    call.result.data = Project.set_public(
        company_id=company_id,
        user_id=call.identity.user,
        ids=request.ids,
        invalid_cls=InvalidProjectId,
        enabled=True,
    )


@endpoint(
    "projects.make_private", min_version="2.9", request_data_model=MakePublicRequest
)
def make_public(call: APICall, company_id, request: MakePublicRequest):
    call.result.data = Project.set_public(
        company_id=company_id,
        user_id=call.identity.user,
        ids=request.ids,
        invalid_cls=InvalidProjectId,
        enabled=False,
    )


@endpoint(
    "projects.get_task_parents",
    min_version="2.12",
    request_data_model=ProjectTaskParentsRequest,
)
def get_task_parents(
    call: APICall, company_id: str, request: ProjectTaskParentsRequest
):
    call.result.data = {
        "parents": ProjectBLL.get_task_parents(
            company_id,
            projects=request.projects,
            include_subprojects=request.include_subprojects,
            state=request.tasks_state,
            name=request.task_name,
        )
    }


@endpoint("projects.get_user_names")
def get_user_names(call: APICall, company_id: str, request: ProjectUserNamesRequest):
    call.result.data = {
        "users": ProjectBLL.get_entity_users(
            company_id,
            entity_cls=Model if request.entity == EntityTypeEnum.model else Task,
            projects=request.projects,
            include_subprojects=request.include_subprojects,
        )
    }


@endpoint("projects.get_all_template")
def get_all_template(call: APICall, company_id: str, _):
    template_list = Template.get_many(company=company_id, query_dict=call.data)
    # algo_category = call.data.get("algo_category")
    # category_res = AlgoCategory.get_many(
    #         company=company_id, parameters={"id":algo_category}, query_dict={"id":algo_category}
    #     )
    # category = category_res[0]['type']
    # algo_type = call.data.get("type" , "")
    # if algo_type:
    #     temp = Mock.get_all_template(algo_type)
    # else:
    #     temp = Mock.get_all_template(category)
    call.result.data = {"list": template_list}


@endpoint("projects.get_history_data")
def get_history_data(call: APICall, company_id: str, _):
    data = Mock.get_history_data(call.data.get("ids", [0]))
    call.result.data = {"list": data}


@endpoint("projects.get_live_data")
def get_live_data(call: APICall, company_id: str, _):
    data = Mock.get_live_data(call.data.get("ids", [0]))
    call.result.data = {"list": data}


@endpoint("projects.get_mock_data")
def get_mock_data(call: APICall, company_id: str, _):
    data = Mock.get_mock_data(call.data.get("ids", [0]))
    call.result.data = {"list": data}


@endpoint("projects.get_satellite")
def get_satellite(call: APICall, company_id: str, _):
    data = Mock.get_satellite(call.data.get("ids", [0]), call.data.get("type", ""))
    call.result.data = {"list": data}


@endpoint("projects.get_satellite_type")
def get_satellite_type(call: APICall, company_id: str, _):
    data = Mock.get_satellite_type()
    call.result.data = {"list": data}


@endpoint("projects.get_ground_station")
def get_ground_station(call: APICall, company_id: str, _):
    data = Mock.get_ground_station(call.data.get("ids", [0]), call.data.get("type", ""))
    call.result.data = {"list": data}


@endpoint("projects.get_ground_station_type")
def get_ground_station_type(call: APICall, company_id: str, _):
    data = Mock.get_ground_station_type()
    call.result.data = {"list": data}


@endpoint("projects.get_constraint")
def get_constraint(call: APICall, company_id: str, _):
    data = Mock.get_constraint(call.data.get("ids", [0]))
    call.result.data = {"list": data}


@endpoint("projects.get_addon")
def get_addon_by(call: APICall, company_id: str, _):
    data = Mock.get_addon(call.data.get("ids", [0]))
    call.result.data = {"list": data}


@endpoint("projects.get_evaluation")
def get_evaluation(call: APICall, company_id: str, _):
    data = Mock.get_evaluation(call.data.get("ids", [0]))
    call.result.data = {"list": data}


@endpoint("projects.get_evolution")
def get_evolution(call: APICall, company_id: str, _):
    data = Mock.get_evolution(call.data.get("id", 0))
    call.result.data = {"list": data}


@endpoint("projects.get_addon_error")
def get_addon_error(call: APICall, company_id: str, _):
    data = Mock.get_addon_error(call.data.get("id", 0))
    call.result.data = {"list": data}


@endpoint("projects.get_run_error")
def get_run_error(call: APICall, company_id: str, _):
    data = Mock.get_run_error(call.data.get("id", 0))
    call.result.data = {"list": data}


@endpoint("projects.get_max_access_time")
def get_max_access_time(call: APICall, company_id: str, _):
    data = Mock.get_max_access_time(call.data.get("id", 0))
    call.result.data = {"list": data}


@endpoint("projects.statistics")
def statistics(call: APICall, company: str, _):
    count = Project.get_count(company=company, query_dict=call.data)
    running = Project.get_count(company=company, query_dict={"status": "running"})
    finished = Project.get_count(company=company, query_dict={"status": "finished"})
    ret = {"count": count, "running": running, "finished": finished}
    call.result.data = {"statistics": ret}


@endpoint("projects.get_algo")
def get_algo(call: APICall, company: str, _):
    with translate_errors_context():
        res = Algo.get_many(company=company, parameters=call.data, query_dict=call.data)
        call.result.data = {"list": res}


@endpoint("projects.get_algo_version")
def get_algo(call: APICall, company: str, _):
    with translate_errors_context():
        res = AlgoRelease.get_many(
            company=company, parameters=call.data, query_dict=call.data
        )
        call.result.data = {"list": res}


@endpoint("projects.get_algo_params")
def get_algo(call: APICall, company: str, _):
    with translate_errors_context():
        res = AlgoRelease.get_many(
            company=company, parameters=call.data, query_dict=call.data
        )
        params = res[0].get("args")
        for param in params:
            if param.get("name") in ["maxIteration","maxTimeConsumption","tabuSize","lateSize","annealType","delugeRatio"]:
                param["disabled"] = False
            else:
                param["disabled"] = True
        call.result.data = {"Args": params}


@endpoint("projects.get_run")
def get_run(call: APICall, company: str, request: ProjectRequest):
    project_id = request.project
    with translate_errors_context():
        query = Q(id=project_id) & get_company_or_none_constraint(company)
        project = Project.objects(query).first()
        if not project:
            raise errors.bad_request.InvalidProjectId(id=project_id)

        project_dict = project.to_proper_dict()
        conform_output_tags(call, project_dict)
        started = project_dict.get("started")
        finished = project_dict.get("finished")
        if started > finished:
            run_time = datetime.datetime.now() - started
            hours, remainder = divmod(run_time.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            formatted_run_time = f"{run_time.days * 24 + hours:0>2}:{minutes:0>2}:{seconds:0>2}"  
        else:
            run_time = finished - started
            hours, remainder = divmod(run_time.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            formatted_run_time = f"{run_time.days * 24 + hours:0>2}:{minutes:0>2}:{seconds:0>2}"  
        project_dict["run_time"] = str(formatted_run_time)
        call.result.data = {"project": project_dict}


@endpoint("projects.get_all_task")
def get_all_task(call: APICall, company: str, request: ProjectRequest):
    conform_tag_fields(call, call.data)
    status = call.data.get("status", "")
    call.data.update({"only_fields": ["name", "id", "started", "completed","status","comment", "created"]})
    if status == "failed":
        call.data.update({"status": status})
    elif status == "succeed":
        call.data.update({"status": ['completed', 'in_progress', 'created', 'queued', 'stopped', 'publishing', 'published', 'closed']})
    else:
        call.data.pop('status', None)   
    ret_params = {}
    tasks = Task.get_many(
        company=company,
        parameters=call.data,
        query_dict=call.data,
        allow_public=True,
        ret_params=ret_params,
    )
    for task in tasks:
        started = task.get("started", datetime.datetime.now())
        completed = task.get("completed", datetime.datetime.now())
        run_time = completed - started
        task["run_time"] = str(run_time)
    call.result.data = {"tasks": tasks, **ret_params}


@endpoint("projects.get_task_log")
def get_task_log(call: APICall, company: str, request: LogEventsRequest):
    task_id = request.task
    task = task_bll.assert_exists(
        company, task_id, allow_public=True, only=("company", "company_origin")
    )[0]

    res = event_bll.events_iterator.get_task_events(
        event_type=EventType.task_log,
        company_id=task.get_index_company(),
        task_id=task_id,
        batch_size=request.batch_size,
        navigate_earlier=request.navigate_earlier,
        from_timestamp=request.from_timestamp,
    )

    if request.order and (
        (request.navigate_earlier and request.order == LogOrderEnum.asc)
        or (not request.navigate_earlier and request.order == LogOrderEnum.desc)
    ):
        res.events.reverse()

    call.result.data = dict(
        events=res.events, returned=len(res.events), total=res.total_events
    )


create_release_fields = {
    "algo": None,
    "version": None,
    "id": None,
    "inputs": None,
    "outputs": None,
    "args": None,
    "exec_config": None,
    "publish_status": None,
    "disabled": None,
    "description": None,
}


@endpoint("projects.save_project")
def save_project(call: APICall, company: str, request: SaveProjectRequest):
    request_algo_id = call.data.get("algo_id", "")
    request_algo_version = call.data.get("algo_version", "")
    identity = call.identity
    project_id = request.project
    name = request.name
    query = Q(id=project_id) & get_company_or_none_constraint(company)
    project = Project.objects(query).first()
    if not project:
        raise errors.bad_request.InvalidProjectId(id=project_id)
    project_dict = project.to_proper_dict()
    if project_dict["status"] != "finished":
        raise errors.bad_request.IncompleteTrain()
    if request_algo_id and request_algo_version:
        algos = project_dict["configs"]["configuration"]["algos"]
        for a in algos:
            if a['algo_id'] == request_algo_id and a['version'] == request_algo_version:
                algo = a
                project_args = algo["hyperparams"]["Args"]
                break
            else:
                raise errors.bad_request.InvalidProjectId(id=project_id) 
    else:
        if project_dict["type"] == "qa":    
            algo = project_dict["configs"]["configuration"]["algo"]
            project_args = project_dict["configs"]["configuration"]["algo"]["hyperparams"]["Args"]
        else:  
            algo = project_dict["configs"]["configuration"]["algos"][0]
            project_args = project_dict["configs"]["configuration"]["algos"][0]["hyperparams"]["Args"]  
    if name != None:
        result = Algo.get_many(
                company=company, parameters={"name":name}, query_dict={"name":name}
            )
        names = [algo_names.get("name") for algo_names in result]
        if name in names:
            raise errors.bad_request.InvalidAlgoName()
        else:
            release_id = create_algo(identity,company,request)
            call.result.data = {"algo": release_id}
            return

    version_res = AlgoRelease.get_many(
            company=company, parameters={"algo":algo['algo_id']}, query_dict={"algo":algo['algo_id']}
        ) 
    versions = [version_ids.get("version") for version_ids in version_res] 
    if request.version in versions:
        raise errors.bad_request.InvalidAlgoVersion()   
    check_result = check_release(request.version)
    if check_result:
        max_version = get_max_version(versions)
        compare_result = compare_versions(request.version,max_version)
        if compare_result:
            release = AlgoRelease.get_many(
                    company=company,
                    query_dict={"algo":algo['algo_id'], "version":algo["version"]},
            )
            if not release:
                raise errors.bad_request.InvalidProjectId(id=project_id)
            algo_id = algo["algo_id"]
            algo_version = algo["version"]
            algo["algo"] = algo_id
            s3_path = f"{algo_id}/{algo_version}"
            algo["version"] = request.version
            check_version = AlgoRelease.get_many(
                    company=company,
                    query_dict=algo,
            )
            if check_version:
                raise errors.bad_request.InvalidAlgoVersion()
            task_id = get_best_task(company,project_id)
                # task_id = "ca6958b10ae64521ae3c3eff630ffb42"   
            if not task_id:
                raise errors.bad_request.InvalidProjectId(id=project_id)
            task = TaskBLL.assert_exists(
                    company,
                    task_ids=task_id,
                    allow_public=True,
            )[0]
            task_dict = task.to_proper_dict()
            args = task_dict["hyperparams"].get("Args",{})
            release_args = [
                    {
                        "name": value.get("name",""),
                        "value": value.get("value",""),
                        "description": value.get("description",""),
                        "type": value.get("type",""),
                    }
                    for key, value in args.items()
            ]
            project_args = [
                        {
                            "name": value.get("name",""),
                            "value": value.get("value",""),
                            "description": value.get("description",""),
                            "type": value.get("type",""),
                            "min":value.get("min", "0"),
                            "max": value.get("max", "0")
                        }
                        for key, value in project_args.items()
                    ]
            inputPath = next((arg for arg in release_args if arg['name'] == 'inputPath'), None)['value']
      
            release_args = [arg for arg in release_args if arg["name"] not in ['cmd', 'inputPath', 'outPath']]
            project_args_dict = {arg["name"]: arg for arg in project_args}
            for release_arg in release_args:
                if release_arg["name"] in project_args_dict:
                    if release_arg["value"] == "None":
                        release_arg["value"] = ""
                    project_arg = project_args_dict[release_arg["name"]]
                    release_arg["min"] = project_arg.get("min", "0")
                    release_arg["max"] = project_arg.get("max", "0")

            create_release_params = {
                    "algo": algo["algo_id"],
                    "version": request.version,
                    "description": request.description,
                    "inputs": release[0]["inputs"],
                    "outputs": release[0]["outputs"],
                    "args": release_args,
                    "publish_status": "no_pass_baseline",
                    "exec_config": release[0]["exec_config"],
                    "disabled": False,
                }
            fields = parse_from_call(
                    create_release_params, create_release_fields, AlgoRelease.get_fields()
                )
            conform_tag_fields(call, fields, validate=True)
            release_id = AlgoReleaseBLL.create(
                        user=identity.user,
                        company=company,
                        **fields,
                    )     
            AlgoReleaseBLL.set_from_project(company, release_id["resease_id"])
            file_path = list_file_in_s3(s3_path)
            rename_files_in_s3(s3_path, algo_id, request.version, file_path)
            update_parameter_xml(inputPath, file_path)
            thread = threading.Thread(target=interface_test_and_baseline_test, args=(company, release_id))
            thread.start()
            call.result.data = {"algo": release_id}
        else:
            raise errors.bad_request.AlgoVersionNumberLow()    
    else:
        raise errors.bad_request.InvalidAlgoVersionNumber()


def update_parameter_xml(inputPath, file_path):
    inputPath = inputPath.replace('/data', 'tiduai-cml-dataset-pvc') + "/parameter.xml"
    for file in file_path:
        if file.find("parameter.xml") != -1:
            originalPath = file       
    try:
        s3_client.download_file(s3_config.bucket, inputPath, '/tmp/parameter.xml')
        s3_client.upload_file('/tmp/parameter.xml', s3_config.bucket, originalPath)
    except Exception as e:
        print(e)


def interface_test_and_baseline_test(company, release_id):
    request_body = {
        "algo_id" : release_id['algo_id'],
        "version" : release_id['version']
    }
    callback_url=config.get("apiserver.faas.callback_url")
    interface_test_url  = callback_url + "/algo.interface_test"
    while True:
        res = request_external_url(interface_test_url, request_body, 'POST')
        if res['data']['status'] == -1:
            break
        elif res['data']['status'] == 2:
            baseline_test_url = callback_url + "/algo.baseline_test"
            res = request_external_url(baseline_test_url, request_body, 'POST')
            if res['data']['status'] == 0:
                update_dict = {"publish_status":PublishStatus.published}
                AlgoReleaseBLL.update(company=company, release_id=release_id['resease_id'], fields=update_dict)
            return    
        elif res['data']['status'] == 1:
            time.sleep(1)

create_algo_fields = {
    "name": None,
    "disabled": None,
    "id": None,
    "description": None,
    "type": None,
    "category": None,
    "source": None,
}


def create_algo(identity,company,request):
    check_result = check_release(request.version)
    project_id = request.project
    if check_result:
        with translate_errors_context():
            query = Q(id=project_id) & get_company_or_none_constraint(company)
            project = Project.objects(query).first()
            if not project:
                raise errors.bad_request.InvalidProjectId(id=project_id)
            project_dict = project.to_proper_dict()
            if project_dict["type"] == "qa":    
                algo = project_dict["configs"]["configuration"]["algo"]
            else:  
                algo = project_dict["configs"]["configuration"]["algos"][0]
            # algo = project_dict["configs"]["configuration"]["algo"]
            algo_id = algo["algo_id"]
            algo_version = algo["version"]
            release = AlgoRelease.get_many(
                company=company,
                query_dict={"algo":algo['algo_id'], "version":algo["version"]}
            )
            if not release:
                raise errors.bad_request.InvalidProjectId(id=project_id)
            s3_path = f"{algo_id}/{algo_version}"
            algo_dict = AlgoBLL.get_by_id(algo_id,company)
            algo_create_params = {
                "name":request.name,
                "disabled":False,
                "description":request.description,
                "type":algo_dict.type,
                "category":algo_dict.category,
                "source":algo_dict.source
            }
            fields = parse_from_call(algo_create_params, create_algo_fields, Algo.get_fields())
            algo_id=AlgoBLL.create(
                    user=identity.user,
                    company=company,
                    **fields,
                )
            task_id = get_best_task(company,project_id)
            if not task_id:
                raise errors.bad_request.InvalidProjectId(id=project_id)
            task = TaskBLL.assert_exists(
                company,
                task_ids=task_id,
                allow_public=True,
            )[0]
            task_dict = task.to_proper_dict()
            args = task_dict["hyperparams"].get("Args",{})
            release_args = [
                {
                    "name": value.get("name",""),
                    "value": value.get("value",""),
                    "description": value.get("description",""),
                    "type": value.get("type",""),
                }
                for key, value in args.items()
            ]
            create_release_params = {
                "algo": algo_id,
                "version": request.version,
                "description": request.description,
                "inputs": release[0]["inputs"],
                "outputs": release[0]["outputs"],
                "args": release_args,
                "publish_status": "no_pass_baseline",
                "exec_config": release[0]["exec_config"],
                "disabled": False,
            }
            fields = parse_from_call(
                create_release_params, create_release_fields, AlgoRelease.get_fields()
            )
            release_id = AlgoReleaseBLL.create(
                    user=identity.user,
                    company=company,
                    **fields,
                )
            AlgoReleaseBLL.set_from_project(company, release_id["resease_id"])    
            file_path = list_file_in_s3(s3_path)
            rename_files_in_s3(s3_path, algo_id, request.version, file_path)
            thread = threading.Thread(target=interface_test_and_baseline_test, args=(company, release_id))
            thread.start()
            return release_id
    else:
        raise errors.bad_request.InvalidAlgoVersionNumber()
    
 
@endpoint("projects.get_algorithms")
def get_algorithms(call: APICall, company:str, _):
    with translate_errors_context():
        query = Q(id=call.data['project']) & get_company_or_none_constraint(company)
        project = Project.objects(query).first()

        if project.to_proper_dict().get('type') == 'qa':
            if not project:
                raise errors.bad_request.InvalidProjectId(id=call.data['project'])
            algo = Algo.get(company=company, id=project.configs.configuration.algo['algo_id'])
            if algo:
                call.result.data = {"algorithms":[{"algo":algo.id,"name":algo.name, "version":project.configs.configuration.algo['version']}]}
            else:
                 call.result.data ={"algorithms":[]}    
        else :
            algos = []
            for project_algo in  project.configs.configuration.algos:
                algo = Algo.get(company=company, id=project_algo['algo_id'])
                print(project_algo['algo_id'])
                if algo:
                    algos.append({"algo":algo.id,"name":algo.name, "version":project_algo['version']})

            call.result.data = {"algorithms":algos}
        return

@endpoint("projects.get_param_list")
def get_param_list(call: APICall, company:str, _):
    # 入参： project \algo \version
    # TODO:使用参数类型，这里需要校验参数类型的
    with translate_errors_context():
        query = Q(id=call.data['project']) & get_company_or_none_constraint(company)
        project = Project.objects(query).first()

        if project.to_proper_dict().get('type') == 'normal':
            params = []
            task_id = get_best_task(company=company, project=call.data['project'])
            # TODO： 从最佳Task中获取参数值
            task = Task.get(company=company, id=task_id)
            log.warn(f'========================== best task {task}')
            if not task:
                call.result.data = {'params':[]}
                return
            for key,value in project.configs.configuration.algos[0].hyperparams.Args.items():
                try:
                    int(value['value'])
                    # if len(value.get('min'))>0 and len(value.get('max'))>0:
                    params.append(value)
                except Exception as e:
                    continue
            log.warn(f'==========================  best task  {task_id}  params {params}')
            for value in params:
                #从最优算法中获取参数
                best_args = task.hyperparams["Args"]
                for arg_key,arg_value in best_args.items():
                    if value['name'] == arg_key:
                        value['origin'] = value['value']
                        value['value'] = arg_value.value
                value['type'] = 'int'
            
            p = [param for param in params]
            call.result.data = {'params':p}
            return
            
        if project.to_proper_dict().get('type') == 'qa':
            params = []
            task_id = get_best_task(company=company, project=call.data['project'])
            # TODO： 从最佳Task中获取参数值
            task = Task.get(company=company, id=task_id)
            if not task:
                call.result.data = {'params':[]}
                return
            for key,value in project.configs.configuration.algo.hyperparams.Args.items():
                try:
                    int(value['value'])
                    if len(value.get('min'))>0 and len(value.get('max'))>0:
                        params.append(value)
                except Exception as e:
                    continue
            for value in params:
                #从最优算法中获取参数
                best_args = task.hyperparams["Args"]
                for arg_key,arg_value in best_args.items():
                    if value['name'] == arg_key:
                        value['origin'] = value['value']
                        value['value'] = arg_value.value
                value['type'] = 'int'
            p = [param for param in params]
            call.result.data = {'params':p}
        else:
            for algo in project.configs.configuration.algos:
                params = []
                if algo.algo_id == call.data['algo'] and algo.version == call.data['version']:
                    for key,value in algo.hyperparams.Args.items():
                        try:
                            int(value['value'])
                        except Exception as e:
                            continue
                        params.append(value)
                    for value in params:
                        # value['origin'] = str(int(value['value'] )-1)
                        value['origin'] = value['value']
                        value['type'] = 'int'
                    p = [param for param in params]
                    call.result.data = {'params':p}
        return


@endpoint("projects.get_all_algo")
def get_all_algo(call: APICall, company_id, _):
    algo_type = call.data.get("type", None)
    with translate_errors_context():
        status = ["no_pass_baseline", "published", "pulled_down"]    
        call.data = {
            "only_fields":{"name", "id", "description"}
        }

        query = None
        if algo_type:
            query = Q(type=algo_type)
        res = Algo.get_many(
            company=company_id, parameters=call.data, query_dict=call.data, query=query
        )
        ids = [res_id.get("id") for res_id in res]
        pipeline = [
            {"$match": {"_id": {"$in": ids}}},
            {
                "$lookup": {
                    "from": "algo_release",
                    "let": {"algo_id": "$_id"},
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$algo", "$$algo_id"]},
                                        {"$in": ["$publish_status", status]}
                                    ]
                                }
                            }
                        },
                        {"$sort": {"last_update": 1}}
                    ],
                    "as": "release",
                }
            },
            {"$unwind": {"path": "$release", "preserveNullAndEmptyArrays": True}},
            {"$match": {"disabled": False}},
        ]
        result = list(Algo.aggregate(pipeline))
        release_map = {}
        for item in result:
            if  item.get("release"):
                release_map[item["_id"]] = item["release"]

        res_list = []
        for res_id in res:
            res_id_with_release = res_id.copy()
            if release_map.get(res_id.get("id"), []) == []:
                continue
            res_list.append(res_id_with_release)
        call.result.data = { "list": res_list}


@endpoint("projects.get_all_algo_release")
def get_all_algo_release(call: APICall, company_id, _):
    with translate_errors_context("retrieving algos"):
        call.data["only_fields"] = {"version", "id", "description"}
        res = AlgoRelease.get_many(
            company=company_id, parameters=call.data, query_dict=call.data
        )

        call.result.data = {"algo": res}


@endpoint("projects.get_dataset_by_project")
def get_dataset_by_project(call: APICall, company, request: ProjectRequest):
    project_id = request.project
    with translate_errors_context():
        query = Q(id=project_id) & get_company_or_none_constraint(company)
        project = Project.objects(query).first()
        if not project:
            raise errors.bad_request.InvalidProjectId(id=project_id)
        project_dict = project.to_proper_dict()
        if project_dict['configs']['train_data']['history_data']['disable']:
            history_data = []
        else:
            history_data = project_dict['configs']['train_data']['history_data']['scene']
        if project_dict['configs']['train_data']['mock_data']['disable']:
            mock_data = []
        else:
            mock_data = project_dict['configs']['train_data']['mock_data']['scene']
        if project_dict['configs']['train_data']['live_data']['disable']:
            live_data=[]
        else:
            live_data = project_dict['configs']['train_data']['live_data']['scene']
        
        dataset_list = []
        is_live_data = False
        if len(live_data) > 0:
            for dataset in live_data:
                new_dataset = {
                    "scenarioCode" : dataset.get("scenarioCode", ""),
                    "scenarioName" : dataset.get("scenarioName", ""),
                    "is_live_data": True
                }
                dataset_list.append(new_dataset)
        else:

            for dataset in history_data:
                new_dataset = {
                    "scenarioCode" : dataset.get("scenarioCode", ""),
                    "scenarioName" : dataset.get("scenarioName", ""),
                    "is_live_data": False
                }
                dataset_list.append(new_dataset)
            for dataset in mock_data:
                new_dataset = {
                    "scenarioCode" : dataset.get("dataset_id", ""),
                    "scenarioName" : dataset.get("dataset_name", ""),
                    "is_live_data": False
                }
                dataset_list.append(new_dataset)

        call.result.data = {"list": dataset_list}
        

@endpoint("projects.update_best_algo_release")
def update_best_algo_release(call: APICall, company, request: ProjectRequest):
    task_id = get_best_task(company, request.project)
    if not task_id:
        raise errors.bad_request.InvalidProjectId(id=project_id)
    task = TaskBLL.assert_exists(company,task_ids=task_id,allow_public=True,)[0]
    task_dict = task.to_proper_dict()
    algorithm = task_dict["algorithm"]
    algo = AlgoBLL.get_by_id(algorithm["id"], company_id=company)
    algo_list = Algo.get_many(company=company, query_dict={"type":algo.type})
    for algo in algo_list:
        release_list = AlgoRelease.get_many(company=company, query_dict={"algo":algo["id"]})
        for release in release_list:
            if release["best"]:
                res = AlgoReleaseBLL.set_best(company, release["id"], False)
    release = AlgoReleaseBLL.get_by_version(algo=algorithm["id"], version = algorithm["version"],company_id=company )
    res = AlgoReleaseBLL.set_best(company, release.id, True)
    if res == 1:
        call.result.data = {"best": {"algo": algorithm["id"], "version": algorithm["version"]}}


@endpoint("projects.task_statistics")
def statistics(call: APICall, company: str, _):
    with translate_errors_context():
        running = Project.get_many_with_join(company=company, query_dict={"status": "running","only_fields" : ["name", "id", "description","type", "status","started","finished"]})
        finished = Project.get_many_with_join(company=company, query_dict={"status": "finished","only_fields" : ["name", "id", "description","type", "status","started","finished"]})
        for run in running:
            if run["started"] > run["finished"]:
                run_time = datetime.datetime.now() - started
                hours, remainder = divmod(run_time.seconds, 3600)
                minutes, seconds = divmod(remainder, 60)
                formatted_run_time = f"{run_time.days * 24 + hours:0>2}:{minutes:0>2}:{seconds:0>2}"
            else:
                run_time = run["finished"] - run["started"]
                hours, remainder = divmod(run_time.seconds, 3600)
                minutes, seconds = divmod(remainder, 60)
                formatted_run_time = f"{run_time.days * 24 + hours:0>2}:{minutes:0>2}:{seconds:0>2}"
            run["run_time"] = str(formatted_run_time)
        for finish in finished:
            if finish["started"] > finish["finished"]:
                run_time = datetime.datetime.now() - started
                hours, remainder = divmod(run_time.seconds, 3600)
                minutes, seconds = divmod(remainder, 60)
                formatted_run_time = f"{run_time.days * 24 + hours:0>2}:{minutes:0>2}:{seconds:0>2}"
            else:
                run_time = finish["finished"] - finish["started"]
                hours, remainder = divmod(run_time.seconds, 3600)
                minutes, seconds = divmod(remainder, 60)
                formatted_run_time = f"{run_time.days * 24 + hours:0>2}:{minutes:0>2}:{seconds:0>2}"
            finish["run_time"] = str(formatted_run_time)
        ret = {"running": running, "finished": finished}
        call.result.data = {"statistics": ret}    


@endpoint("projects.algo_count_statistics")
def algo_count_statistics(call: APICall, company: str, _):
    with translate_errors_context():
        count = AlgoRelease.get_count(company=company, query_dict={"disabled": False})
        ret = {"count": count, "reduce": 2}
        call.result.data = {"statistics": ret}   


@endpoint("projects.algo_statistics")
def algo_statistics(call: APICall, company: str, _):
    with translate_errors_context():
        status = ["no_pass_baseline", "published", "pulled_down"]    
        query_dict = {
            "order_by" : ["-last_update"],
            "only_fields" : ["name","description","id","last_update"],
            "name": call.data.get("name", "")
        }
        res = Algo.get_many_with_join(
            company=company,
            query_dict=query_dict,
        )
        ids = [res_id.get("id") for res_id in res]
        pipeline = [
            {"$match": {"_id": {"$in": ids}}},
            {
                "$lookup": {
                    "from": "algo_release",
                    "let": {"algo_id": "$_id"},
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$algo", "$$algo_id"]},
                                        {"$in": ["$publish_status", status]}
                                    ]
                                }
                            }
                        },
                        {"$sort": {"last_update": 1}}
                    ],
                    "as": "release",
                }
            },
            {"$unwind": {"path": "$release", "preserveNullAndEmptyArrays": True}},
            {"$match": {"disabled": False}},
        ]
        result = list(Algo.aggregate(pipeline))
        release_map = {}
        for item in result:
            if  item.get("release"):
                release_map[item["_id"]] = item["release"]

        res_list = []
        for res_id in res:
            res_id_with_release = res_id.copy()
            release = release_map.get(res_id.get("id"), {})
            release_version = release.get("version", "")
            publish_status = release.get("publish_status", "")
            res_id_with_release["release"] = {
                "version" : release_version,
                "input": 3,
                "output": 1,
                "publish_status": publish_status
            }
            if release_map.get(res_id.get("id"), []) == []:
                continue
            res_list.append(res_id_with_release)
        call.result.data = {"list": res_list}

@endpoint("projects.train_histogram")
def train_histogram(call: APICall, company: str, request: ProjectRequest):
    project_id = request.project
    # with translate_errors_context():
    query = Q(id=project_id) & get_company_or_none_constraint(company)
    project = Project.objects(query).first()
    if not project:
        raise errors.bad_request.InvalidProjectId(id=project_id)
    project_dict = project.to_proper_dict()   
    history_data = project_dict['configs']['train_data']['history_data']['scene']
    mock_data = project_dict['configs']['train_data']['mock_data']['scene']
    dataset_list = history_data + mock_data

    # 获取每一个数据集的数据
    complete_rate_values = []
    complete_time_values = []
    schema_score_values = []
    dataset_name_values = []
    for dataset in dataset_list:
        log.info(f'------histogram dataset---------- {dataset}')
        dataset_name_values.append(dataset['scenarioName'])
        # TODO:还需要基线数据
        task_id = get_best_task(company, request.project, dataset=dataset['scenarioCode'])
        log.info(f'best_task {task_id}')
        task = Task.get(company=company, id=task_id)
        if not task:
            return None

        last_metrics = task["last_metrics"]  
        for metric_id, metric_data in last_metrics.items():
            for sub_metric_id, sub_metric_data in metric_data.items():
                metric_name = sub_metric_data['metric']
                value = sub_metric_data['value']

                log.info(f'Task {task_id}---------{metric_name}-------------{value}')
                if metric_name == 'complete_time':
                    complete_time_values.append(value)
                elif metric_name == 'complete_rate':
                    complete_rate_values.append(value)
                elif metric_name == 'schema_score':
                    schema_score_values.append(value)
        #返回三个图表数据：
    
    call.result.data =  {
        'complete_rate':{'x':dataset_name_values, 'y1':complete_rate_values,'y2':complete_rate_values},
        'complete_time':{'x':dataset_name_values, 'y1':complete_time_values, 'y2':complete_time_values},
        'schema_score':{'x':dataset_name_values, 'y1':schema_score_values,'y2':schema_score_values}
    }

@endpoint("projects.get_failed_projects")
def get_failed_projects(call: APICall, company: str, _ ):
    day = call.data.get("day", 1)
    filter_day = datetime.datetime.now() - datetime.timedelta(days = day)
    query_dict = {
        "only_fields":[
        "name",
        "id",
        "last_update",
        "status"
        ],
        "status":"failed"
    }
    res = Project.get_many_with_join(
            company=company,
            query_dict=query_dict,
        )
    for r in res:
        r['msg'] = f"{r['name']}训练任务，运行失败"
        if r['last_update'] < filter_day:
            res.remove(r)
    call.result.data = {"list": res}

@endpoint("projects.get_param_comb_types")
def get_param_comb_types(call: APICall, company: str, _ ):
    param_comb_types = [
        {'name':'正交实验', 'type':1},
        {'name':'网格搜索', 'type':2},
        {'name':'随机组合', 'type':3},
    ]
    call.result.data = {'list':param_comb_types}

def update_project_status(company, project_id):
    with translate_errors_context():
        query = Q(id=project_id) & get_company_or_none_constraint(company)
        project = Project.objects(query).first()

        tasks = get_all_tasks(company=company, project=project_id)
        total = len(tasks)
        succeed = 0
        failed = 0
        running = 0
        for task in tasks:
            if task.get('status') in [TaskStatus.completed]:
                succeed += 1
            elif task.get('status') in [TaskStatus.stopped, TaskStatus.failed, TaskStatus.unknown]:
                failed += 1
            elif task.get('status') in [TaskStatus.in_progress]:
                running += 1
        log.info('Project {} update: succeed {}, failed {}, running {}'.format(project.name, succeed, failed, running))
        if running > 0 and project.status != Status.running:
            fields = {'status':Status.running}
            ProjectBLL.update(company, project_id, **fields)
        if failed > total*0.2 :
            fields = {'status':Status.failed, 'finished':datetime.datetime.now()}
            ProjectBLL.update(company, project_id, **fields)
            if project.type ==  Type.cmp:
                cmp_project_finished(company=company, project_id=project_id)
        if running == 0 and succeed > total*0.8 :
            fields = {'status':Status.finished, 'finished': datetime.datetime.now()}
            ProjectBLL.update(company, project_id, **fields)
            print('type   ',project.type)
            if project.type == Type.cmp:
                cmp_project_finished(company=company, project_id=project_id)


def get_best_with_metrics(company, project_id, metric) -> str:
    tasks = get_all_tasks(company=company, project=project_id)
    query = Q(id=project_id) & get_company_or_none_constraint(company)
    project = Project.objects(query).first()
    if not project:
        raise errors.bad_request.InvalidProjectId(id=project_id)
    best_task = None
    best_score = float('inf') if metric == 'complete_time' else float(0)
    if tasks:
        for task in tasks:
            last_metrics = task["last_metrics"]  
            for metric_id, metric_data in last_metrics.items():
                for sub_metric_id, sub_metric_data in metric_data.items():
                    metric_name = sub_metric_data.get('metric')
                    value = sub_metric_data.get('value', 0)
                    log.warn('Task {}\t metric name {}\t value {}'.format(task.get('id'), metric_name, value))
                    if metric_name == metric:
                        score = value
                        if metric == 'complete_time':
                            if score < best_score:
                                best_score = score
                                best_task = task   
                        else:
                            if score > best_score:
                                best_score = score
                                best_task = task     
        if best_task:
            print('got best task ', metric, best_task)
            # log.info(f'get best task {}')
            return best_task
    return None                      


def cmp_project_finished(company, project_id):
    best_complete_rate_task = get_best_with_metrics(company, project_id, "complete_rate")
    best_complete_time_task = get_best_with_metrics(company, project_id, "complete_time")
    best_complete_score_task = get_best_with_metrics(company, project_id, "scheme_score")
    rate_str = f'1)完成率维度,算法【{best_complete_rate_task.get("algorithm").get("name") if best_complete_rate_task else ""}】表现更佳。\n'
    time_str = f'2)完成时间维度,算法【{best_complete_time_task.get("algorithm").get("name") if best_complete_time_task else ""}】表现更佳。\n' 
    score_str = f'3)完成分数维度,算法【{best_complete_score_task.get("algorithm").get("name")if best_complete_score_task else ""}】表现更佳。\n' 
    
    fields = {"conclusion":rate_str+time_str+score_str}
    ProjectBLL.update(company, project_id, **fields)



# 获取算法原始参数的训练任务
def get_original_task(company, project_id, dataset=None):
    query_dict = {
        "project" : project_id,
        "original": True
    }
    if dataset:
        query_dict.update({"dataset_id": dataset})
    original_task = {}
    tasks = Task.get_many(company=company, query_dict=query_dict)
    if tasks:
        original_task = tasks[0]
    if original_task:
        return original_task
    else:
        return None 


def has_duplicates(check_arr):
    return len(check_arr) != len(set(check_arr))

def get_max_version(versions):
    if versions:
        versions.sort(key=lambda x:tuple(int(v) for v in x.split(".")), reverse=True)
        max_version = versions[0]
    else:
        max_version = "0.0.0"    
    return max_version 


def compare_versions(version1, version2):
    v1 = list(map(int, version1.split(".")))
    v2 = list(map(int, version2.split(".")))
    if v1 > v2:
        return True
    else:
        return False    


@endpoint("projects.save_to_template")
def save_to_template(call: APICall, company: str, request: ProjectRequest ):
    updated = TemplateBLL.update(
                company=company,
                project = request.project,
            ) 
    call.result.data = {"msg":updated}       


@endpoint("projects.status_statistics")
def status_statistics(call: APICall, company: str, _):
    count = Project.get_count(company=company, query_dict=call.data)
    running = Project.get_count(company=company, query_dict={"status": "running"})
    finished = Project.get_count(company=company, query_dict={"status": "finished"})
    stopped = Project.get_count(company=company, query_dict={"status": "stopped"})
    ret = {"count": count, "running": running, "finished": finished, "stopped":stopped}

    query_dict = {
        "only_fields":['id', 'last_update', 'name',  'status', 'type', 'configs'],
        "order_by" : ["-last_update"],
        "status": "finished",
        "page":0,
        "page_size":4
        }
    projects = Project.get_many(
        company=company,
        query_dict=query_dict,
        parameters=query_dict,)
    for project in projects:
        project_id = project['id']
        task_id = get_best_task(company,project_id)
        if not task_id:
            project['complete_rate'] = 0       
            project['complete_time'] = 0 
        else:    
            task = TaskBLL.assert_exists(
                    company,
                    task_ids=task_id,
                    allow_public=True,
            )[0]
            task_dict = task.to_proper_dict()  
            last_metrics = task_dict['last_metrics']
            complete_rate = 0
            complete_time = float('inf')
            scheme_score = 0
            for metric_id, metric_data in last_metrics.items():
                for sub_metric_id, sub_metric_data in metric_data.items():
                    metric_name = sub_metric_data.get('metric')
                    value = sub_metric_data.get('value', 0)
                    if metric_name == 'complete_rate':
                        complete_rate = float(value)
                    elif metric_name == 'complete_time':
                        complete_time = float(value)
                    elif metric_name == 'scheme_score':
                        scheme_score = float(value)
            project['complete_rate'] = complete_rate       
            project['complete_time'] = complete_time         

        algo = project['configs']['configuration']['algo']
        algos = project['configs']['configuration']['algos']
        algos.append(algo)
        algo_list = []
        for a in algos:
            algo_id = a.get('algo_id', '')
            if algo_id:
                algo = Algo.get_many(company=company, query_dict={"id":algo_id})
                algo_list.append(algo[0]['name'])
        project['algo'] = algo_list  
        del project['configs']      

    call.result.data = {"statistics": ret, "project":projects}

# SS数据更新接口
@endpoint("projects.live_data_update")
def live_data_update(call: APICall, company: str, _):

    # 对下这里的字段
    live_data_code = call.data.get('scenarioCode')
    live_data_name = call.data.get('scenarioName')

    log.warn(f'update by dataset {live_data_name} {live_data_code}')
    projects = ProjectBLL.get_projects_by_live_data(company=company)
    if len(projects)>0:

        for project_id in projects:
            query = Q(id=project_id) & get_company_or_none_constraint(company)
            project = Project.objects(query).first()
            dataset = {"type": "live_data", "name": live_data_name, "id": live_data_code}

            for i in range(0,len(project.configs.train_data.live_data.scene)):
                if project.configs.train_data.live_data.disable == False:
                    project.configs.train_data.live_data.generation += 1
                    project_dict = project.to_proper_dict()
                    update_dict = {'configs':project_dict.get('configs')}

                    dataset['generation'] = project.configs.train_data.live_data.scene[i]['generation']
                    try:
                        log.info(ProjectBLL.update(company, project.id, **update_dict))
                    except Exception as e:
                        log.info(f'Update generation error: {e}')

            log.info(f'project {project.name} update by dataset {live_data_name} {live_data_code}')

            fields = {'status':Status.pending}
            ProjectBLL.update(company, project.id, **fields)

            # 创建对应的线程处理

            log.info(f'Going to create task with live dataset {dataset}')
            thread = threading.Thread(target=generate_task_param, args=(call, company, [dataset], project))
            thread.start()
    call.result.data = {'status':'ok'}


# SS数据统计接口，执行次数
@endpoint("projects.execute_times")
def project_execute_times(call: APICall, company: str, _):
    tasks = get_all_tasks(
        company=company,
        project=call.data.get('project'),
        dataset=call.data.get('dataset'),
    )
    max_generation =  0
    for task in tasks:
        if task.get('generation')+1 > max_generation:
            max_generation = task.get('generation')+1
    call.result.data = {'times':max_generation}

# 通过SS数据ID获取相关联的project
# 输入：实时场景code
# 输出：project 列表
def get_projects_by_live_data():
    data_code = ""

    projects = ProjectBLL.get_projects_by_live_data()

    for project in projects:
        query = Q(id=project) & get_company_or_none_constraint(company)
        p = Project.objects(query).first()
        # 对于每一个project

# 生成所有参数和结果的表格数据
@endpoint("projects.tasks_detail")
def tasks_detail(call: APICall, company: str, _):


    # =======================
    data = {
        "titles": [
            "complete_rate",
            "complete_time",
            "scheme_score"
        ],
        "values": [
            {

                "complete_rate": "99",
                "complete_time": "5",
                "scheme_score": "99",

            }
        ]
    }

    call.result.data = data
    return
    # ==========================

    tasks = get_all_tasks(
        company=company, 
        project= call.data.get('project'), 
        dataset=call.data.get('dataset'), 
        algo_id=call.data.get('algo_id'), 
        algo_version=call.data.get('algo_version')
        )

    query = Q(id=call.data.get('project')) & get_company_or_none_constraint(company)
    project = Project.objects(query).first()
    if not project:
        raise errors.bad_request.InvalidProjectId(id=project)
    # 获取评估指标并计算权重
    evaluation_criterion = project["configs"]["evaluation_criterion"]
    weight_rate = 0
    weight_time = 0
    weight_score = 0
    for criterion in evaluation_criterion:
            if criterion['id'] == 1:
                weight_rate = criterion['weight'] * 0.01
            elif criterion['id'] == 2:
                weight_time = criterion['weight'] * 0.01
            elif criterion['id'] == 3:
                weight_score = criterion['weight'] * 0.01
    
    param_keys = []
    
    if len(tasks) > 0:
        for k, v in tasks[0].get('hyperparams').get('Args').items():
            if v.get('section') == 'Params':
                param_keys.append(k)
    
    log.info(f'Project {project.name} param keys {param_keys}')

    tasks_values = []
    for task in tasks:
        task_values = {}
        for key in param_keys:
            value_dict = task.get('hyperparams').get('Args').get(key)
            task_values[key] = value_dict.get('value')
        
        last_metrics = task['last_metrics']
        complete_rate = 0
        complete_time = float('inf')
        scheme_score = 0
        for metric_id, metric_data in last_metrics.items():
            for sub_metric_id, sub_metric_data in metric_data.items():
                metric_name = sub_metric_data.get('metric')
                value = sub_metric_data.get('value', 0)
                if metric_name == 'complete_rate':
                    complete_rate = float(value)
                elif metric_name == 'complete_time':
                    complete_time = float(value)
                elif metric_name == 'scheme_score':
                    scheme_score = float(value)
        
        task_values['complete_rate'] = complete_rate
        task_values['complete_time'] = complete_time
        task_values['scheme_score'] = scheme_score

        tasks_values.append(task_values)

    log.info(f'titles: {param_keys}, values {tasks_values}')
    param_keys = param_keys + ['complete_rate','complete_time','scheme_score']
    call.result.data = {'titles': param_keys, 'values': tasks_values}