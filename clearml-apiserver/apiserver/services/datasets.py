from typing import Sequence, Optional, Tuple

import attr
from mongoengine import Q

from apiserver.apierrors import errors
from apiserver.apimodels.base import UpdateResponse, MakePublicRequest, IdResponse
from apiserver.apimodels.datasets import (
    DatasetFindRequest,
    DatasetFileDownloadRequest,
    DatasetFileDeleteRequest,
    DatasetFileRequest,
    DeleteRequestForMongo,
    DatasetUpdateRequest,
    DatasetCreateVersionRequest,
    DatasetRequestGetVersion,
    DatasetRequestDownLoadVersion,
    BatchDatasetFileDownloadRequest,
    BatchDatasetFileDeleteRequest,
    DatasetFileUploadRequest,
    DatasetsListRequests,
    DatasetVersionsListRequests,
    DatasetDetailRequests,
)
from apiserver.bll.datasets import (
    DatasetBLL, DatasetsBLL, DatasetsVersionBLL, VersionFilesBLL, DatasetsKind
)
from apiserver.bll.organization import OrgBLL, Tags
from apiserver.bll.project import ProjectBLL, ProjectQueries
from apiserver.bll.project.project_bll import pipeline_tag, reports_tag
from apiserver.bll.project.project_cleanup import (
    delete_project,
    validate_project_delete,
)
from apiserver.database.errors import translate_errors_context
from apiserver.database.model import EntityVisibility
from apiserver.database.model.model import Model
from apiserver.database.model.dataset import Dataset
from apiserver.database.model.task.task import TaskType, Task
from apiserver.database.utils import (
    parse_from_call,
    get_company_or_none_constraint,
)
from apiserver.service_repo import APICall, endpoint
from apiserver.services.utils import (
    conform_tag_fields,
    conform_output_tags,
    get_tags_filter_dictionary,
    sort_tags_response,
)

from boto3.session import Session
import boto3

import rarfile
import os
from apiserver.config_repo import config
from flask import request as request_openapi
import zipfile
from threading import Timer
import shutil

org_bll = OrgBLL()
project_bll = ProjectBLL()
project_queries = ProjectQueries()
dataset_bll = DatasetBLL()

create_fields = {
    "name": None,
    "description": None,
    "id": None,
    "dataset_type": None,
    "is_public": None,
}


def convert_size(bytes):
    if bytes is None or bytes < 0:
        return "Invalid Size"
    
    size_map={
        'TB':1024 ** 4,
        'GB':1024 ** 3,
        'MB':1024 ** 2,
        'KB':1024
    }

    for unit, conversion in size_map.items():
        if bytes >= conversion:
            return f"{bytes/conversion:.2f} {unit}"
    return f"{bytes:.2f} Bytes"

@endpoint("datasets.dataset_file_list")
def dataset_file_list(call: APICall, company: str, request: DatasetFileRequest):

    dataset_name = request.dataset_id
    version = request.version

    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.pvc")

    session = Session(access_key,secret_key)
    s3_client = session.client('s3',endpoint_url=url)

    s3_prefix = f"{pvc}/{dataset_name}/{str(version)}"
    try:
        res = s3_client.list_objects(
            Bucket = bucket,
            Prefix = s3_prefix
        )
        
    except Exception as e:
        print(e)
        
    result = []
    for info in res["Contents"]:
        tmp = dict()
        tmp["name"] = info["Key"].replace(s3_prefix,"")
        tmp["size"] = convert_size(info["Size"])
        tmp["last_update"] = info["LastModified"]
        # tmp["is_dir"] = os.path.isdir(tmp["name"])
        result.append(tmp)
    
    resutlt_tree = {}
    # print(result)
    for item in result:
        parts = item['name'].split('/')
        current_level = resutlt_tree
        # print("parts:",parts)
        for part in parts[1:-1]:  # 除了最后一个部分，它是一个文件名
            # print("part:",part)
            # print("Before_current_level:",current_level)
            if part not in current_level:
                current_level[part] = {'is_file': []}  # 初始化文件夹为包含空文件列表的字典
            current_level = current_level[part]
            # print("After_current_level:",current_level)
        # 最后一部分是文件名，将其添加到当前目录的文件列表
        file_info = {
            'name': parts[-1],
            'size': item['size'],
            'last_update': item['last_update']
        }
        current_level['is_file'].append(file_info)

    call.result.data = {
                "dataset_version": version,
                "dataset_list": resutlt_tree
                }

def support_gbk(zip_file):
    """ 支持中文编码 """
    name_to_info = zip_file.NameToInfo
    # copy map first
    for name, info in name_to_info.copy().items():
        try:
            real_name = name.encode('cp437').decode('gbk')
        except:
            real_name = name
        if real_name != name:
            info.filename = real_name
            del name_to_info[name]
            name_to_info[real_name] = info
    return zip_file


@endpoint("datasets.upload_dataset_file_by_cmd")
def upload_dataset_file_by_cmd(call: APICall, company: str, request: DatasetFileUploadRequest):
    dataset_name = request.dataset_id
    version = request.version
    dir_path = request.dir_path

    bucket = config.get("apiserver.s3.bucket") 
    pvc = config.get("apiserver.s3.pvc")
    s3_prefix = f"{bucket}/{pvc}/{dataset_name}/{version}"
    path = os.path.join(s3_prefix,dir_path)
    
    file = f"s3cmd put <your file path> s3://{path}"
    folder = f"s3cmd put -r <your folder path> s3:{path}"

    results = list()
    results.append({"upload a file":file})
    results.append({"upload a folder":folder})
    call.result.data = {"cmd":results}

@endpoint("datasets.upload_dataset_file")
def upload_dataset_file(call: APICall, company: str, request: DatasetFileUploadRequest):
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.pvc")

    UPLOAD_FOLDER = '/home/<USER>/files/'
    if not os.path.exists(UPLOAD_FOLDER):
        # 目录不存在，创建目录
        os.makedirs(UPLOAD_FOLDER)

    dataset_name = request.dataset_id
    version = request.version
    dir_path = request.dir_path

    session = Session(access_key,secret_key)
    s3_client = session.client('s3',endpoint_url=url)
    s3_prefix = f"{pvc}/{dataset_name}/{str(version)}"
    s3_path = os.path.join(s3_prefix,dir_path)

    File = request_openapi.files['file']
    file_name = File.filename
    file_format = file_name.split('.',1)[1]
    local_file = os.path.join(UPLOAD_FOLDER,file_name)

    if File:
        File.save(local_file)
        print(f"Saved {file_name} to {local_file}")
    else:
        print("Failed to save!")

    if file_format.lower() == 'zip':
        """上传zip压缩文件"""
        file_path = os.path.dirname(local_file)
        extract_dir_name = os.path.splitext(local_file)[0]
        print("file_path:",file_path)
        print("extract_dir_name:",extract_dir_name)
        if not os.path.exists(extract_dir_name):
            # 目录不存在，创建目录
            os.makedirs(extract_dir_name)
        try:
            with zipfile.ZipFile(local_file,'r') as zfp:
                with support_gbk(zfp) as supported_zfp:
                    supported_zfp.extractall(extract_dir_name)
        except Exception as e:
            print(e)
        for root,_,files in os.walk(extract_dir_name):
            for file in files:
                local_file_name = os.path.join(root,file)
                key_dir = os.path.relpath(local_file_name,file_path)
                s3_key = f"{s3_path}{key_dir}"
                try:
                    s3_client.upload_file(
                        Filename = local_file_name,
                        Bucket = bucket,
                        Key = s3_key
                    )
                except Exception as e:
                    print(f"Failed to upload {file} : {e}")
        try:
            res = s3_client.list_objects(
                Bucket = bucket,
                Prefix = f"{s3_path}{os.path.basename(extract_dir_name)}/"
            )
            upload_files_list = [item['Key'] for item in res['Contents']]
        except Exception as e:
            print(e)

        print(f"Delete tmp directory:{extract_dir_name}")
        if os.path.exists(extract_dir_name):
            if os.path.exists(local_file):
                """删除本地zip文件"""
                os.remove(local_file)  
            shutil.rmtree(extract_dir_name)   #删除zip文件解压后的文件夹

        call.result.data = {"upload_files":upload_files_list}
    else:
        """上传单个文件"""
        try:
            s3_client.upload_file(
                Filename = local_file,
                Bucket = bucket,
                Key = f"{s3_path}{file_name}"
            )
        except Exception as e:
            print(f"Failed to upload {file_name} : {e}")
        
        if os.path.exists(local_file):
            os.remove(local_file)
        call.result.data = {"upload_files":f"{s3_path}{file_name}"}

@endpoint("datasets.batch_delete_dataset_file")
def batch_delete_dataset_file(call: APICall, company: str, request: BatchDatasetFileDeleteRequest):

    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.pvc")

    dataset_name = request.dataset_id
    version = request.version
    batch_file_names = request.batch_file_names

    session = Session(access_key,secret_key)
    s3_client = session.client('s3',endpoint_url=url)
    s3_prefix = f"{pvc}/{dataset_name}/{str(version)}"

    result = list()
    for file_name in batch_file_names:
        if len(os.path.basename(file_name).split('.',1)) > 1:
            """删除单个文件"""
            try:
                s3_client.delete_object(
                    Bucket = bucket,
                    Key = f"{s3_prefix}/{file_name}"
                )
            except Exception as e:
                print(e)
            # call.result.data = {"delete_file":os.path.basename(file_name)}
            result.append(file_name)
        else:
            """删除一个文件夹文件"""
            delete_objects = list()
            print("file_name:",file_name)
            try:
                res = s3_client.list_objects(
                    Bucket = bucket,
                    Prefix = os.path.normpath(f"{s3_prefix}{file_name}/")
                )
                delete_objects = [{'Key':item['Key']} for item in res['Contents']]
                try:
                    s3_client.delete_objects(
                        Bucket = bucket,
                        Delete = {
                                'Objects': delete_objects
                            }
                        )
                    for file in delete_objects:
                        result.append(file['Key'])
                except Exception as e:
                    print(e)
            except Exception as e:
                print(e)
            # call.result.data = {"delete_directory":f"{s3_prefix}{file_name}/"}
    call.result.data = {"batch delete files":result}

@endpoint("datasets.delete_dataset_file")
def delete_dataset_file(call: APICall, company: str, request: DatasetFileDeleteRequest):
    
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.pvc")

    dataset_name = request.dataset_id
    file_name = request.file_name
    version = request.version

    session = Session(access_key,secret_key)
    s3_client = session.client('s3',endpoint_url=url)
    
    s3_prefix = f"{pvc}/{dataset_name}/{str(version)}"

    if len(os.path.basename(file_name).split('.',1)) > 1:
        ############删除单个文件###########
        try:
            s3_client.delete_object(
                Bucket = bucket,
                Key = f"{s3_prefix}/{file_name}"
            )
        except Exception as e:
            print(e)
        call.result.data = {"delete_file":os.path.basename(file_name)}
    else:
        ############删除一个文件夹文件###########
        delete_objects = list()
        try:
            res = s3_client.list_objects(
                Bucket = bucket,
                Prefix = f"{s3_prefix}{file_name}/"
            )
            delete_objects = [{'Key':item['Key']} for item in res['Contents']]
            try:
                s3_client.delete_objects(
                    Bucket = bucket,
                    Delete = {
                            'Objects': delete_objects
                        }
                    )
            except Exception as e:
                print(e)
        except Exception as e:
            print(e)
        call.result.data = {"delete_directory":f"{s3_prefix}{file_name}/"}
    
@endpoint("datasets.batch_download_dataset_file")
def batch_download_dataset_file(call: APICall, company: str, request: BatchDatasetFileDownloadRequest):
    DOWNLOAD_DIR = '/home/<USER>'
    # 检查目录是否存在
    if not os.path.exists(DOWNLOAD_DIR):
        # 目录不存在，创建目录
        os.makedirs(DOWNLOAD_DIR)
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.pvc")

    dataset_name = request.dataset_id
    version = request.version
    batch_file_names = request.batch_file_names

    session = Session(access_key,secret_key)
    s3_client = session.client('s3',endpoint_url=url)
    s3_prefix = f"{pvc}/{dataset_name}/{str(version)}"

    tmp_file_path = os.path.normpath(f"{DOWNLOAD_DIR}/{dataset_name}/{'/tmp/'}") #/home/<USER>/3d07b24944e34b8e8373637e0302cb9c/tmp
    if not os.path.exists(tmp_file_path):
        os.makedirs(tmp_file_path)

    # print(batch_file_names)
    """将需要下载的文件和文件夹文件下载到后端环境中"""
    try:
        for file_name in batch_file_names:
            if len(os.path.basename(file_name).split('.',1)) > 1:
                print("File")
                key = os.path.normpath(f"{s3_prefix}/{file_name}") #default-xhz-pvc/3d07b24944e34b8e8373637e0302cb9c/6/Datasets/Commerce/10corr/Data_10corr_4.txt
                s3_client.download_file(
                            Bucket = bucket,
                            Key = key,
                            Filename = os.path.normpath(f"{tmp_file_path}/{os.path.basename(file_name)}")
                        )
            else:
                try:
                    print("Dir")
                    res = s3_client.list_objects(
                        Bucket = bucket,
                        Prefix = os.path.normpath(f"{s3_prefix}/{file_name}")
                    )
                    for info in res['Contents']:
                            key = info['Key']  #default-xhz-pvc/3d07b24944e34b8e8373637e0302cb9c/6/Datasets/Commerce/10corr/Data_10corr_1.txt
                            basename_file_name = os.path.basename(info['Key']) #Data_10corr_1.txt
                            tmp_dir_path = os.path.normpath(f"{tmp_file_path}/{os.path.basename(os.path.dirname(key))}") #/home/<USER>/3d07b24944e34b8e8373637e0302cb9c/tmp/10corr
                            if not os.path.exists(tmp_dir_path):
                                os.makedirs(tmp_dir_path)
                            s3_client.download_file(
                                Bucket = bucket,
                                Key = key,
                                Filename = f"{tmp_dir_path}/{basename_file_name}"
                            )
                except Exception as e:
                    print(e)

        """将需要下载的文件进行压缩"""
        zip_filename = f"{DOWNLOAD_DIR}/{dataset_name}/{dataset_name}.zip"
        # print(zip_filename)
        with zipfile.ZipFile(zip_filename,'w') as zip_file:
            for root,dir,files in os.walk(tmp_file_path):
                for file in files:
                    file_path = os.path.join(root,file)
                    zip_file.write(file_path,arcname=os.path.relpath(file_path,tmp_file_path))
        
        """上传压缩包"""
        s3_client.upload_file(
            Bucket = bucket,
            Key = os.path.basename(zip_filename),
            Filename = zip_filename
        )
        """生成压缩文件下载url"""
        download_dir_url = s3_client.generate_presigned_url(
            'get_object',
            Params = {
                'Bucket': bucket,
                'Key': os.path.basename(zip_filename)
            },
            ExpiresIn = 3600
        )
        Timer(300,delete_zip_file,args=[os.path.basename(zip_filename)]).start()
        call.result.data = {"download_url ": download_dir_url}
    except Exception as e:
        print(e)
    finally:
        print(f"Delete tmp directory:{os.path.dirname(tmp_file_path)}")
        if os.path.exists(os.path.dirname(tmp_file_path)):
            # os.remove(os.path.dirname(tmp_file_path))
            shutil.rmtree(os.path.dirname(tmp_file_path))

    
@endpoint("datasets.download_dataset_file")
def download_dataset_file(call: APICall, company: str, request: DatasetFileDownloadRequest):
    
    DOWNLOAD_DIR = '/home/<USER>'
    # 检查目录是否存在
    if not os.path.exists(DOWNLOAD_DIR):
        # 目录不存在，创建目录
        os.makedirs(DOWNLOAD_DIR)

    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.pvc")

    dataset_name = request.dataset_id
    file_name = request.file_name
    version = request.version

    session = Session(access_key,secret_key)
    s3_client = session.client('s3',endpoint_url=url)

    s3_prefix = f"{pvc}/{dataset_name}/{str(version)}"

    if len(os.path.basename(file_name).split('.',1)) > 1:
        print("Download a file")
        #####下载单个文件########
        try:
            key = f"{s3_prefix}/{file_name}"
            download_url = s3_client.generate_presigned_url(
                'get_object',
                Params = {
                    'Bucket': bucket,
                    'Key': key
                },
                ExpiresIn = 3600
            )

        except Exception as e:
            print(e)
        call.result.data = {"download_url ": download_url}
    else:
        print("Download a directory")
        #####下载文件夹##########
        zip_filename = f"{os.path.join(DOWNLOAD_DIR,file_name.split('/')[-2])}.zip"
        print(zip_filename)
        try:
            res = s3_client.list_objects(
                Bucket = bucket,
                Prefix = os.path.normpath(f"{s3_prefix}/{file_name}")
            )
            with zipfile.ZipFile(zip_filename,'w',zipfile.ZIP_DEFLATED) as zip_file:
                for info in res['Contents']:
                    key = info['Key']
                    # basename_file_name = os.path.basename(info['Key'])
                    common_path = f"{s3_prefix}/{file_name}/"
                    relative_path = os.path.relpath(key,common_path)
                    tmp_file_path = f"/tmp/{relative_path}"
                    s3_client.download_file(
                        Bucket = bucket,
                        Key = key,
                        Filename = tmp_file_path
                    )
                    zip_file.write(tmp_file_path,arcname=relative_path)
                    os.remove(tmp_file_path)
            s3_client.upload_file(
                Bucket = bucket,
                Key = os.path.basename(zip_filename),
                Filename = zip_filename
            )    
            download_dir_url = s3_client.generate_presigned_url(
                'get_object',
                Params = {
                    'Bucket': bucket,
                    'Key': os.path.basename(zip_filename)
                },
                ExpiresIn = 3600
            )
            Timer(60, delete_zip_file, args=[os.path.basename(zip_filename)]).start()
        except Exception as e:
            print(e)
        finally:
            if os.path.exists(zip_filename):
                os.remove(zip_filename)
        call.result.data = {"download_url ": download_dir_url}
        



def calculate_dataset_size(dataset_name: str):
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.pvc")

    session = Session(access_key, secret_key)
    s3_client = session.client('s3', endpoint_url=url)

    dataset_size = 0
    dataset_version_count = 0 
    try:
        cal_dataset_size = s3_client.list_objects(
            Bucket = bucket,
            Prefix = pvc + '/' + dataset_name + '/'
        )

        if cal_dataset_size.__contains__('Contents'):
            for dataset_info in cal_dataset_size['Contents']:
                dataset_size += dataset_info['Size']           
    except Exception as e:
        print(e)

    try:
        cal_version_count = s3_client.list_objects(
            Bucket = bucket,
            Prefix = pvc + '/' + dataset_name + '/',
            Delimiter = '/'
        )
        if cal_version_count.__contains__('CommonPrefixes'):
            dataset_version_count = len(cal_version_count['CommonPrefixes'])            
    except Exception as e:
        print(e)

    return convert_size(dataset_size), dataset_version_count


@endpoint(
    "datasets.create_dataset",
    required_fields=["name"],
    response_data_model=IdResponse,
)
def create_dataset(call: APICall, company: str, _):
    identity = call.identity
    with translate_errors_context():
        # print("call.data:",call.data)
        fields = parse_from_call(call.data, create_fields, Dataset.get_fields())
        conform_tag_fields(call, fields, validate=True)
        res = IdResponse(
            id=DatasetBLL.create(
                user=identity.user,
                company=company,
                **fields,
            )
        )
        return res
    
@endpoint(
    "datasets.get_id_by_name",
    required_fields=["dataset_name"]
)
def get_id_by_name(call: APICall, company: str, request: DatasetFindRequest):
    name = request.dataset_name
    with translate_errors_context():
        fields = parse_from_call(call.data, create_fields, Dataset.get_fields())
        conform_tag_fields(call, fields, validate=True)
        res = DatasetBLL.get_id_by_name(name)

        call.result.data = {"dataset_id":res}

@endpoint("datasets.get_all_dataset")
def get_all_dataset(call: APICall, company_id, _):
    with translate_errors_context():
        res = Dataset.get_many(
            company=company_id, parameters=call.data, query_dict=call.data
        )
        count = Dataset.get_count(
            company=company_id,
            query_dict=call.data,
        )
        page = call.data.get("page", 0)
        page_size = call.data.get("page_size", 0)
        pager = {"page": page, "page_size": page_size, "count": count}
        
        for item in res:
            dataset_name = item.get('id')
            item['size'], item["versions_num"] = calculate_dataset_size(dataset_name)
        call.result.data = {"pager": pager, "list":res}

@endpoint("datasets.statistics")
def get_all_dataset(call: APICall, company_id, _):
    with translate_errors_context():
        public = Dataset.get_count(company=company_id, query_dict = {"is_public":"public"})
        private = Dataset.get_count(company=company_id, query_dict = {"is_public":"private"})
        preset = Dataset.get_count(company=company_id, query_dict = {"is_public":"preset"})
        statistics = {"public":public, "private":private, "preset":preset}
        call.result.data = {"statistics": statistics}

@endpoint("datasets.delete_dataset", required_fields=["dataset_id"], ) 
def delete_dataset(call: APICall, __, request: DeleteRequestForMongo):
    with translate_errors_context():
        res = DatasetBLL.delete(request.dataset_id)
        if res == 1:
            call.result.data = {"delete_dataset":request.dataset_id}

@endpoint("datasets.update_dataset", required_fields=["dataset_id"], response_data_model=UpdateResponse)
def update_dataset(call: APICall, company:str, request:DatasetUpdateRequest):
    with translate_errors_context():
        fields = parse_from_call(
         call.data, create_fields, Dataset.get_fields(), discard_none_values=False
        )
        updated = DatasetBLL.update(company=company, dataset_id=request.dataset_id, **fields)
        call.result.data_model = UpdateResponse(updated=updated, fields=fields)

@endpoint("datasets.create_dataset_version",required_fields=["dataset_id","version"])
def create_dataset_version(call: APICall, company: str, request: DatasetCreateVersionRequest):
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.pvc")

    session = Session(access_key, secret_key)
    s3_client = session.client('s3', endpoint_url=url)
    key = pvc + '/' + request.dataset_id + '/' +request.version + '/' + request.dataset_id + '.txt'
    file_content = request.description if request.description else ''
    try:
        response = s3_client.list_objects_v2(
            Bucket=bucket,
            Prefix=f"{pvc}/{request.dataset_id }/",
            Delimiter='/'
        )
        existing_versions = [prefix['Prefix'] for prefix in response.get('CommonPrefixes', [])]
        target_version_prefix = f"{pvc}/{request.dataset_id}/{request.version}/"
        
        if target_version_prefix in existing_versions:
            call.result.code = 400
            call.result.data = {"message":"Version already exists"}
            return 
            
        s3_client.put_object(
            Bucket=bucket,
            Key=key,
            Body=file_content
        )
        call.result.data = {"creater_version":request.version}
    except Exception as e:
        print(e)
        
@endpoint("datasets.get_all_dataset_version",required_fields=["dataset_id"])
def get_all_dataset_version(call: APICall, company: str, request: DatasetRequestGetVersion):
    dataset_name = request.dataset_id
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.pvc")

    session = Session(access_key,secret_key)
    s3_client = session.client('s3',endpoint_url=url)
    desc_filename = f"{dataset_name}.txt"
    s3_prefix = f"{pvc}/{dataset_name}/"
    versions = []
    try:
        res = s3_client.list_objects_v2(
            Bucket=bucket,
            Prefix=s3_prefix,
            Delimiter='/'
        )
        for obj in res.get('CommonPrefixes', []):
            prefix = obj.get('Prefix', '')
            clean_prefix = prefix[len(s3_prefix):].rstrip('/')
            desc_key = f"{prefix}{desc_filename}"
            file_count = 0
            total_size = 0
            objects_response = s3_client.list_objects_v2(Bucket=bucket, Prefix=prefix)
            if 'Contents' in objects_response:
                for item in objects_response['Contents']:
                    if item['Key'] != desc_key:  # 排除desc文件本身
                        file_count += 1
                        total_size += item['Size']
            try:
                desc_response = s3_client.get_object(Bucket=bucket, Key=desc_key)
                desc_content = desc_response['Body'].read().decode('utf-8')

                version_object = {
                    'version': clean_prefix,
                    'description': desc_content,
                    'file_count': file_count,
                    'total_size': convert_size(total_size)
                }
                versions.append(version_object)

            except s3_client.exceptions.NoSuchKey:
                version_object = {
                    'version': clean_prefix,
                    'description': '',
                    'file_count': file_count,
                    'total_size': convert_size(total_size)
                }
                versions.append(version_object)

    except Exception as e:
        print(e)
    call.result.data = {"dataset_id":dataset_name, "version_list":versions}

@endpoint("datasets.download_dataset_version",required_fields=["dataset_id","version"])
# def download_dataset_version(call: APICall, company: str, request: DatasetRequestDownLoadVersion):
#     url = config.get("apiserver.s3.url")
#     access_key = config.get("apiserver.s3.access_key")
#     secret_key = config.get("apiserver.s3.secret_key")
#     bucket = config.get("apiserver.s3.bucket")
#     pvc = config.get("apiserver.s3.pvc")

#     dataset_name = request.dataset_id
#     version = request.version
#     desc_filename = f"{dataset_name}.txt"

#     session = Session(aws_access_key_id=access_key, aws_secret_access_key=secret_key)
#     s3_client = session.client('s3', endpoint_url=url)
#     s3_prefix = f"{pvc}/{dataset_name}/{str(version)}"

#     try:
#         response = s3_client.list_objects_v2(Bucket=bucket, Prefix=s3_prefix)

#         if 'Contents' in response:
#             for obj in response['Contents']:
#                 key = obj['Key']
#                 if key.endswith(desc_filename):
#                     continue  
#                 relative_path = key[len(s3_prefix) + 1:]
#                 target_file_path = os.path.join(os.getcwd(), str(version), relative_path)
#                 os.makedirs(os.path.dirname(target_file_path), exist_ok=True)
#                 s3_client.download_file(Bucket=bucket, Key=key, Filename=target_file_path)
#                 print(f"Downloaded {key} to {target_file_path}")

#         call.result.data = {"message": "Download completed", "version": version}

#     except Exception as e: 
#         print(e)

def download_dataset_version(call: APICall, company: str, request: DatasetRequestDownLoadVersion):
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.pvc")

    dataset_name = request.dataset_id
    version = request.version
    desc_filename = f"{dataset_name}.txt"
    s3_prefix = f"{pvc}/{dataset_name}/{str(version)}"
    zip_filename = f"{dataset_name}_{version}.zip"

    session = Session(aws_access_key_id=access_key, aws_secret_access_key=secret_key)
    s3_client = session.client('s3', endpoint_url=url)

    try:
        response = s3_client.list_objects_v2(Bucket=bucket, Prefix=s3_prefix)
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for obj in response['Contents']:
                key = obj['Key']
                if key.endswith(desc_filename):
                    continue
                file_name = os.path.basename(key)
                temp_file_path = f"/tmp/{file_name}"
                s3_client.download_file(bucket, key, temp_file_path)
                relative_path = os.path.relpath(key, s3_prefix)
                relative_path = version + "/" + relative_path
                zip_file.write(temp_file_path, relative_path)
                os.remove(temp_file_path) 
        s3_client.upload_file(zip_filename, bucket, zip_filename)
        download_url = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket, 'Key': zip_filename},
            ExpiresIn=3600
        )
        Timer(600, delete_zip_file, args=[zip_filename]).start()
        call.result.data = {"download_url": download_url}

    except Exception as e:
        print(e)
    finally:
        if os.path.exists(zip_filename):
            os.remove(zip_filename)

def delete_zip_file(zip_filename: str):
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")

    session = Session(aws_access_key_id=access_key, aws_secret_access_key=secret_key)
    s3_client = session.client('s3', endpoint_url=url)
    
    try:
        s3_client.delete_object(Bucket=bucket, Key=zip_filename)
        print(f"Deleted {zip_filename} from S3")
    except Exception as e:
        print(e)


@endpoint("datasets.kinds")
def get_kinds(call: APICall):
    """获取数据关联的类型信息"""
    kinds = [{"value": kind.value, "name": kind.cn_name} for kind in DatasetsKind]
    call.result.data = {
        "kinds": kinds
    }


@endpoint("datasets.list", request_data_model=DatasetsListRequests)
def get_datasets_list(call: APICall):
    """获取数据集合列表"""
    call.result.data = DatasetsBLL.list(page=call.data_model.page, page_size=call.data_model.page_size,name=call.data_model.name,kind=call.data_model.kind,algo_id=call.data_model.algo_id)


@endpoint("datasets.list_test")
def gen_datasets_list(call: APICall):
    """获取数据集合某个版本详情信息"""
    from apiserver import database

    algo_id = "10"
    user_id = "20"
    base_id = "22321"
    for i in range(1, 10):
        # 创建数据集合
        dataset_id = database.utils.id()
        DatasetsBLL.create(
            id=dataset_id, name=f"数据集合-{i}", description=f"数据集合描叙-{i}", kind="mixture", size=f"{i}Gi", base_id=base_id,
            algorithm_id=algo_id, user_id=user_id, algorithm_name=f"演进算法-{i}"
        )
        # 创建数据集合版本
        for i in range(1, 10):
            version_id = DatasetsVersionBLL.create(id=database.utils.id(), version=f"1.0.{i}", dataset_id=dataset_id)

            # 双击版本关联的文件
            for i in range(1, 30):
                VersionFilesBLL.create(id=database.utils.id(), version_id=version_id, filename=f"/tmp/{dataset_id}-{version_id}-{i}.jpg", filesize="10ki")
    call.result.data = {}


@endpoint("datasets.versions", request_data_model=DatasetVersionsListRequests)
def get_dataset_versions(call: APICall):
    """获取数据集合关联的版本列表"""

    call.result.data = {
        "versions": DatasetsVersionBLL.list(dataset_id=call.data_model.dataset_id)
    }


@endpoint("datasets.details", request_data_model=DatasetDetailRequests)
def get_dataset_details(call: APICall):
    """获取数据集合某个版本详情信息"""
    # 查询数据集合
    dataset = DatasetsBLL.get_by_id(call.data_model.dataset_id)
    # 查询集合关联的集合
    versions = DatasetsVersionBLL.list(call.data_model.dataset_id)
    # 查询版本关联的文件
    # 最新版本等于列表中第一个元素
    version_id = call.data_model.version_id
    if not call.data_model.version_id and versions:
        version_id = versions[0]["id"]

    files = VersionFilesBLL.list(version_id=version_id, page=call.data_model.page, page_size=call.data_model.page_size)

    dataset["version_id"] = version_id
    dataset["versions"] = versions
    dataset["files"] = files

    call.result.data = dataset


