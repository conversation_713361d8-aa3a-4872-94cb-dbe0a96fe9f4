import datetime


class Mock:
    history_data = [
        {
            "id": 1,
            "name": "数据集1",
            "description": "数据集描述1",
        },
        {
            "id": 2,
            "name": "数据集2",
            "description": "数据集描述2",
        },
        {
            "id": 3,
            "name": "数据集3",
            "description": "数据集描述3",
        },
    ]

    @classmethod
    def get_history_data(cls, search_ids):
        if search_ids == [0]:
            return Mock.history_data
        elif search_ids:
            results = []
            for search_id in search_ids:
                for entry in cls.history_data:
                    if entry["id"] == search_id:
                        results.append(entry)
                        break
            return results
        else:
            return []

    live_data = [
        {
            "id": 1,
            "name": "中心1",
            "description": "live_data1 description",
            "status": "online",
        },
        {
            "id": 2,
            "name": "中心2",
            "description": "live_data2 description",
            "status": "online",
        },
        {
            "id": 3,
            "name": "中心3",
            "description": "live_data3 description",
            "status": "online",
        },
    ]

    @classmethod
    def get_live_data(cls, search_ids):
        if search_ids == [0]:
            return Mock.live_data
        elif search_ids:
            results = []
            for search_id in search_ids:
                for entry in cls.live_data:
                    if entry["id"] == search_id:
                        results.append(entry)
                        break
            return results
        else:
            return []

    mock_data = [
        {
            "id": 1,
            "name": "区域1",
            "description": "mock_data1 description",
        },
        {
            "id": 2,
            "name": "区域2",
            "description": "mock_data2 description",
        },
        {
            "id": 3,
            "name": "区域3",
            "description": "mock_data3 description",
        },
    ]

    @classmethod
    def get_mock_data(cls, search_ids):
        if search_ids == [0]:
            return Mock.mock_data
        elif search_ids:
            results = []
            for search_id in search_ids:
                for entry in cls.mock_data:
                    if entry["id"] == search_id:
                        results.append(entry)
                        break
            return results
        else:
            return []

    satellite = [
        {
            "id": 1,
            "name": "wx1",
            "description": "wx1 description",
            "serial_number": "11-1-1",
            "type": "optics",
            "load_name": "tc",
        },
        {
            "id": 2,
            "name": "wx2",
            "description": "wx2 description",
            "serial_number": "11-1-2",
            "type": "optics",
            "load_name": "tc",
        },
        {
            "id": 3,
            "name": "wx3",
            "description": "wx3 description",
            "serial_number": "11-1-3",
            "type": "sar",
            "load_name": "tc",
        },
        {
            "id": 4,
            "name": "wx4",
            "description": "wx4 description",
            "serial_number": "11-1-4",
            "type": "sar",
            "load_name": "tc",
        },
        {
            "id": 5,
            "name": "wx5",
            "description": "wx5 description",
            "serial_number": "11-1-5",
            "type": "electronic",
            "load_name": "tc",
        },
        {
            "id": 6,
            "name": "wx6",
            "description": "wx6 description",
            "serial_number": "11-1-5",
            "type": "electronic",
            "load_name": "tc",
        },
    ]

    @classmethod
    def get_satellite(cls, search_ids, type):
        if search_ids == [0]:
            return Mock.satellite
        elif search_ids:
            results = []
            for search_id in search_ids:
                for entry in cls.satellite:
                    if entry["id"] == search_id:
                        results.append(entry)
                        break
            return results
        elif type != "":
            results = []
            for entry in cls.satellite:
                if entry["type"] == type:
                    results.append(entry)
            return results
        else:
            return []

    @classmethod
    def satellite_statistics(cls, search_ids, _):
        if search_ids:
            results = {"optics": [], "sar": [], "electronic": []}
            for search_id in search_ids:
                for entry in cls.satellite:
                    if entry["id"] == search_id:
                        if entry["type"] == "optics":
                            results["optics"].append(entry)
                        elif entry["type"] == "sar":
                            results["sar"].append(entry)
                        elif entry["type"] == "electronic":
                            results["electronic"].append(entry)
                        break
            return results
        else:
            return {}

    satellite_type = [
        {"id": 1, "name": "optics"},
        {"id": 2, "name": "sar"},
        {"id": 3, "name": "electronic"},
    ]

    @classmethod
    def get_satellite_type(cls):
        return Mock.satellite_type

    ground_station = [
        {
            "id": 1,
            "name": "北京",
            "description": "description 1",
            "type": "stationary",
            "identifier": "BJ",
            "antennas_num": "123",
        },
        {
            "id": 2,
            "name": "上海",
            "description": "description 2",
            "type": "stationary",
            "identifier": "SH",
            "antennas_num": "224",
        },
        {
            "id": 3,
            "name": "广州",
            "description": "description 3",
            "type": "stationary",
            "identifier": "GZ",
            "antennas_num": "98",
        },
        {
            "id": 4,
            "name": "深圳",
            "description": "description 4",
            "type": "mobile",
            "identifier": "GZ",
            "antennas_num": "112",
        },
    ]

    ground_station_type = [
        {"id": 1, "name": "stationary"},
        {"id": 2, "name": "mobile"},
    ]

    @classmethod
    def get_ground_station_type(cls):
        return Mock.ground_station_type

    @classmethod
    def get_ground_station(cls, search_ids,type):
        if search_ids == [0]:
            return Mock.ground_station
        elif search_ids:
            results = []
            for search_id in search_ids:
                for entry in cls.ground_station:
                    if entry["id"] == search_id:
                        results.append(entry)
                        break
            return results
        elif type != "":
            results = []
            for entry in cls.ground_station:
                if entry["type"] == type:
                    results.append(entry)
            return results
        else:
            return []

    @classmethod 
    def ground_station_statistics(cls, search_ids, _):
        if search_ids:
            results = {"stationary": [], "mobile": []}
            for search_id in search_ids:
                for entry in cls.ground_station:
                    if entry["id"] == search_id:
                        if entry["type"] == "stationary":
                            results["stationary"].append(entry)
                        elif entry["type"] == "mobile":
                            results["mobile"].append(entry)
                        break
            return results
        else:
            return {}


    qa_template = [
        {
            "id": 1,
            "name": "问答算法模板",
            "description": "构建问答式的算法训练环境，通过配置问答插件以及调整算法参数的方式，优化算法。",
            "publisher": "tidu",
            "publish_time": datetime.date(2024, 10, 11),
        },
    ]
    normal_template = [
        {
            "id": 2,
            "name": "普通算法模板",
            "description": "构建通用的算法训练环境，通过配置通用插件以及调整算法参数的方式，优化算法。",
            "publisher": "tidu",
            "publish_time": datetime.date(2024, 10, 9),
        },
    ]

    cmp_template = [
        {
            "id": 3,
            "name": "对比算法模板",
            "description": "构建对比算法训练环境，通过添加多个算法以及调整算法参数的方式，选出最优的算法。",
            "publisher": "tidu",
            "publish_time": datetime.date(2024, 10, 9),
        },
    ]

    @classmethod
    def get_all_template(cls, temp_name):
        print(temp_name)
        if temp_name == "qa":
            return Mock.qa_template
        elif temp_name == "normal":
            return Mock.normal_template
        elif temp_name == "cmp":
            return Mock.cmp_template

    addon = [
        {
            "id": 1,
            "name": "dx1",
            "algo": "algo5",
            "source": "tidu",
            "version": "v1.0.0",
            "ectype_num": "2",
            "update_time": datetime.date(2024, 10, 12),
        },
        {
            "id": 2,
            "name": "dx2",
            "algo": "algo5",
            "source": "tidu",
            "version": "v1.0.0",
            "ectype_num": "7",
            "update_time": datetime.date(2024, 10, 1),
        },
        {
            "id": 3,
            "name": "dx3",
            "algo": "algo5",
            "source": "tidu",
            "version": "v1.0.0",
            "ectype_num": "4",
            "update_time": datetime.date(2024, 9, 9),
        },
        {
            "id": 4,
            "name": "dx4",
            "algo": "algo5",
            "source": "tidu",
            "version": "v1.0.0",
            "ectype_num": "6",
            "update_time": datetime.date(2024, 10, 9),
        },
        {
            "id": 5,
            "name": "dx5",
            "algo": "algo5",
            "source": "tidu",
            "version": "v1.0.0",
            "ectype_num": "9",
            "update_time": datetime.date(2024, 10, 15),
        },
    ]

    @classmethod
    def get_addon(cls, search_ids):
        if search_ids == [0]:
            return Mock.addon
        elif search_ids:
            results = []
            for search_id in search_ids:
                for entry in cls.addon:
                    if entry["id"] == search_id:
                        results.append(entry)
                        break
            return results
        else:
            return []

    evaluation = [
        {
            "id": 1,
            "name": "任务完成率",
            "description": "任务完成率是指一定时间内容完成的任务数量与总任务数量的比例",
        },
        {
            "id": 2,
            "name": "任务完成时间",
            "description": "任务运行分数是指任务在预期时间内完成的及时度",
        },
        {
            "id": 3,
            "name": "任务运行分数",
            "description": "任务运行分数",
        },
    ]

    @classmethod
    def get_evaluation(cls, search_ids):
        if search_ids == [0]:
            return Mock.evaluation
        elif search_ids:
            results = search_ids
            for search_id in search_ids:
                for entry in cls.evaluation:
                    if entry["id"] == search_id['id']:
                        search_id['name'] = entry['name']
                        break
            return results
        else:
            return []

    addon_error = [
        {
            "id": 1,
            "name": "继续流程",
        },
        {
            "id": 2,
            "name": "停止流程",
        },
        {
            "id": 3,
            "name": "继续流程但跳过故障插件",
        },
    ]

    @classmethod
    def get_addon_error(cls, id):
        if id != 0:
            return Mock.addon_error[id - 1]
        else:
            return Mock.addon_error

    run_error = [
        {
            "id": 1,
            "name": "全部实验运行结束再停止",
        },
        {
            "id": 2,
            "name": "故障率高于20%自动停止",
        },
        {
            "id": 3,
            "name": "出现一个就停止",
        },
    ]

    @classmethod
    def get_run_error(cls, id):
        if id != 0:
            return Mock.run_error[id - 1]
        else:
            return Mock.run_error

    constraint_setting = [
        {"id": 1, "name": "约束1"},
        {"id": 2, "name": "约束2"},
        {"id": 3, "name": "约束3"},
        {"id": 4, "name": "约束4"},
        {"id": 5, "name": "约束5"},
        {"id": 6, "name": "约束6"},
    ]

    @classmethod
    def get_constraint(cls, search_ids):
        if search_ids == [0]:
            return Mock.constraint_setting
        elif search_ids:
            results = []
            for search_id in search_ids:
                for entry in cls.constraint_setting:
                    if entry["id"] == search_id:
                        results.append(entry)
                        break
            return results
        else:
            return []

    evolution = [{"id": 1, "name": "正交实验"},{"id": 2, "name": "网格搜索"},{"id": 3, "name": "随机组合"}]

    @classmethod
    def get_evolution(cls, id):
        if id != 0:
            return Mock.evolution[id - 1]
        else:
            return Mock.evolution

    max_access_time = [
        {
            "id": 1,
            "name": "持续到场景结束",
        },
        {
            "id": 2,
            "name": "持续到实验结束",
        },
        {
            "id": 3,
            "name": "自定义",
        },
    ]

    @classmethod
    def get_max_access_time(cls, id):
        if id != 0:
            return Mock.max_access_time[id - 1]
        else:
            return Mock.max_access_time
