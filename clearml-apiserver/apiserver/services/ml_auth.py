from apiserver.apimodels.auth import (
    CreateUser<PERSON>equest,
    CreateUserResponse,
    EditUserReq,
    UpdateUserPasswordRequest,
    ResetUserRequest,
)
from apiserver.apimodels.base import UpdateResponse
from apiserver.config_repo import config
from apiserver.service_repo import APICall, endpoint
from apiserver.bll.bll.ml_auth.auth_user_bll import AuthUserBLL
from apiserver.bll.bll.ml_auth.login_bll import LoginBLL
from apiserver.bll.bll.ml_auth.verify_code import VerifyCode


log = config.logger(__file__)


@endpoint(
    "ml_auth.create_user",
    request_data_model=CreateUserRequest,
    response_data_model=CreateUserResponse
)
def create_user(call: APICall, *_, **__):
    AuthUserBLL.create_user(call=call)


@endpoint("ml_auth.get_all_user")
def get_all_user(call: <PERSON><PERSON><PERSON>, *_, **__):
    AuthUserBLL.user_list(call=call)


@endpoint("ml_auth.delete_user")
def delete_user(call: APICall, *_, **__):
    AuthUserBLL.delete_user(call=call)


@endpoint(
    "ml_auth.update_user",
    request_data_model=EditUserReq,
    response_data_model=UpdateResponse
)
def update_user(call: APICall,  *_, **__):
    AuthUserBLL.update_user(call=call)


@endpoint(
    "ml_auth.update_password",
    request_data_model=UpdateUserPasswordRequest,
    response_data_model=UpdateResponse
)
def update_user_password(call: APICall,  *_, **__):
    AuthUserBLL.update_user_password(call=call)


@endpoint(
    "ml_auth.reset",
    request_data_model=ResetUserRequest,
)
def rest_user(call: APICall,  *_, **__):
    AuthUserBLL.reset_user(call=call)
    pass


@endpoint("ml_auth.user_by_id")
def user_by_id(call: APICall, *_, **__):
    AuthUserBLL.user_by_id(call=call)


@endpoint("ml_auth.get_other_session")
def get_other_session(call: APICall, *_, **__):
    AuthUserBLL.get_other_session(call=call)

@endpoint("ml_auth.login")
def login(call: APICall, *_, **__):
    LoginBLL.login(call=call)


@endpoint("ml_auth.logout")
def logout(call: APICall, *_, **__):
    LoginBLL.logout(call=call)


# @endpoint("ml_auth.verify_code")
# def verify_code(call: APICall, *_, **__):
#     VerifyCode.get_verify_code(call=call)


# @endpoint("ml_auth.refresh_captcha_image")
# def refresh_captcha_image(call: APICall, *_, **__):
#     VerifyCode.refresh_captcha_image(call=call)
