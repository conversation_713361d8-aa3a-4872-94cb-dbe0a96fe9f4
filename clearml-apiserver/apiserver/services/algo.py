from typing import Sequence, Optional, Tuple

import attr
from mongoengine import Q

from apiserver.apierrors import errors
from apiserver.apimodels.base import UpdateResponse, MakePublicRequest, IdResponse
from apiserver.database.model.algo import (
    AlgoType,
    AlgoCategory,
    AlgoSource,
    Algo,
    AlgoRelease,
    AlgoRun,
    PublishStatus,
)
from apiserver.database.errors import translate_errors_context
from apiserver.service_repo import APICall, endpoint
from apiserver.database.utils import parse_from_call
from apiserver.services.utils import conform_tag_fields
from apiserver.utilities.algo_param_format import FileType, parse_file
from apiserver.bll.algo import AlgoBLL, AlgoReleaseBLL, AlgoRunBLL
from apiserver.apimodels.algo import (
    AlgoFileRequest,
    AlgoReadFileRequest,
    AlgoCreateVersionRequest,
    AlgoIDRequest,
    AlgoVersionRequest,
    FileListRequest,
    FileDeleteRequest,
    BaselineTestRequest,
    ReadFileRequest
)
import os,datetime,random
import time
from boto3.session import Session
import zipfile
import shutil
from threading import Timer
import threading
from apiserver.config_repo import config
from apiserver.utilities.harbor import harbor_cli
# from apiserver.utilities.faas import *
from flask import request as request_openapi
from flask import stream_with_context
import xml.etree.ElementTree as ET
import json
import boto3
from uuid import uuid4
import re
from apiserver.redis_manager import redman
import copy
from apiserver.database.utils import (
    parse_from_call,
    get_company_or_none_constraint,
)
from apiserver.database.model.project import Project
from apiserver.bll.project import ProjectBLL
from apiserver.config_repo import config
# from apiserver.utilities.sys_dict import get_dict_content
from apiserver.utilities.k8s import *
from apiserver.utilities.resource import *
from apiserver.utilities.s3 import *
from apiserver.utilities.algo_param_format import *
log = config.logger(__file__)


create_fields = {
    "id": None,
    "company":None,
    "user":None,
    "name": None,
    "description": None,
    # "type": None,
    "category": None,
    "source": None,
    "disabled": None
}

create_release_fields = {
    "algo": None,
    "version": None,
    "id": None,
    "args": None,
    "publish_status": None,
    "disabled": None,
    "description": None,
}

files_dict = {
        "name":None,
        "size":None,
        "descriptio":None
}

# 文件上传临时目录
UPLOAD_PREFIX = "/code/upload/"
ZIP_DIR = "/code/upload/zip"
CODE_DIR = "/code"

# TODO：后续可能需要基于用户划分仓库
ALGORITHM_HARBOR_PROJECT = "ai-algo"


@endpoint("algo.get_all")
def get_all(call: APICall, company_id, _):
    with translate_errors_context():
        status = []
        publish_status = call.data.get("publish_status", "")
        if publish_status:
            status.append(publish_status)
        else:
            status = ["published", "pulled_down"]    
        res_list = []
        for field in ["category", "source"]:
            if call.data.get(field) == '':
                call.data.pop(field, None)  
        page = call.data.get("page",0)
        page_size = call.data.get("page_size",10)
        call.data["order_by"]=["-last_update"]
        call.data["only_fields"] = [
        "name",
        "id",
        "last_update",
        ]
        call.data.pop("page", None)
        call.data.pop("page_size", None)

        log.info(f"get all call.data: {call.data}")
        res = Algo.get_many_with_join(
            company=company_id,
            query_dict=call.data,
        )
        # TODO:  优化这里的pipeline
        ids = [res_id.get("id") for res_id in res]
        log.info(f"ids: {ids}")
        pipeline = [
            {"$match": {"_id": {"$in": ids}}},
            {
                "$lookup": {
                    "from": "algo_release",
                    "let": {"algo_id": "$_id"},
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$algo", "$$algo_id"]},
                                        {"$in": ["$publish_status", status]}
                                    ]
                                }
                            }
                        },
                        {"$sort": {"last_update": -1}},
                        {"$project":{
                            "algo":1,
                            "version":1,
                            "publish_status":1,
                            # "last_update":1,
                            # "exec_config":1
                            }},
                    ],
                    "as": "release",
                }
            },
            {"$unwind": {"path": "$release", "preserveNullAndEmptyArrays": True}},
            {"$skip": page * page_size},
            {"$limit": page_size},
            # {"$match": {"disabled": False}},
        ]

        result = list(Algo.aggregate(pipeline))

        release_map = {}
        for item in result:
            algo_id = item["_id"]
            release = item.get("release")
            if not release:
                continue
            if algo_id in release_map:
                if release_map[algo_id].get("publish_status") == "published":
                    continue
                if release.get("publish_status") == "published":
                    release_map[algo_id] = release
            else:
                release_map[algo_id] = release
        res_list = []
        for res_id in res:
            res_id_with_release = res_id.copy()
            res_id_with_release["release"] = release_map.get(res_id.get("id"), [])
            if release_map.get(res_id.get("id"), []) == []:
                log.info(f"Algo without release : {res_id.get('id')}")
                continue
            res_list.append(res_id_with_release)
        total_count = len(res_list)
        # start_index = page * page_size
        # end_index = start_index + page_size
        paginated_list = res_list

        pager = {
            "page": page,
            "page_size": page_size,
            "count": total_count,
            "total_pages": (total_count // page_size) + (1 if total_count % page_size > 0 else 0)
        }
        call.result.data = {"pager": pager, "list": paginated_list}


@endpoint("algo.get_all_release")
def get_all_release(call: APICall, company_id, _):
    
    def get_release_readme(algo_id, version):
        target_dir = os.path.join(CODE_DIR, algo_id, version)
        if os.path.exists(target_dir):
            for file in os.listdir(target_dir):
                if file == "README.md":
                    # 读取文件
                    # log.info(f'readme file: {os.path.join(target_dir, file)} for algo {algo_id}-{version}')
                    with open(os.path.join(target_dir, file), "r") as f:
                        return f.read()
        return ""
    
    with translate_errors_context():
        res = AlgoRelease.get_many(
            company=company_id,
            query_dict=call.data,
        )
        # 判断是否只有一个版本
        if len(res) == 1:
            algo_id = call.data.get("algo")
            releases = AlgoRelease.get_many(
                company=company_id,
                query_dict={
                    "algo":algo_id
                }
            )
        for i in range(len(res)):
            res[i]["description"] = get_release_readme(res[i]["algo"], res[i]["version"])

        count = AlgoRelease.get_count(
            company=company_id,
            query_dict=call.data,
        )
        page = call.data.get("page", 0)
        page_size = call.data.get("page_size", 0)
        pager = {"page": page, "page_size": page_size, "count": count}

        call.result.data = {"pager": pager, "list": res}

@endpoint("algo.delete_release")
def delete_release(call: APICall, company: str, request: AlgoIDRequest):
    success = False
    version = call.data.get("version")
    algo_id = call.data.get("algo_id")
    algo_release = AlgoReleaseBLL.get_by_version(
            algo = algo_id,
            version = version,
            company_id = company
        )
    print("[DEBUG] : release_id = ",algo_release.id)
    res = AlgoReleaseBLL.delete(
            release_id = algo_release.id
    )    

    call.result.data = {
        "result" : success
    }
@endpoint("algo.get_baseline_dataset")
def get_dataset(call: APICall,company,_):
    call.result.data = {
        "datasets": [
            {
                "name":"set",
                "id" : '1',
            },
            {
                "name":"set2",
                "id" : '2',
            }
        ]
    }

@endpoint("algo.get_algo_type")
def get_dataset(call: APICall,company,_):
    call.result.data = {
        "tag": [
            {
                "name":"tag1"
            },
            {
                "name":"tag2"
            }
        ]
    }

# 导入算法
@endpoint(
    "algo.create",
    required_fields=["name"]
)
def create(call: APICall, company: str, _):
    result = Algo.get_many(
        company=company,parameters={"name":call.data.get("name")} ,query_dict={"name":call.data.get("name")}
    )

    exist_algo = None
    for algo in result:
        if algo.get("name") == call.data.get("name"):
            exist_algo = algo
            break
    try:
        if not exist_algo:
            with translate_errors_context():
                fields = parse_from_call(call.data, create_fields, Algo.get_fields())
                print()
                conform_tag_fields(call, fields, validate=True)
                algo_id=AlgoBLL.create(
                        user=call.identity.user,
                        company=company,
                        **fields,
                    )
        else:
            algo_id = exist_algo.get("id")

        version_res = AlgoRelease.get_many(
            company=company, parameters={"algo":algo_id}, query_dict={"algo":algo_id}
        )
        versions = [version_ids.get("version") for version_ids in version_res]
        version = call.data.get("release",{}).get("version")
        # 判断算法版本是否存在
        if version in versions:
            raise errors.bad_request.InvalidAlgoVersion()
            
        # 新增版本
        release_payload = call.data.get("release")
        # release_payload.update({"publish_status":"no_pass_baseline"})  #  新增版本都是未上线

        check_result = check_release(version)
        if check_result:
            max_version = get_max_version(versions)
            compare_result = compare_versions(version,max_version)
            if compare_result:
                fields = parse_from_call(
                    release_payload, create_release_fields, AlgoRelease.get_fields()
                )
                log.info(f'fields: {fields}')
                res = AlgoReleaseBLL.create(
                    user=call.identity.user,
                    company=company,
                    algo=algo_id,
                    publish_status=PublishStatus.pulled_down,
                    **fields,
                )
                set_current_algo(algo_id=algo_id,version=version,company=company)
                if not res:
                    # TODO: 增加错误信息
                    raise errors.bad_request.AlgoCreateFailed()
                    log.error(f'create algo release failed: {res}')
                    return
                # TODO: 处理文件  1）更新json中的字段 

                source_dir = os.path.join(UPLOAD_PREFIX, str(call.data.get("s3_id")))
                if os.path.exists(source_dir):
                    target_dir = os.path.join(CODE_DIR, algo_id, res.get("version"))
                    log.info(f'source_dir: {source_dir} target_dir: {target_dir}')
                    os.makedirs(target_dir, exist_ok=True)
        
                    try:
                        json_file = []
                        # 获取源目录下的所有文件和目录
                        for item in os.listdir(source_dir):
                            source_path = os.path.join(source_dir, item)
                            target_path = os.path.join(target_dir, item)
                            # 移动文件或目录
                            log.info(f'move {source_path} to {target_path}')
                            shutil.move(source_path, target_path)

                        # 更新json中的字段
                        log.info(f'format file {os.path.join(target_dir, "code/param.json")}')
                        format_file(FileType.JSON,  os.path.join(target_dir, "code/param.json"), content=release_payload.get("args",{}))

                        # 删除空的源目录
                        if os.path.exists(source_dir):
                            os.rmdir(source_dir)
                    except Exception as e:
                        raise errors.bad_request.FailedToMoveFiles(f"Failed to move files: {str(e)}")

                call.result.data = {
                    "algo_id": algo_id,
                    "version": version,
                }
            else:
                raise errors.bad_request.AlgoVersionNumberLow()
        else:
            raise errors.bad_request.InvalidAlgoVersionNumber()
    except errors.bad_request.InvalidAlgoVersion:
        raise errors.bad_request.InvalidAlgoVersion()
    

@endpoint("algo.upload_file")
def upload_file(call: APICall, company: str, request: FileListRequest):
    
    if not request.s3_id:
        request.s3_id = str(uuid4()).replace("-", "")
    
    File = request_openapi.files["file"]
    file_name = File.filename
    try:
        file_format = file_name.split(".", 1)[1]
    except:
        # raise errors.bad_request.BadFileRequest()
        file_format = 'raw'
    
    if  file_format.lower() == "zip":
        # 解压文件到 /code/upload/uuid
        upload_dir = os.path.join(UPLOAD_PREFIX, str(request.s3_id))
        os.makedirs(upload_dir, exist_ok=True)
        
        try:
            with zipfile.ZipFile(File, 'r') as zip_ref:
                # 检查zip文件是否包含恶意路径
                for file_info in zip_ref.infolist():
                    if file_info.filename.startswith('/') or '..' in file_info.filename:
                        raise errors.bad_request.InvalidZipFile("Zip file contains invalid paths")
                
                # 解压所有文件
                zip_ref.extractall(upload_dir)
        except zipfile.BadZipFile:
            raise errors.bad_request.InvalidZipFile("Invalid zip file format")
        except Exception as e:
            raise errors.bad_request.InvalidZipFile(f"Failed to extract zip file: {str(e)}")
    else:
        # 移动文件到 /code/upload/uuid
        upload_dir = os.path.join(UPLOAD_PREFIX, str(request.s3_id))
        os.makedirs(upload_dir, exist_ok=True)
        file_path = os.path.join(upload_dir, file_name)
        File.save(file_path)

    call.result.data = {
        "s3_id": str(request.s3_id),
    }


@endpoint("algo.file_list")
def file_list(call: APICall, company: str, request: FileListRequest):
    
    desc_map = {
        "code": "代码目录",
        "model": "模型目录",
        "infer": "推理目录",
        "README.md": "说明文档",
        "param.json": "参数配置",
    }

    def format_size(size):
        if size < 1024:
            return f"{size}B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.2f}KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / 1024 / 1024:.2f}MB"
        else:
            return f"{size / 1024 / 1024 / 1024:.2f}GB"

    # 返回 uuid下的文件列表和json内容（json内容需要跟前端对下）
    upload_dir = os.path.join(UPLOAD_PREFIX, str(request.s3_id))

    file_list = [file for file in os.listdir(upload_dir)]

    file_list_res = []

    for file in file_list:
        if file in desc_map:
            desc = desc_map[file]
        else: 
            desc = ""
        
        size = os.path.getsize(os.path.join(upload_dir, file))
        file_list_res.append({
            "name": file,
            "size": format_size(size),
            "description": desc,
        })

    content = parse_file(FileType.JSON, os.path.join(upload_dir, "code/param.json"))

    call.result.data = {
        "file_list": file_list_res,
        "args": content if content else {},
    }

@endpoint("algo.get_resource")
def get_resource(call: APICall, company: str, _):
    # 返回资源规格列表，TOOD: 从配置获取
    resource_list = [
        {
            "name": "1c2G",
            "cpu": 1,
            "memory": 2,
        },
        {
            "name": "2c4G",
            "cpu": 2,
            "memory": 4,
        },
        {
            "name": "4c8G",
            "cpu": 4,
            "memory": 8,
        },
        {
            "name": "8c16G",
            "cpu": 8,
            "memory": 16,
        },
    ]
    call.result.data = {
        "resource_list": resource_list
    }

@endpoint("algo.get_images")
def get_images(call: APICall, company: str, _):
    try:
        images = harbor_cli.get_image_names()
        call.result.data = {"images": images}
    except Exception as e:
        raise errors.bad_request.InvalidGetImages()


@endpoint("algo.get_image_tag")
def get_image_tag(call: APICall, company: str, _):
    image = call.data.get("image")
    tag = harbor_cli.get_tags(image)
    call.result.data = {"tag": tag}

@endpoint("algo.set_online")
def set_online(call: APICall, company: str, request: AlgoFileRequest):
    releases = AlgoRelease.get_many(
        company=company,
        query_dict={
            "algo":request.algo_id,
            "version":request.version
        }
    )
    
    cpu = call.data.get('cpu')
    memory = call.data.get('memory')
    image = call.data.get('image')
    version = call.data.get('tag') 
    # 构建镜像
    base_image = f'{image}:{version}'
    if image_build(base_image):
        log.info(f'build image {image} success')
    else:
        log.info(f'build image {image} error')
    if not cpu or not memory or not image or not version:
        raise errors.server_error.ResourceNotEnough(f'set online failed: {cpu} {memory} {image} {version}')
        return
        
    log.info(f'set online: {cpu} {memory} {image} {version}')
   
    try:
        # k8s操作
        image_name = f'{image}:{version}'
        if check_start_scripts(request.algo_id,request.version):
            create_deployment_object(image_name,request.algo_id,request.version,str(cpu),str(memory),str(company),"bash")
        else:
            create_deployment_object(image_name,request.algo_id,request.version,str(cpu),str(memory),str(company))
        for release in releases :
            update_dict = {"exec_config":f'{image}:{version}',"publish_status":PublishStatus.published}
            log.info(f'update {release["id"]} to published')
            AlgoReleaseBLL.update(
                company=company,
                release_id=release["id"],
                fields=update_dict
            ) 
    except Exception as e:
        # TODO:加上错误信息
        log.warn(f"create deployment failed ... {e}")
        return False
    call.result.data = {
        "status": True
    }

@endpoint("algo.set_offline")
def set_offline(call: APICall, company: str, request: AlgoFileRequest):
    # 算法下线 删除deploymentname = call.data.get("name")
    releases = AlgoRelease.get_many(
        company=company,
        query_dict={
            "algo":request.algo_id,
            "version":request.version
        }
    )
    try:
        delete_deployment(request.algo_id,request.version)
        for release in releases :
            update_dict = {"publish_status":PublishStatus.pulled_down}
            AlgoReleaseBLL.update(
                company=company,
                release_id=release["id"],
                fields=update_dict
            )
    except Exception as e:
        log.error(f'set offline failed: {e}')
        # TODO: 增加错误信息
    call.result.data = {
        "status": True
    }

# 下载算法
@endpoint("algo.download_release", required_fields=["algo_id"])
def download_release(call: APICall, company: str, request: AlgoFileRequest):
    algorithm_id = request.algo_id
    version = request.version
    s3_prefix = f"{s3_config.pvc}/{algorithm_id}/{str(version)}"
    zip_filename = f"{algorithm_id}_{version}.zip"
 
    try:
        response = s3_client.list_objects_v2(Bucket=s3_config.bucket, Prefix=s3_prefix)
        with zipfile.ZipFile(zip_filename, "w", zipfile.ZIP_DEFLATED) as zip_file:
            for obj in response["Contents"]:
                key = obj["Key"]
                key = key.rstrip('/')
                file_name = os.path.basename(key)
                temp_file_path = f"/tmp/{file_name}"
                s3_client.download_file(s3_config.bucket, key, temp_file_path)
                relative_path = os.path.relpath(key, s3_prefix)
                relative_path = version + "/" + relative_path
                zip_file.write(temp_file_path, relative_path)
                os.remove(temp_file_path)
        s3_client.upload_file(zip_filename, s3_config.bucket, zip_filename)
        download_url = s3_client.generate_presigned_url(
            "get_object", Params={"Bucket": s3_config.bucket, "Key": zip_filename}, ExpiresIn=3600
        )
        Timer(600,delete_file(zip_filename)).start()
        call.result.data = {"download_url": download_url}
    except Exception as e:
        log.info(f'download release failed: {e}')
    finally:
        if os.path.exists(zip_filename):
            os.remove(zip_filename)

# 删除算法
@endpoint("algo.delete")
def delete(call:APICall, company: str, request: AlgoIDRequest):
    # 确认数据库中有该算法
    result = Algo.get_many(
        company=company, 
        parameters={
            "id":request.algo_id,
            }
    )
    try:
        if not result:
            raise errors.bad_request.BadFileRequest(msg="算法不存在")
        else:
            # 删除算法
            del_by_algo = AlgoBLL.delete(
                company=company,
                algo_id=request.algo_id,
            )
    except Exception as e:
        raise errors.server_error.AlgoDeleteError(e)
    # 校验id
    target_dir = os.path.join(UPLOAD_PREFIX, str(request.algo_id))
    # target_dir = os.path.join(UPLOAD_PREFIX, str(request.algo_id),str(request.version))
    if os.path.exists(target_dir):
        try:
            shutil.rmtree(target_dir)
        except Exception as e:
            raise errors.bad_request.InvalidDeleteAlgo(f"Failed to delete algo: {str(e)}")
    else:
        print("Directory does not exist: ", target_dir)

# 获取算法运行状态、发布状态、版本
@endpoint("algo.get_status")
def get_alog_status(call: APICall, company: str, request: AlgoIDRequest):
    PUBLISH_STASUS_SUCCESS = "published"
    PUBLISH_STASUS_FAIL = "pulled_down"
    releases = AlgoRelease.get_many(
        company=company,
        query_dict={
             "algo":request.algo_id
        }
    )
    publish_status = False
    
    for release in releases:
        publish_status = release['publish_status']
        version = release['version']
        if publish_status == "published":
            publish_status = PUBLISH_STASUS_SUCCESS
            break
    # 获取算法运行状态
    namespace = config.get("apiserver.kubernetes.namespace", "tiduai")
    name = f'svc-{request.algo_id}-{version.replace(".", "-")}'
    running_status = get_deployment_status(name,namespace)
    call.result.data = {
        "publish_status": publish_status,
        "running_status": running_status
    }

# 算法演进状态路径
@endpoint("algo.get_version_compare",required_fields=["algo_id"])
def get_version_compase(call: APICall, company: str, request: AlgoIDRequest):

    def diff_args(args, previous_args):
        diff_list = []
        for key, value in args.items():
            if key in previous_args:
                value = value.get("value","")
                previous_value = previous_args.get(key, {}).get("value","")
                if value != previous_value:
                    log.info(f'diff_args: {key} {value} {previous_value}')
                    diff_list.append({
                        "name": key,
                        "value": value,
                        "previous": previous_value
                    })
        return diff_list

    # 获取算法版本列表
    releases = AlgoRelease.get_many(
        company=company,
        query_dict={
             "algo":request.algo_id
        }
    )
    releases.sort(key=lambda x: tuple(int(v) for v in x.get("version").split(".")))

    param_fields = ["input_train", "output_train","input_infer","output_infer"]
    changes = []
    log.info(f'releases: {[res.get("version") for res in releases]}')

    for i in range(0, len(releases)):
        diffs = {}
        args = releases[i].get("args")
        if i > 0:
            previous_args = releases[i-1].get("args")
            for field in param_fields:
                diff = diff_args(args.get(field, {}), previous_args.get(field, {}))
                if diff:
                    diffs[field] = diff

        changes.append({
            "version": releases[i].get("version"),
            "date": releases[i].get("created").strftime("%Y-%m-%d %H:%M:%S"),
            "changes": diffs
        })

    call.result.data = {
        "changes": changes
    }
    
@endpoint("algo.statistics")
def statistics(call: APICall, company: str, _):
    # 获取算法版本列表

    total = Algo.get_count(
        company=company,
        query_dict={}
    )

    online_list = list(AlgoRelease.aggregate(pipeline=[
        { "$match": { "publish_status": "published" } },
        { "$group": { 
            "_id": "$algo" 
            }
        },
        {
            "$count": "count"
        }
    ]))
    online_count = 0
    if online_list:
        online_count = online_list[0].get("count",0)
 
    log.info(f' total: {total} online_count: {online_count}')
    offline_count = total - online_count
    
    call.result.data = {
        "total": total,
        "online": online_count,
        "offline": offline_count
    }

# 获取算法详情
@endpoint("algo.get_by_id")
def get_by_id(call: APICall, company: str, request: AlgoIDRequest):
    algo_id = request.algo_id
    call.data["id"] = algo_id
    call.data["only_fields"] = [
        # "type.name",
        "name",
        "id",
        "last_update"
    ]
    res = Algo.get_by_id(
            company=company,
            algo=algo_id
        )
    pipeline = [
                {"$match": {"_id": algo_id }},
                {
                    "$lookup": {
                        "from": "algo_release",
                        "let": {"algo_id": "$_id"},
                        "pipeline": [
                            {
                                "$match": {
                                    "$expr": {
                                        "$and": [
                                            {"$eq": ["$algo", "$$algo_id"]},
                                        ]
                                    }
                                }
                            },
                            {"$sort": {"last_update": 1}}
                        ],
                        "as": "release",
                    }
                },
                {"$unwind": {"path": "$release", "preserveNullAndEmptyArrays": True}},
            ] 

    if res:
        result = list(Algo.aggregate(pipeline)) 
        
        res_dict = res.to_proper_dict()
        if result:
            log.info(f"pipeline result {len(result)} {result} ")
            res_dict["publish_status"] = "pulled_down"
            for release in result:
                if release["release"]["publish_status"] == "published":
                    res_dict["publish_status"] = "published"
                    break
        call.result.data = {"algo":res_dict}
    else:
        call.result.data = {"algo":{}}

@endpoint("algo.set_current")
def set_current(call: APICall, company: str, request: AlgoVersionRequest):
    set_current_algo(algo_id=request.algo_id,version=request.version,company=company)

# 更新算法描述
# TODO mdw格式
@endpoint("algo.update")
def update_description(call: APICall,company: str, request: AlgoVersionRequest):
    # 直接将整个文件保存下来
    # 获取存储位置
    target_dir = os.path.join(CODE_DIR,request.algo_id,request.version)
    release_payload = call.data.get("description")
    format_file(FileType.MD,  os.path.join(target_dir, "READEME.md"), content=release_payload)

# 更新算法参数
@endpoint("algo.update_param")
def update_description(call: APICall,company: str, request: AlgoVersionRequest):
    # 直接将整个文件保存下来
    # 获取存储位置
    target_dir = os.path.join(CODE_DIR,request.algo_id,request.version)
    release_payload = call.data.get("release")
    format_file(FileType.JSON,  os.path.join(target_dir, "code/param.json"), content=release_payload.get("args",{}))

# 流程: 训练 -> 获取新版本 -> 云存储
@endpoint("algo.storage_training_result")
def storage_training_result(call: APICall,company: str, request: AlgoVersionRequest):
    # 获取到旧的算法
    old_version = request.version
    algo_id = request.algo_id
    source_dir = os.path.join(CODE_DIR,algo_id)
    # 获取新版本
    version = increment_version(old_version)
    storge_algo(request.algo_id,old_version,version)
    target_dir = os.path.join(source_dir,version)
    fields = parse_from_call(
            release_payload, create_release_fields, AlgoRelease.get_fields()
        )
    try:
        format_file(FileType.JSON,  os.path.join(target_dir, "code/param.json"), content=release_payload.get("args",{}))
    except Exception as e:
        log.error(f'update algo erro {e}')
    # release 保存新版本
    AlgoReleaseBLL.create( 
        user=call.identity.user,
        company=company,
        algo=algo_id,
        version=version,
        publish_status=PublishStatus.pulled_down,
        **fields
    )

# 保存算法
def storge_algo(algo_id,old_verison,new_version):
     # 复制原文件
    source_dir = os.path.join(CODE_DIR,algo_id,old_verison)
    target_dir = os.path.join(CODE_DIR,algo_id,new_version)
    # 检查目标目录是否存在
    if os.path.exists(target_dir):
        log.info(f"Target directory already exists: {target_dir}")
    else:
        os.makedirs(target_dir)
        log.info(f"Created target directory: {target_dir}")

    # 复制 source_dir 中的所有内容到 target_dir
    for item in os.listdir(source_dir):
        source_path = os.path.join(source_dir, item)
        target_path = os.path.join(target_dir, item)

        if os.path.isdir(source_path):
            shutil.copytree(source_path, target_path)  # 递归复制目录
            log.info(f"Copied directory: {source_path} -> {target_path}")
        else:
            shutil.copy2(source_path, target_path)  # 复制文件并保留元数据
            log.info(f"Copied file: {source_path} -> {target_path}")

    log.info(f"All contents from {source_dir} copied to {target_dir}")

# 获取新版本
def increment_version(version):
    major, minor= map(int, version.split('.'))
    minor += 1
    if minor > 99:
        major += 1
        minor = 0
    return f"{major}.{minor}"

# 检查算法版本
def check_release(version):
    pattern = r"^[0-9]\d*\.[0-99]\d*$"
    if re.match(pattern, version):
        return True
    else:
        return False

def get_max_version(versions):
    if versions:
        versions.sort(key=lambda x:tuple(int(v) for v in x.split(".")), reverse=True)
        max_version = versions[0]
    else:
        max_version = "0.0"    
    return max_version 

def compare_versions(version1, version2):
    v1 = list(map(int, version1.split(".")))
    v2 = list(map(int, version2.split(".")))
    if v1 > v2:
        return True
    else:
        return False

def get_entry(name: str,size: str,description: str):
    return {
        "name": name,
        "size": size,
        "description": description
    }

# 校验是否存在启动脚本
def check_start_scripts(algo_id,version):
    target_dir = os.path.join(UPLOAD_PREFIX,algo_id,version)
    file_path = os.path.join(target_dir,"infer/endpoint.sh" )
    if os.path.exists(file_path):
        log.info(f'start scripts exists: {file_path}')
        return True
    else:
        log.info(f'start scripts not exists: {file_path}')
        return False

def check_algo(algo_id,company):
    releases = AlgoRelease.get_many(
        company=company,
        query_dict={
            "algo":request.algo_id
        }
    )
    if len(releases) == 1:
        return True
    else:
        return False

# 设置为当前版本
def set_current_algo(algo_id,version,company):
    current_release = get_current(algo_id,company)
    release = AlgoReleaseBLL.get_by_version(
        company_id=company,
        algo=algo_id,
        version=version
    )
    if current_release:
        log.info(f"need to delete {current_release.get('version')}")
        AlgoReleaseBLL.update(
            company=company,
            release_id=current_release.get('id'),
            fields={
                "current": False
            }
        )
        try:
            delete_deployment(algo_id,current_release.get('version'))
        except Exception as ex:
            log.info(f'delete deploy err {ex}')

    log.info(f'set current {release.version}')
    AlgoReleaseBLL.update(
        company=company,
        release_id=release.id,
        fields={
            "current": True
        }
    )

# 获取当前版本算法
def get_current(algo_id, company):
        current = AlgoRelease.get_many(
            company=company,
            query_dict={
                "algo": algo_id,
                "current": True
            },
        )
        if current:
            for cur in current:
                if cur.get('current') == True:
                    return cur
        return None