from apiserver.service_repo import APICall, endpoint
from apiserver.config_repo import config

log = config.logger(__file__)


@endpoint("sys.dict_list")
def sys_dict_list(call: APICall, company, _):

    log.info(f'-------------- {call.data.get("dictType")}')
    
    data_dict = {
        "algorithm_types":[{
            "dictCode": 100, 
            "dictValue": "task_plan", 
            "dictLabel": "机器学习", 
            },
            {
            "dictCode": 200, 
            "dictValue": "type_2", 
            "dictLabel": "强化学习", 
            }],
        "algorithm_category":[{
            "dictCode": 100, 
            "dictValue": "normal", 
            "dictLabel": "传统机器学习", 
            },
            {
            "dictCode": 200, 
            "dictValue": "type_2", 
            "dictLabel": "深度学习", 
            }],
        "algorithm_source":[{
            "dictCode": 100, 
            "dictValue": "kd", 
            "dictLabel": "梯度", 
            }],
        "algorithm_status":[{
            "dictCode": 100, 
            "dictValue": "published", 
            "dictLabel": "已上线", 
            },
            {
            "dictCode": 200, 
            "dictValue": "pulled_down", 
            "dictLabel": "已下线", 
            }]

        }

    data = data_dict.get(call.data.get("dictType"))
    call.result.data = {
    "code": 200,  
    "msg": "cccc",        
    "rows": data,
    }

@endpoint("sys.dict_type_satellite_type")
def dict_typesatellite_type(call: APICall, company, _):
    # data = data_dict.get(call.data.get("dictType"))
    call.result.data = {
    "code": 200,  
    "msg": "cccc",        
    "data": [{
            "dictCode": 100, 
            "dictValue": "type_1", 
            "dictLabel": "类型1", 
            },
            {
            "dictCode": 200, 
            "dictValue": "type_2", 
            "dictLabel": "类型2", 
            }]
    }
@endpoint("sys.dict_type_station_type")
def dict_typestation_type(call: APICall, company, _):
    # data = data_dict.get(call.data.get("dictType"))
    call.result.data = {
    "code": 200,  
    "msg": "cccc",        
    "data": [{
            "dictCode": 100, 
            "dictValue": "type_1", 
            "dictLabel": "类型1", 
            },
            {
            "dictCode": 200, 
            "dictValue": "type_2", 
            "dictLabel": "类型2", 
            }]
    }

@endpoint("sys.dict_type_target_classify")
def dict_typetarget_classify(call: APICall, company, _):
    # data = data_dict.get(call.data.get("dictType"))
    call.result.data = {
    "code": 200,  
    "msg": "cccc",        
    "data": [{
            "dictCode": 100, 
            "dictValue": "type_1", 
            "dictLabel": "类型1", 
            },
            {
            "dictCode": 200, 
            "dictValue": "type_2", 
            "dictLabel": "类型2", 
            }]
    }

@endpoint("sys.dict_type_target_type")
def dict_typetarget_type(call: APICall, company, _):
    # data = data_dict.get(call.data.get("dictType"))
    call.result.data = {
    "code": 200,  
    "msg": "cccc",        
    "data": [{
            "dictCode": 100, 
            "dictValue": "type_1", 
            "dictLabel": "类型1", 
            },
            {
            "dictCode": 200, 
            "dictValue": "type_2", 
            "dictLabel": "类型2", 
            }]
    }

@endpoint("sys.dict_type_work_ability")
def dict_typework_ability(call: APICall, company, _):
    # data = data_dict.get(call.data.get("dictType"))
    call.result.data = {
    "code": 200,  
    "msg": "cccc",        
    "data": [{
            "dictCode": 100, 
            "dictValue": "type_1", 
            "dictLabel": "类型1", 
            },
            {
            "dictCode": 200, 
            "dictValue": "type_2", 
            "dictLabel": "类型2", 
            }]
    }
@endpoint("sys.dict_type_task_type")
def dict_type_task_type(call: APICall, company, _):
    # data = data_dict.get(call.data.get("dictType"))
    call.result.data = {
    "code": 200,  
    "msg": "cccc",        
    "data": [{
            "dictCode": 100, 
            "dictValue": "type_1", 
            "dictLabel": "类型1", 
            },
            {
            "dictCode": 200, 
            "dictValue": "type_2", 
            "dictLabel": "类型2", 
            }]
    }
