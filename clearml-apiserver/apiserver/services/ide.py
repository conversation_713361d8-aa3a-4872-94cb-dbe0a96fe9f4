from typing import Sequence, Optional, Tuple
from mongoengine import Q
from apiserver.service_repo import APICall, endpoint
from apiserver.database.errors import translate_errors_context
import apiserver.database.errors
from apiserver.database.model.ide import Ide
from apiserver.apimodels.ides import(
    IdeRequest,
    IdeGetRequest,
    IdeCreateRequest,
    IdeUpdateRequest,
)
from apiserver.database.utils import (
    parse_from_call,
    get_company_or_none_constraint,
)
from apiserver.database.model import EntityVisibility
from apiserver.apimodels.base import UpdateResponse, IdResponse
from apiserver.utilities.git import get_projects
from apiserver.utilities.harbor import harbor_cli
from apiserver.utilities.xml import generate_dataset_xml
from apiserver.bll.ide import IdeBLL
from apiserver.bll.algo import AlgoBLL
import time
import gitlab
from apiserver.config_repo import config

log = config.logger(__file__)
ide_bll = IdeBLL()
algo_bll = AlgoBLL()

create_fields = {
    "name": None,
    "tags": list,
    "description": None,
    "algorithms": list,
    "image": None,
    "cpu": None,
    "gpu": None,
    "mem": None,
    "status": None,
    "url_vscode": None,
    "url_jupyter": None,
    "is_public": None,
    "resourceId": None,
}

# 适配算法仓的IDE实现，名称与算法名一致，将一个算法的所有版本算法都挂到IDE中

@endpoint("ides.get_by_id")
def get_by_id(call: APICall, company: str, request: IdeRequest):
    ide_id = request.ide
    ide = ide_bll.get_by_id(ide_id=ide_id, company_id=company)
    
    print(f'get ide{ide}')
    call.result.data = {"ide":ide.to_proper_dict()}

def get_by_name(name: str,company: str):
    return ide_bll.get_by_name(name = name,company_id=company)

@endpoint("ides.get_by_algo")
def get_by_algo(call: APICall, company: str, _):
    try:
        ide = ide_bll.get_by_algo(algo=call.data['algo'], company_id=company)
    except:
        ide = None
    call.result.data = {"ide": ide.to_proper_dict()}

@endpoint("ides.stop")
def stop(call: APICall, company: str, _):
    algo_id = call.data['algo_id']
    print(f'stop ide_ids {ide_id}')

    ide = get_by_name(algo_id,company=company)
    count = ide_bll.stop(ide.id,company_id=company)
    
    call.result.data = {"stop_count":count}


@endpoint("ides.start")
def stop(call: APICall, company: str, _):
    algo_id = call.data['algo_id']
    identity = call.identity
    ide = get_by_name(algo_id,company=company)

    # 数据集：直接将所有数据集挂在到Ide的路径下去，用于满足指标
    if not ide:
        # 使用数据集ID先生成xml文件
        # body = {"scenarioCodeList":[call.data['dataset_id']]}
        # res = generate_dataset_xml(body)
        # if res.get('code') != 200 :
        #     raise errors.bad_request.XmlGenerateFailed(extra_msg=dataset.get("id"))  
        # res_data = res.get("data", {})
        # if res_data:
        #     for res in res_data:
        #         dataset_path = res.get("inputPath").replace('xml/', '').replace('/data','')
        #     log.info(f'-----------dataset {res}')

        with translate_errors_context():
            ide = ide_bll.create(
                company_id=company,
                user=identity.user,
                name=algo_id,
                description=f'Ide for {algo_id}',
                cpu=2,
                mem=4096, 
                gpu=0,
                algos=[algo_id],
                image = "hub.tiduyun.com:5000/ide/clearml-vscode:v1",  #TODO：使用配置获取
                is_public="public",
                resourceId = 1,
                dataset_path=''
            )

    count = ide_bll.start(ide.id,company_id=company)
    update_algo_status(company_id=company,ide_id=ide.id,status='tostart')
    
    call.result.data = {"start_count":count}

@endpoint("ides.delete")
def stop(call: APICall, company: str, _):
    ide_id = call.data['ide']
    print(f'delete ide_ids {ide_id}')

    count = ide_bll.delete(ide_id, company_id=company)
    call.result.data = {"delete_count":count}

@endpoint("ides.update", response_data_model=UpdateResponse)
def update(call: APICall, company_id, request: IdeRequest):
    fields = parse_from_call(
        call.data, create_fields, Ide.get_fields(), discard_none_values=False
    )
    update_dict = ide_bll.update(company_id, request.ide, **fields)

    if fields.get('status'):

        ide = ide_bll.get_by_id(ide_id=request.ide, company_id=company_id)
        if ide.status is 'available' and fields.get('status') is 'running':
            call.result.data_model = UpdateResponse(updated=fields, fields=fields)

        #同步更新算法的状态
        update_algo_status(company_id=company_id,ide_id=request.ide,status=fields.get('status'),url=fields.get('url_vscode'))

    print(f'updated ide: {update_dict}')

    # 更新算法仓的字段
    call.result.data_model = UpdateResponse(updated=update_dict, fields=fields)

def update_algo_status(company_id, ide_id, status, url=None):
    ide = ide_bll.get_by_id(company_id=company_id,ide_id=ide_id)
    update_dict = {'ide_status': status}
    if url:
        update_dict['ide_url'] = url
    algo_bll.update(company_id,ide.algorithms[0], **update_dict)

@endpoint("ides.get_all")
def get_all(call: APICall, company_id: str, request: IdeGetRequest):
    data = call.data

    ret_params = {}

    ides: Sequence[dict] = Ide.get_many(
        company=company_id,
        query_dict=data,
        parameters=call.data,
        ret_params=ret_params,
    )
    count = Ide.get_count(
            company=company_id,
            query_dict=call.data,
        )
    page = call.data.get("page", 0)
    page_size = call.data.get("page_size", 0)
    pager = {"page": page, "page_size": page_size, "count": count}

    call.result.data = {"ides": ides, "pager":pager, **ret_params}


# 新建IDE
@endpoint("ides.create", response_data_model=IdResponse,)
def create(call:APICall, company: str, create_request:IdeCreateRequest):
    identity = call.identity
    data = call.data
    with translate_errors_context():
        ide = ide_bll.create(
            company_id=company,
            user=identity.user,
            name=create_request.algorithm,
            description=create_request.description,
            cpu=create_request.cpu,
            mem=create_request.mem, 
            gpu=create_request.gpu,
            algos=[create_request.algorithm],
            image = create_request.image,
            is_public=create_request.is_public,
            resourceId = create_request.resourceId
        )
    call.result.data_model = IdResponse(id=ide.id)

@endpoint("ides.get_images")
def get_images(call:APICall, company: str, _):
    images = harbor_cli.get_images()
    call.result.data = {"images": images}

@endpoint("ides.get_image_tags")
def get_image_tags(call:APICall, company: str, _):
    print(f'ides.get_image_tags {call.data}')
    tags = harbor_cli.get_tags(call.data['image_name'])
    call.result.data = {"tags": tags}

@endpoint("ides.statistics")
def statistics(call:APICall, company: str, _):
    public = Ide.get_count(company=company, query_dict = {"is_public":"public"})
    private = Ide.get_count(company=company, query_dict = {"is_public":"private"})
    preset = Ide.get_count(company=company, query_dict = {"is_public":"preset"})
    ret = {'public':public, 'private':private, 'preset':preset}
    call.result.data = {"statistics": ret}

# @endpoint("ides.timeout_ides")
# def timeout_ides(call:APICall, company: str, _):
#     Ide.get_many()

@endpoint("ides.get_algos")
def get_algos(call:APICall, company: str, _):
    gl = gitlab.Gitlab(url= config.get("apiserver.git.address"), private_token=config.get("apiserver.git.private_token"))
    projects = gl.projects.list(iterator=True)
    res = [p.path_with_namespace for p in projects]
    call.result.data={"list":res}

@endpoint("ides.get_resource")
def get_resource(call:APICall, company: str, _):
    resource1 ={
        "id":0,
        "name":"1核2g",
        "cpu":1,
        "gpu":0,
        "mem":2048
    }
    resource2 ={
        "id":1,
        "name":"2核4g",
        "cpu":2,
        "gpu":0,
        "mem":4096
    }
    resource3 ={
        "id":2,
        "name":"4核8g",
        "cpu":4,
        "gpu":0,
        "mem":8192
    }
    resource4 ={
        "id":3,
        "name":"8核16g",
        "cpu":8,
        "gpu":0,
        "mem":16384
    }
    resource5 ={
        "id":4,
        "name":"16核32g",
        "cpu":16,
        "gpu":0,
        "mem":32768
    }
    res = [resource1,resource2,resource3,resource4,resource5]
    call.result.data={"list":res}