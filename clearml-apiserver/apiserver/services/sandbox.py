from apiserver.config_repo import config
from apiserver.service_repo import APICall, endpoint
from apiserver.database.errors import translate_errors_context
from apiserver.database.model.task.task import Task
from apiserver.database.model.project import Project, Status
from apiserver.database.model.algo import Algo, AlgoRelease
from apiserver.apimodels.projects import ProjectRequest
import datetime
from mongoengine import Q
from apiserver.database.utils import (
    parse_from_call,
    get_company_or_none_constraint,
)

log = config.logger(__file__)

@endpoint("sandbox.task_statistics")
def task_statistics(call: APICall, company_id, _):
    with translate_errors_context():
        start_time = (datetime.datetime.now() - datetime.timedelta(days = 3600))
        periodic_time = (datetime.datetime.now() - datetime.timedelta(days = 7))
        cmp_projects = Project.get_many(company=company_id, query_dict = {"type":"cmp"})
        cmp_task = 0
        cmp_task_change = 0
        for cmp_project in cmp_projects:
            project_id = cmp_project.get("id")
            cmp_task = Task.get_count(company=company_id, query_dict={"project":project_id}) + cmp_task
            cmp_task_change = len(Task.get_many(company=company_id, query_dict = {"project":"project_id", "created": [start_time, periodic_time]})) + cmp_task_change
        completed = Task.get_count(company=company_id, query_dict={"status":"completed"})
        change_completed = len(Task.get_many(company=company_id, query_dict = {"status":"completed", "created": [start_time, periodic_time]}))
        all_task = Task.get_count(company=company_id, query_dict={})
        all_task_change = len(Task.get_many(company=company_id, query_dict = {"created": [start_time, periodic_time]}))

        all_release = AlgoRelease.get_count(company=company_id)
        publish_release = AlgoRelease.get_count(company=company_id, query_dict={"from_project":True})
        release_change = len(AlgoRelease.get_many(company=company_id, query_dict = {"from_project":True, "created": [periodic_time, datetime.datetime.now()]}))
        call.result.data = {
            "statistics": {
                "cmp_task": {
                    'count': cmp_task,
                    'change': {
                        'value': cmp_task - cmp_task_change,
                        'trend': 'increase' if cmp_task - cmp_task_change > 0 else ('decrease' if cmp_task - cmp_task_change < 0 else '')
                    }
                },
                "completed": {
                    'count': completed,
                    'change': {
                        'value': completed - change_completed,
                        'trend': 'increase' if completed - change_completed > 0 else ('decrease' if completed - change_completed < 0 else '')
                    }
                },
                "all_task": {
                    'count': all_task,
                    'change': {
                        'value': all_task - all_task_change,
                        'trend': 'increase' if all_task - all_task_change > 0 else ('decrease' if all_task - all_task_change < 0 else '')
                    }
                },
                "publish_rate": {
                    'count': str(int(publish_release / all_release * 100)) + "%",
                    'change': {
                        'value': release_change,
                        'trend': 'increase'
                    }
                }
            }
        }


@endpoint("sandbox.project_statistics")
def project_statistics(call: APICall, company_id, _):
    project_count = Project.get_count(company=company_id, query_dict = {}) 
    project_running = Project.get_count(company=company_id, query_dict = {'status':"running"}) 
    project_finished = Project.get_count(company=company_id, query_dict = {'status':'finished'}) 
    project_stopped = Project.get_count(company=company_id, query_dict = {'status':'stopped'}) 

    project_normal = Project.get_count(company=company_id, query_dict = {'type':'normal'}) 
    project_qa = Project.get_count(company=company_id, query_dict = {'type':'qa'}) 

    call.result.data = {"statistics":{
        "status":{
            'count':project_count,
            'running':project_running,
            'finished':project_finished,
            'stopped':project_stopped
        },
        "type":{
            'count':project_count,
            'normal':project_normal,
            'qa':project_qa
        },
        "scene":{
            'count':1,
            'list':['主中心']
        }
    }}

@endpoint("sandbox.get_project_status")
def get_project_status(call: APICall, company_id, _):
    all_status = [
        {"name":"未开始", "value":"created"},
        {"name":"待运行", "value":"pending"},
        {"name":"运行中", "value":"running"},
        {"name":"运行成功", "value":"finished"},
        {"name":"运行失败", "value":"failed"},
        {"name":"已停止", "value":"stopped"}
    ]
    call.result.data = {'list':all_status}

@endpoint("sandbox.get_all_project")
def get_all_project(call: APICall, company_id, _):
    query_dict = {
        "only_fields": ["name", "id", "status", "type", "configs"]
    }
    for field in ["status", "type", "name"]:
        if call.data.get(field) == '':
            call.data.pop(field, None) 
    query_dict.update(call.data)
    projects = Project.get_many(company=company_id, query_dict=query_dict, parameters = query_dict)
    count = Project.get_count(
        company=company_id,
        query_dict=call.data,
    )
    page = call.data.get("page", 0)
    page_size = call.data.get("page_size", 0)
    pager = {"page": page, "page_size": page_size, "count": count}
    for project in projects:
        scene_list = project['configs']['train_data']['history_data']['scene']
        project['scene'] = "主中心"
        project['scene_list'] = scene_list
        del project['configs']
    call.result.data = {'list':projects, "pager": pager}


@endpoint("sandbox.get_task_by_project")
def get_task_by_project(call: APICall, company_id, request:ProjectRequest):
    project = Project.get_many(company=company_id, query_dict={"id":request.project})
    if not project:
        call.result.data = {'list':[]}
        return
    project_algos = project[0]['configs']['configuration']['algos']
    project_algo = project[0]['configs']['configuration']['algo']
    if project_algo.get("algo_id", ""):
        project_algos.append(project_algo)
    for project_algo in project_algos:
        algo_id = project_algo.get("algo_id", "")
        del project_algo['hyperparams']
        if algo_id:
            query = Q(id=algo_id) & get_company_or_none_constraint(company_id)
            algo = Algo.objects(query).first().to_proper_dict()
            project_algo['name'] = algo['name']
        else:
            project_algos.remove(project_algo)   
 
    query_dict = {
        "project":request.project,
        "only_fields":["name", "id", "status","comment","algorithm", "last_metrics"]
    }
    tasks = Task.get_many(company=company_id, query_dict=query_dict, parameters = query_dict)
    running_tasks = Task.get_count(company=company_id, query_dict={"status": ['in_progress', 'queued', 'created'], "project":request.project})
    for task in tasks:
        query = Q(id=task['algorithm']['id']) & get_company_or_none_constraint(company_id)
        algo = Algo.objects(query).first().to_proper_dict()
        algo_name = algo['name']

        last_metrics = task.get("last_metrics", {})
        complete_rate = 0
        complete_time = 0
        for metric_id, metric_data in last_metrics.items():
                for sub_metric_id, sub_metric_data in metric_data.items():
                    metric_name = sub_metric_data.get('metric')
                    variant_name = sub_metric_data.get('variant')
                    value = sub_metric_data.get('value', 0)
                    if metric_name == 'Summary' and variant_name == 'complete_rate':
                        complete_rate = int(value)
                    elif metric_name == 'Summary' and variant_name == 'complete_time':
                        complete_time = int(value)

        del task['last_metrics']
        task["algorithm"]['name'] = algo_name
        task['complete_rate'] = complete_rate
        task['complete_time'] = complete_time

    if project[0].get('finished'):
        run_time = project[0].get('finished') - project[0].get('started')
    else:
        run_time = datetime.datetime.now() - project[0].get('started')
    rt = str(run_time).split(".")[0]
    call.result.data = {'list':tasks, 'algo':project_algos, 'running_task':str(running_tasks), 'run_time':rt}