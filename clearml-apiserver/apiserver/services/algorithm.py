from typing import Sequence, Optional, <PERSON><PERSON>

import attr
from mongoengine import Q

from apiserver.apierrors import errors
from apiserver.apimodels.base import UpdateResponse, MakePublicRequest, IdResponse
from apiserver.database.model.algorithm import Algorithm
from apiserver.database.errors import translate_errors_context
from apiserver.service_repo import APICall, endpoint
from apiserver.database.utils import parse_from_call
from apiserver.services.utils import conform_tag_fields
from apiserver.bll.algorithm import AlgorithmBLL
from apiserver.apimodels.algorithm import (
    AlgoRequest,
    AlgoUpdateRequest,
    AlgoFileUploadRequest,
    AlgoGitUrlRequest,
    AlgoFileDeleteRequest,
    AlgoFileBatchDeleteRequest,
    AlgoCreateVersionRequest,
    AlgoFileRequest,
    AlgoReadFileRequest
)
import os
from boto3.session import Session
import zipfile
import shutil
import gitlab
import git
from git import Repo, exc
from threading import Timer
from apiserver.config_repo import config
from apiserver.utilities.harbor import harbor_cli
from flask import request as request_openapi

create_fields = {
    "name": None,
    "description": None,
    "id": None,
    "algorithm_type": None,
    "is_public": None,
    "is_inference": None,
    "image": None,
    "model_format": None,
}
algo_bll = AlgorithmBLL()


@endpoint("algorithm.get_images")
def get_images(call: APICall, company: str, _):
    images = harbor_cli.get_images()
    call.result.data = {"images": images}


@endpoint(
    "algorithm.create",
    required_fields=["name"],
    response_data_model=IdResponse,
)
def create(call: APICall, company: str, _):
    identity = call.identity
    with translate_errors_context():
        fields = parse_from_call(call.data, create_fields, Algorithm.get_fields())
        conform_tag_fields(call, fields, validate=True)
        res = IdResponse(
            id=AlgorithmBLL.create(
                user=identity.user,
                company=company,
                **fields,
            )
        )
        return res


@endpoint("algorithm.get_all")
def get_all(call: APICall, company_id, _):
    with translate_errors_context():
        pvc = config.get("apiserver.s3.algorithm_pvc")
        res = Algorithm.get_many(
            company=company_id, parameters=call.data, query_dict=call.data
        )
        count = Algorithm.get_count(
            company=company_id,
            query_dict=call.data,
        )
        page = call.data.get("page", 0)
        page_size = call.data.get("page_size", 0)
        pager = {"page": page, "page_size": page_size, "count": count}

        call.result.data = {"pager": pager, "list": res}


@endpoint(
    "algorithm.update",
    required_fields=["algorithm_id"],
    request_data_model=AlgoUpdateRequest,
)
def update(call: APICall, company_id, request: AlgoRequest):
    fields = parse_from_call(
        call.data, create_fields, Algorithm.get_fields(), discard_none_values=False
    )
    AlgorithmBLL.update(company_id, request.algorithm_id, **fields)
    call.result.data = fields


@endpoint(
    "algorithm.delete",
    required_fields=["algorithm_id"],
)
def delete(call: APICall, __, request: AlgoRequest):
    res = AlgorithmBLL.delete(request.algorithm_id)
    if res == 1:
        call.result.data = {"delete_algorithm": request.algorithm_id}


@endpoint("algorithm.statistics")
def statistics(call: APICall, company: str, _):
    public = Algorithm.get_count(company=company, query_dict={"is_public": "public"})
    private = Algorithm.get_count(company=company, query_dict={"is_public": "private"})
    preset = Algorithm.get_count(company=company, query_dict={"is_public": "preset"})
    ret = {"public": public, "private": private, "preset": preset}
    call.result.data = {"statistics": ret}


@endpoint("algorithm.upload_file")
def upload_file(call: APICall, company: str, request: AlgoFileUploadRequest):
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")

    UPLOAD_FOLDER = "/home/<USER>/files/"
    if not os.path.exists(UPLOAD_FOLDER):
        # 目录不存在，创建目录
        os.makedirs(UPLOAD_FOLDER)

    algorithm_name = request.algorithm_id
    version = request.version
    dir_path = request.dir_path

    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)
    s3_prefix = f"{pvc}/{algorithm_name}/{str(version)}"
    s3_path = os.path.join(s3_prefix, dir_path)

    File = request_openapi.files["file"]
    file_name = File.filename
    file_format = file_name.split(".", 1)[1]
    local_file = os.path.join(UPLOAD_FOLDER, file_name)

    if File:
        File.save(local_file)
        print(f"Saved {file_name} to {local_file}")
    else:
        print("Failed to save!")

    if file_format.lower() == "zip":
        """上传zip压缩文件"""
        file_path = os.path.dirname(local_file)
        extract_dir_name = os.path.splitext(local_file)[0]
        print("file_path:", file_path)
        print("extract_dir_name:", extract_dir_name)
        if not os.path.exists(extract_dir_name):
            # 目录不存在，创建目录
            os.makedirs(extract_dir_name)
        try:
            with zipfile.ZipFile(local_file, "r") as zfp:
                with support_gbk(zfp) as supported_zfp:
                    supported_zfp.extractall(extract_dir_name)
        except Exception as e:
            print(e)
        for root, _, files in os.walk(extract_dir_name):
            for file in files:
                local_file_name = os.path.join(root, file)
                key_dir = os.path.relpath(local_file_name, file_path)
                s3_key = f"{s3_path}{key_dir}"
                try:
                    s3_client.upload_file(
                        Filename=local_file_name, Bucket=bucket, Key=s3_key
                    )
                except Exception as e:
                    print(f"Failed to upload {file} : {e}")
        try:
            res = s3_client.list_objects(
                Bucket=bucket, Prefix=f"{s3_path}{os.path.basename(extract_dir_name)}/"
            )
            upload_files_list = [item["Key"] for item in res["Contents"]]
        except Exception as e:
            print(e)

        print(f"Delete tmp directory:{extract_dir_name}")
        if os.path.exists(extract_dir_name):
            if os.path.exists(local_file):
                """删除本地zip文件"""
                os.remove(local_file)
            shutil.rmtree(extract_dir_name)  # 删除zip文件解压后的文件夹
        size, version_num = calculate_algorithm_size(algorithm_name)
        AlgorithmBLL.update_mongo(company, algorithm_name, size, version_num)
        call.result.data = {"upload_files": upload_files_list}
    else:
        """上传单个文件"""
        try:
            s3_client.upload_file(
                Filename=local_file, Bucket=bucket, Key=f"{s3_path}{file_name}"
            )
        except Exception as e:
            print(f"Failed to upload {file_name} : {e}")

        if os.path.exists(local_file):
            os.remove(local_file)
        size, version_num = calculate_algorithm_size(algorithm_name)
        AlgorithmBLL.update_mongo(company, algorithm_name, size, version_num)
        call.result.data = {"upload_files": f"{s3_path}{file_name}"}


@endpoint("algorithm.get_algos")
def get_algos(call: APICall, company: str, _):
    gl = gitlab.Gitlab(
        url=config.get("apiserver.git.address"),
        private_token=config.get("apiserver.git.private_token"),
    )
    projects = gl.projects.list(iterator=True)
    res = [p.path_with_namespace for p in projects]
    call.result.data = {"list": res}


@endpoint("algorithm.upload_git")
def upload_file(call: APICall, company: str, request: AlgoGitUrlRequest):
    gitlab_url = config.get("apiserver.git.pull_url")
    git_url = call.data.get("git_url")
    repo_url = f"{gitlab_url}{git_url}.git"
    r = "/home/<USER>/files/"
    repo_dir = f"/home/<USER>/files"
    if not os.path.exists(repo_dir):
        os.makedirs(repo_dir)
    git.Repo.clone_from(repo_url, repo_dir)

    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")
    algorithm_name = request.algorithm_id
    version = request.version
    dir_path = ""

    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)
    s3_prefix = f"{pvc}/{algorithm_name}/{str(version)}"
    s3_path = os.path.join(s3_prefix, dir_path)

    for root, _, files in os.walk(repo_dir):
        for file in files:
            local_file_name = os.path.join(root, file)
            key_dir = os.path.relpath(local_file_name, r)
            s3_key = f"{s3_path}{key_dir}"
            try:
                s3_client.upload_file(
                    Filename=local_file_name, Bucket=bucket, Key=s3_key
                )
            except Exception as e:
                print(f"Failed to upload {file} : {e}")
    try:
        res = s3_client.list_objects(Bucket=bucket, Prefix=f"{s3_path}")
    except Exception as e:
        print(e)
    shutil.rmtree(r)
    upload_files_list = [item["Key"] for item in res["Contents"]]
    size, version_num = calculate_algorithm_size(algorithm_name)
    AlgorithmBLL.update_mongo(company, algorithm_name, size, version_num)
    call.result.data = {"upload_files": upload_files_list}


@endpoint("algorithm.delete_file")
def delete_file(call: APICall, company: str, request: AlgoFileDeleteRequest):
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")

    algorithm_id = request.algorithm_id
    file_name = request.file_name
    version = request.version

    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)

    s3_prefix = f"{pvc}/{algorithm_id}/{str(version)}"

    if len(os.path.basename(file_name).split(".", 1)) > 1:
        ############删除单个文件###########
        try:
            s3_client.delete_object(Bucket=bucket, Key=f"{s3_prefix}/{file_name}")
        except Exception as e:
            print(e)
        call.result.data = {"delete_file": os.path.basename(file_name)}
    else:
        ############删除一个文件夹文件###########
        delete_objects = list()
        try:
            res = s3_client.list_objects(
                Bucket=bucket, Prefix=f"{s3_prefix}{file_name}/"
            )
            print(bucket)
            print(f"{s3_prefix}{file_name}/")
            delete_objects = [{"Key": item["Key"]} for item in res["Contents"]]
            try:
                s3_client.delete_objects(
                    Bucket=bucket, Delete={"Objects": delete_objects}
                )
            except Exception as e:
                print(e)
        except Exception as e:
            print(e)
        size, version_num = calculate_algorithm_size(algorithm_id)
        AlgorithmBLL.update_mongo(company, algorithm_id, size, version_num)
        call.result.data = {"delete_directory": f"{s3_prefix}{file_name}/"}


@endpoint("algorithm.download_file")
def download_file(call: APICall, company: str, request: AlgoFileDeleteRequest):
    DOWNLOAD_DIR = "/home/<USER>"
    # 检查目录是否存在
    if not os.path.exists(DOWNLOAD_DIR):
        # 目录不存在，创建目录
        os.makedirs(DOWNLOAD_DIR)

    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")

    algorithm_id = request.algorithm_id
    file_name = request.file_name
    version = request.version

    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)

    s3_prefix = f"{pvc}/{algorithm_id}/{str(version)}"

    if len(os.path.basename(file_name).split(".", 1)) > 1:
        print("Download a file")
        #####下载单个文件########
        try:
            key = f"{s3_prefix}/{file_name}"
            download_url = s3_client.generate_presigned_url(
                "get_object", Params={"Bucket": bucket, "Key": key}, ExpiresIn=3600
            )

        except Exception as e:
            print(e)
        call.result.data = {"download_url ": download_url}
    else:
        #####下载文件夹##########
        zip_filename = f"{os.path.join(DOWNLOAD_DIR,file_name.split('/')[-2])}.zip"
        print(zip_filename)
        try:
            res = s3_client.list_objects_v2(
                Bucket=bucket, Prefix=os.path.normpath(f"{s3_prefix}/{file_name}")
            )
            with zipfile.ZipFile(zip_filename, "w", zipfile.ZIP_DEFLATED) as zip_file:
                for info in res["Contents"]:
                    key = info["Key"]
                    file_name = os.path.basename(key)
                    temp_file_path = f"/tmp/{file_name}"
                    s3_client.download_file(bucket, key, temp_file_path)
                    common_path = f"{s3_prefix}/{file_name}/"
                    relative_path = os.path.relpath(key, common_path)
                    zip_file.write(temp_file_path, arcname=relative_path)
                    os.remove(temp_file_path)
            s3_client.upload_file(
                Bucket=bucket, Key=os.path.basename(zip_filename), Filename=zip_filename
            )
            download_dir_url = s3_client.generate_presigned_url(
                "get_object",
                Params={"Bucket": bucket, "Key": os.path.basename(zip_filename)},
                ExpiresIn=3600,
            )
            print(download_dir_url)
            Timer(60, delete_zip_file, args=[os.path.basename(zip_filename)]).start()
        except Exception as e:
            print(e)
        finally:
            if os.path.exists(zip_filename):
                os.remove(zip_filename)
        call.result.data = {"download_url ": download_dir_url}


@endpoint("algorithm.batch_delete_file")
def batch_delete_file(call: APICall, company: str, request: AlgoFileBatchDeleteRequest):

    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")

    algorithm_id = request.algorithm_id
    version = request.version
    batch_file_names = request.file_name

    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)
    s3_prefix = f"{pvc}/{algorithm_id}/{str(version)}"

    result = list()
    for file_name in batch_file_names:
        if len(os.path.basename(file_name).split(".", 1)) > 1:
            """删除单个文件"""
            try:
                s3_client.delete_object(Bucket=bucket, Key=f"{s3_prefix}/{file_name}")
            except Exception as e:
                print(e)
            # call.result.data = {"delete_file":os.path.basename(file_name)}
            result.append(file_name)
        else:
            """删除一个文件夹文件"""
            delete_objects = list()
            print("file_name:", file_name)
            try:
                res = s3_client.list_objects(
                    Bucket=bucket, Prefix=os.path.normpath(f"{s3_prefix}{file_name}/")
                )
                delete_objects = [{"Key": item["Key"]} for item in res["Contents"]]
                try:
                    s3_client.delete_objects(
                        Bucket=bucket, Delete={"Objects": delete_objects}
                    )
                    for file in delete_objects:
                        result.append(file["Key"])
                except Exception as e:
                    print(e)
            except Exception as e:
                print(e)
            # call.result.data = {"delete_directory":f"{s3_prefix}{file_name}/"}
    size, version_num = calculate_algorithm_size(algorithm_id)
    AlgorithmBLL.update_mongo(company, algorithm_id, size, version_num)
    call.result.data = {"batch delete files": result}


@endpoint("algorithm.batch_download_file")
def batch_download_file(
    call: APICall, company: str, request: AlgoFileBatchDeleteRequest
):
    DOWNLOAD_DIR = "/home/<USER>"
    # 检查目录是否存在
    if not os.path.exists(DOWNLOAD_DIR):
        # 目录不存在，创建目录
        os.makedirs(DOWNLOAD_DIR)
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")

    algorithm_id = request.algorithm_id
    version = request.version
    batch_file_names = request.file_name

    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)
    s3_prefix = f"{pvc}/{algorithm_id}/{str(version)}"

    tmp_file_path = os.path.normpath(
        f"{DOWNLOAD_DIR}/{algorithm_id}/{'/tmp/'}"
    )  # /home/<USER>/3d07b24944e34b8e8373637e0302cb9c/tmp
    if not os.path.exists(tmp_file_path):
        os.makedirs(tmp_file_path)

    # print(batch_file_names)
    """将需要下载的文件和文件夹文件下载到后端环境中"""
    try:
        for file_name in batch_file_names:
            if len(os.path.basename(file_name).split(".", 1)) > 1:
                print("File")
                key = os.path.normpath(
                    f"{s3_prefix}/{file_name}"
                )  # default-xhz-pvc/3d07b24944e34b8e8373637e0302cb9c/6/Datasets/Commerce/10corr/Data_10corr_4.txt
                s3_client.download_file(
                    Bucket=bucket,
                    Key=key,
                    Filename=os.path.normpath(
                        f"{tmp_file_path}/{os.path.basename(file_name)}"
                    ),
                )
            else:
                try:
                    print("Dir")
                    res = s3_client.list_objects(
                        Bucket=bucket,
                        Prefix=os.path.normpath(f"{s3_prefix}/{file_name}"),
                    )
                    for info in res["Contents"]:
                        key = info[
                            "Key"
                        ]  # default-xhz-pvc/3d07b24944e34b8e8373637e0302cb9c/6/Datasets/Commerce/10corr/Data_10corr_1.txt
                        basename_file_name = os.path.basename(
                            info["Key"]
                        )  # Data_10corr_1.txt
                        tmp_dir_path = os.path.normpath(
                            f"{tmp_file_path}/{os.path.basename(os.path.dirname(key))}"
                        )  # /home/<USER>/3d07b24944e34b8e8373637e0302cb9c/tmp/10corr
                        if not os.path.exists(tmp_dir_path):
                            os.makedirs(tmp_dir_path)
                        s3_client.download_file(
                            Bucket=bucket,
                            Key=key,
                            Filename=f"{tmp_dir_path}/{basename_file_name}",
                        )
                except Exception as e:
                    print(e)

        """将需要下载的文件进行压缩"""
        zip_filename = f"{DOWNLOAD_DIR}/{algorithm_id}/{algorithm_id}.zip"
        # print(zip_filename)
        with zipfile.ZipFile(zip_filename, "w") as zip_file:
            for root, dir, files in os.walk(tmp_file_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    zip_file.write(
                        file_path, arcname=os.path.relpath(file_path, tmp_file_path)
                    )

        """上传压缩包"""
        s3_client.upload_file(
            Bucket=bucket, Key=os.path.basename(zip_filename), Filename=zip_filename
        )
        """生成压缩文件下载url"""
        download_dir_url = s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": bucket, "Key": os.path.basename(zip_filename)},
            ExpiresIn=3600,
        )
        Timer(300, delete_zip_file, args=[os.path.basename(zip_filename)]).start()
        call.result.data = {"download_url ": download_dir_url}
    except Exception as e:
        print(e)
    finally:
        print(f"Delete tmp directory:{os.path.dirname(tmp_file_path)}")
        if os.path.exists(os.path.dirname(tmp_file_path)):
            # os.remove(os.path.dirname(tmp_file_path))
            shutil.rmtree(os.path.dirname(tmp_file_path))


@endpoint("algorithm.create_version", required_fields=["algorithm_id", "version"])
def create_ersion(call: APICall, company: str, request: AlgoCreateVersionRequest):
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")
    algorithm_id = request.algorithm_id
    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)
    key = (
        pvc
        + "/"
        + request.algorithm_id
        + "/"
        + request.version
        + "/"
        + request.algorithm_id
        + ".txt"
    )
    file_content = request.description if request.description else ""
    try:
        response = s3_client.list_objects_v2(
            Bucket=bucket, Prefix=f"{pvc}/{request.algorithm_id }/", Delimiter="/"
        )
        existing_versions = [
            prefix["Prefix"] for prefix in response.get("CommonPrefixes", [])
        ]
        target_version_prefix = f"{pvc}/{request.algorithm_id}/{request.version}/"

        if target_version_prefix in existing_versions:
            call.result.code = 400
            call.result.data = {"message": "版本已存在"}
            return

        s3_client.put_object(Bucket=bucket, Key=key, Body=file_content)
        size, version_num = calculate_algorithm_size(algorithm_id)
        AlgorithmBLL.update_mongo(company, algorithm_id, size, version_num)
        call.result.data = {"creater_version": request.version}
    except Exception as e:
        print(e)


@endpoint("algorithm.get_all_version", required_fields=["algorithm_id"])
def get_all_version(call: APICall, company: str, request: AlgoRequest):
    algorithm_id = request.algorithm_id
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")

    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)
    desc_filename = f"{algorithm_id}.txt"
    s3_prefix = f"{pvc}/{algorithm_id}/"
    versions = []
    try:
        res = s3_client.list_objects_v2(Bucket=bucket, Prefix=s3_prefix, Delimiter="/")
        for obj in res.get("CommonPrefixes", []):
            prefix = obj.get("Prefix", "")
            clean_prefix = prefix[len(s3_prefix) :].rstrip("/")
            desc_key = f"{prefix}{desc_filename}"
            file_count = 0
            total_size = 0
            objects_response = s3_client.list_objects_v2(Bucket=bucket, Prefix=prefix)
            if "Contents" in objects_response:
                for item in objects_response["Contents"]:
                    if item["Key"] != desc_key:  # 排除desc文件本身
                        file_count += 1
                        total_size += item["Size"]
            try:
                desc_response = s3_client.get_object(Bucket=bucket, Key=desc_key)
                desc_content = desc_response["Body"].read().decode("utf-8")

                version_object = {
                    "version": clean_prefix,
                    "description": desc_content,
                    "file_count": file_count,
                    "total_size": convert_size(total_size),
                }
                versions.append(version_object)

            except s3_client.exceptions.NoSuchKey:
                version_object = {
                    "version": clean_prefix,
                    "description": "",
                    "file_count": file_count,
                    "total_size": convert_size(total_size),
                }
                versions.append(version_object)

    except Exception as e:
        print(e)
    call.result.data = {"algorithm_id": algorithm_id, "version_list": versions}


@endpoint("algorithm.download_version", required_fields=["algorithm_id"])
def download_version(call: APICall, company: str, request: AlgoFileRequest):
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")

    algorithm_id = request.algorithm_id
    version = request.version
    desc_filename = f"{algorithm_id}.txt"
    s3_prefix = f"{pvc}/{algorithm_id}/{str(version)}"
    zip_filename = f"{algorithm_id}_{version}.zip"

    session = Session(aws_access_key_id=access_key, aws_secret_access_key=secret_key)
    s3_client = session.client("s3", endpoint_url=url)

    try:
        response = s3_client.list_objects_v2(Bucket=bucket, Prefix=s3_prefix)
        with zipfile.ZipFile(zip_filename, "w", zipfile.ZIP_DEFLATED) as zip_file:
            for obj in response["Contents"]:
                key = obj["Key"]
                if key.endswith(desc_filename):
                    continue
                file_name = os.path.basename(key)
                temp_file_path = f"/tmp/{file_name}"
                s3_client.download_file(bucket, key, temp_file_path)
                relative_path = os.path.relpath(key, s3_prefix)
                relative_path = version + "/" + relative_path
                zip_file.write(temp_file_path, relative_path)
                os.remove(temp_file_path)
        s3_client.upload_file(zip_filename, bucket, zip_filename)
        download_url = s3_client.generate_presigned_url(
            "get_object", Params={"Bucket": bucket, "Key": zip_filename}, ExpiresIn=3600
        )
        Timer(600, delete_zip_file, args=[zip_filename]).start()
        call.result.data = {"download_url": download_url}

    except Exception as e:
        print(e)
    finally:
        if os.path.exists(zip_filename):
            os.remove(zip_filename)


@endpoint("algorithm.file_list")
def file_list(call: APICall, company: str, request: AlgoFileRequest):

    algorithm_id = request.algorithm_id
    version = request.version

    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")

    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)
    desc_filename = f"/{algorithm_id}.txt"

    s3_prefix = f"{pvc}/{algorithm_id}/{str(version)}"
    try:
        res = s3_client.list_objects(Bucket=bucket, Prefix=s3_prefix)

    except Exception as e:
        print(e)

    result = []
    for info in res["Contents"]:
        tmp = dict()
        tmp["name"] = info["Key"].replace(s3_prefix, "")
        if tmp["name"] == desc_filename:
            continue
        tmp["size"] = convert_size(info["Size"])
        tmp["last_update"] = info["LastModified"]
        # tmp["is_dir"] = os.path.isdir(tmp["name"])
        result.append(tmp)
    call.result.data = {"algorithm_version": version, "algorithm_list": result}


@endpoint("algorithm.read_file")
def file_list(call: APICall, company: str, request: AlgoReadFileRequest):
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")
    algorithm_id = request.algorithm_id
    file_name = request.file_name
    version = request.version
    s3_prefix = f"{pvc}/{algorithm_id}/{str(version)}/{file_name}"
    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)
    response = s3_client.get_object(Bucket=bucket, Key=s3_prefix)
    file_content = response["Body"].read().decode("utf-8")
    call.result.data = {"file": file_content}


def calculate_algorithm_size(algorithm_id: str):
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")

    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)

    algorithm_size = 0
    algorithm_version_count = 0
    try:
        cal_algorithm_size = s3_client.list_objects(
            Bucket=bucket, Prefix=pvc + "/" + algorithm_id + "/"
        )

        if cal_algorithm_size.__contains__("Contents"):
            for algorithm_info in cal_algorithm_size["Contents"]:
                algorithm_size += algorithm_info["Size"]
    except Exception as e:
        print(e)

    try:
        cal_version_count = s3_client.list_objects(
            Bucket=bucket, Prefix=pvc + "/" + algorithm_id + "/", Delimiter="/"
        )
        if cal_version_count.__contains__("CommonPrefixes"):
            algorithm_version_count = len(cal_version_count["CommonPrefixes"])
    except Exception as e:
        print(e)

    return convert_size(algorithm_size), algorithm_version_count


def convert_size(bytes):
    if bytes is None or bytes < 0:
        return "Invalid Size"

    size_map = {"TB": 1024**4, "GB": 1024**3, "MB": 1024**2, "KB": 1024}

    for unit, conversion in size_map.items():
        if bytes >= conversion:
            return f"{bytes/conversion:.2f} {unit}"
    return f"{bytes:.2f} Bytes"


def delete_zip_file(zip_filename: str):
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")

    session = Session(aws_access_key_id=access_key, aws_secret_access_key=secret_key)
    s3_client = session.client("s3", endpoint_url=url)

    try:
        s3_client.delete_object(Bucket=bucket, Key=zip_filename)
        print(f"Deleted {zip_filename} from S3")
    except Exception as e:
        print(e)


def support_gbk(zip_file):
    """支持中文编码"""
    name_to_info = zip_file.NameToInfo
    # copy map first
    for name, info in name_to_info.copy().items():
        try:
            real_name = name.encode("cp437").decode("gbk")
        except:
            real_name = name
        if real_name != name:
            info.filename = real_name
            del name_to_info[name]
            name_to_info[real_name] = info
    return zip_file
