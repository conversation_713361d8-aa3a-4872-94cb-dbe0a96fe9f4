from apiserver.service_repo import APICall, endpoint
from apiserver.database.errors import translate_errors_context
from mongoengine import Q
import json
from kubernetes import client, config
from apiserver.database.utils import (
    parse_from_call,
    get_company_or_none_constraint,
)
import re
import math

def parse_memory(value):
    if value.endswith("Ki"):
        return int(value.strip("Ki")) / 1024
    elif value.endswith("Mi"):
        return int(value.strip("Mi")) 
    elif value.endswith("Gi"):
        return int(value.strip("Gi")) * 1024
    else:
        value = re.sub(r"[^\d]", "", value)
        return int(value) / 1024 if value else 0    

def get_metrics_server_data(page, page_size, search, arch_filter):
    try:
        config.load_kube_config()
    except:
        config.load_incluster_config()
    api_client = client.ApiClient()
    core_v1 = client.CoreV1Api()
    nodes  = core_v1.list_node().items
    metrics_path = "/apis/metrics.k8s.io/v1beta1/nodes"
    response = api_client.call_api(
        metrics_path,
        "GET",
        auth_settings = ["BearerToken"],
        _preload_content = False
    )        
    if response[1] !=200:
        return []
    metrics_data = json.loads(response[0].data.decode("utf-8"))
    metrics = {item['metadata']['name']:item['usage'] for item in metrics_data['items']}
    node_info = []
    for node in nodes:
        node_name = node.metadata.name
        node_ip = next((addr.address for addr in node.status.addresses if addr.type == "InternalIP"), "")
        arch = node.status.node_info.architecture
        node_status = "ready" if any(
            condition.type == "Ready" and condition.status == "True" for condition in node.status.conditions
        ) else "notReady"
        cpu_capacity = node.status.capacity.get("cpu", "N/A")
        memory_capacity = node.status.capacity.get("memory", "N/A").strip("Ki")
        cpu_usage = metrics[node_name]['cpu'].strip("n") if node_name in metrics else None
        memory_usage = metrics[node_name]['memory'].strip("ki") if node_name in metrics else None
        cpu_total_cores = int(cpu_capacity) if cpu_capacity.isdigit() else 0
        cpu = (
            f"{int(cpu_usage) / (cpu_total_cores * 1_000_000_000) * 100:.2f}"
            if cpu_usage and cpu_capacity != "N/A" else "N/A"
        )
        memory_capacity_mib = parse_memory(memory_capacity)
        memory_usage_mib = parse_memory(memory_usage)
        memory = (
            f"{int(memory_usage_mib) / (int(memory_capacity_mib)) * 100:.2f}"
            if memory_usage and memory_capacity else "N/A"
        )
        disk_capacity = node.status.allocatable.get('ephemeral-storage', "N/A").strip("Ki")
        disk = int(int(disk_capacity) / 1024**2)
        node_info.append({
            "node":node_name,
            "ip":node_ip,
            "cpu":cpu,
            "memory":memory,
            "status":node_status,
            "arch":arch,
            "disk":disk
        })
    if search:
        node_info = [node for node in node_info if search.lower() in node['node'].lower()]    
    if arch_filter:
        node_info = [node for node in node_info if node['arch'] == arch_filter]    
    total_count = len(node_info)
    total_pages = math.ceil(total_count / page_size)
    start_index = page * page_size
    end_index = start_index + page_size
    pager = {
            "page": page,
            "page_size": page_size,
            "count": total_count,
        }
    node_list = node_info[start_index:end_index]    
    return pager ,node_list


@endpoint("resource.get_all_resource")
def get_all_resource(call: APICall, company_id, _):
    page = call.data.get("page", 0)
    page_size = call.data.get("page_size",10)
    search = call.data.get('name', '')
    arch = call.data.get('arch', '')
    pager, node_list = get_metrics_server_data(page,page_size,search,arch)
    call.result.data = {'list':node_list, 'pager':pager}