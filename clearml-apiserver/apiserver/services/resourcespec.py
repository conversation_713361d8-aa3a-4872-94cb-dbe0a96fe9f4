from typing import Sequence, Optional, Tuple
from mongoengine import Q
from apiserver.service_repo import APICall, endpoint
from apiserver.database.errors import translate_errors_context
import apiserver.database.errors
from apiserver.database.model.resourcespec import ResourceSpec
from apiserver.apimodels.ides import(
    IdeRequest,
    IdeGetRequest,
    IdeCreateRequest,
    IdeUpdateRequest,
)
from apiserver.database.utils import (
    parse_from_call,
    get_company_or_none_constraint,
)
from apiserver.bll.resourcespec import ResourceSpecBLL
import time
import gitlab
from apiserver.config_repo import config

log = config.logger(__file__)

resource_spec_bll = ResourceSpecBLL()

@endpoint("resourcespec.get_all")
def get_all(call: APICall, company: str, _):
    resource_specs = ResourceSpec.get_many(company=company)
    # resource_id = "1b9a26c761cb4ee6958af63a74cf0dc4"
    # resource = ResourceSpecBLL.get_by_id(resource_id, company)
    # cpu = resource.cpu
    # mem = resource.mem

    call.result.data = {"list":resource_specs}


@endpoint("resourcespec.create_resource")
def create_resource(call: APICall, company: str, _):
    identity = call.identity
    name = call.data.get("name", "")
    cpu = call.data.get("cpu", "")
    mem = call.data.get("mem", "")
    created = ResourceSpecBLL.create(user=identity.user, company_id=company, name=name, cpu=cpu, mem=mem,) 
    call.result.data = {"msg":created}  
    
