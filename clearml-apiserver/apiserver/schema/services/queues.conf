_description: "Provides a management API for queues of tasks waiting to be executed by workers deployed anywhere (see Workers Service)."
_definitions {
    include "_common.conf"
    queue_metrics {
        type: object
        properties: {
            queue: {
                type: string
                description: "ID of the queue"
            }
            dates {
                type: array
                description: "List of timestamps (in seconds from epoch) in the acceding order. The timestamps are separated by the requested interval. Timestamps where no queue status change was recorded are omitted."
                items { type: integer }
            }
            avg_waiting_times {
                type: array
                description: "List of average waiting times for tasks in the queue. The points correspond to the timestamps in the dates list. If more than one value exists for the given interval then the maximum value is taken."
                items { type: number }
            }
            queue_lengths {
                type: array
                description: "List of tasks counts in the queue. The points correspond to the timestamps in the dates list. If more than one value exists for the given interval then the count that corresponds to the maximum average value is taken."
                items { type: integer }
            }
        }
    }
    entry {
        type: object
        properties: {
            task {
                description: "Queued task ID"
                type: string
            }
            added {
                description: "Time this entry was added to the queue"
                type: string
                format: "date-time"
            }
        }
    }
    queue {
        type: object
        properties: {
            id {
                description: "Queue id"
                type: string
            }
            name {
                description: "Queue name"
                type: string
            }
            user {
                description: "Associated user id"
                type: string
            }
            company {
                description: "Company id"
                type: string
            }
            created {
                description: "Queue creation time"
                type: string
                format: "date-time"
            }
            tags {
                description: "User-defined tags"
                type: array
                items { type: string }
            }
            system_tags {
                description: "System tags. This field is reserved for system use, please don't use it."
                type: array
                items { type: string }
            }
            entries {
                description: "List of ordered queue entries"
                type: array
                items { "$ref": "#/definitions/entry" }
            }
            metadata {
                description: "Queue metadata"
                type: object
                additionalProperties {
                    "$ref": "#/definitions/metadata_item"
                }
            }
        }
    }
}

get_by_id {
    "2.4" {
        description: "Gets queue information"
        request {
            type: object
            required: [ queue ]
            properties {
                queue {
                    description: "Queue ID"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                queue {
                    description: "Queue info"
                    "$ref": "#/definitions/queue"
                }
            }
        }
    }
    "2.20": ${get_by_id."2.4"} {
        request.properties.max_task_entries {
            description: Max number of queue task entries to return
            type: integer
        }
    }
}
// typescript generation hack
get_all_ex {
    internal: true
    "2.4": ${get_all."2.4"}
    "2.15": ${get_all_ex."2.4"} {
        request {
            properties {
                scroll_id {
                    type: string
                    description: "Scroll ID returned from the previos calls to get_all_ex"
                }
                refresh_scroll {
                    type: boolean
                    description: "If set then all the data received with this scroll will be requeried"
                }
                size {
                    type: integer
                    minimum: 1
                    description: "The number of queues to retrieve"
                }
            }
        }
        response.properties.scroll_id {
            type: string
            description: "Scroll ID that can be used with the next calls to get_all_ex to retrieve more data"
        }
    }
    "2.20": ${get_all_ex."2.15"} {
        request.properties.max_task_entries {
            description: Max number of queue task entries to return
            type: integer
        }
    }
    "2.21": ${get_all_ex."2.20"} {
        request.properties.search_hidden {
            description: "If set to 'true' then hidden queues are included in the search results"
            type: boolean
            default: false
        }
    }
    "2.27": ${get_all_ex."2.21"} {
        request.properties {
            filters {
                type: object
                additionalProperties: ${_definitions.field_filter}
            }
        }
    }
}
get_all {
    "2.4" {
        description: "Get all queues"
        request {
            type: object
            properties {
                name {
                    description: "Get only queues whose name matches this pattern (python regular expression syntax)"
                    type: string
                }
                id {
                    description: "List of Queue IDs used to filter results"
                    type: array
                    items { type: string }
                }
                tags {
                    description: "User-defined tags list used to filter results. Prepend '-' to tag name to indicate exclusion"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list used to filter results. Prepend '-' to system tag name to indicate exclusion"
                    type: array
                    items { type: string }
                }
                page {
                    description: "Page number, returns a specific page out of the result list of results."
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                order_by {
                    description: "List of field names to order by. When search_text is used, '@text_score' can be used as a field representing the text score of returned documents. Use '-' prefix to specify descending order. Optional, recommended when using page"
                    type: array
                    items { type: string }
                }
                search_text {
                    description: "Free text search query"
                    type: string
                }
                only_fields {
                    description: "List of document field names (nesting is supported using '.', e.g. execution.model_labels). If provided, this list defines the query's projection (only these fields will be returned for each result entry)"
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties {
                queues {
                    description: "Queues list"
                    type: array
                    items { "$ref": "#/definitions/queue"}
                }
            }
        }
    }
    "2.15": ${get_all."2.4"} {
        request {
            properties {
                scroll_id {
                    type: string
                    description: "Scroll ID returned from the previos calls to get_all"
                }
                refresh_scroll {
                    type: boolean
                    description: "If set then all the data received with this scroll will be requeried"
                }
                size {
                    type: integer
                    minimum: 1
                    description: "The number of queues to retrieve"
                }
            }
        }
        response.properties.scroll_id {
            type: string
            description: "Scroll ID that can be used with the next calls to get_all to retrieve more data"
        }
    }
    "2.20": ${get_all."2.15"} {
        request.properties.max_task_entries {
            description: Max number of queue task entries to return
            type: integer
        }
    }
    "2.21": ${get_all."2.20"} {
        request.properties.search_hidden {
            description: "If set to 'true' then hidden queues are included in the search results"
            type: boolean
            default: false
        }
    }
}
get_default {
    "2.4" {
        description: ""
        request {
            type: object
            properties {}
            additionalProperties: false
        }
        response {
            type: object
            properties {
                id {
                    description: "Queue id"
                    type: string
                }
                name {
                    description: "Queue name"
                    type: string
                }
            }
        }
    }
}
create {
    "2.4" {
        description: "Create a new queue"
        request {
            type: object
            required: [ name ]
            properties {
                name {
                    description: "Queue name Unique within the company."
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "New queue ID"
                    type: string
                }
            }
        }
    }
    "2.13": ${create."2.4"} {
        metadata {
            description: "Queue metadata"
            type: object
            additionalProperties {
                "$ref": "#/definitions/metadata_item"
            }
        }
    }
}
update {
    "2.4" {
        description: "Update queue information"
        request {
            type: object
            required: [ queue ]
            properties {
                queue {
                    description: "Queue id"
                    type: string
                }
                name {
                    description: "Queue name Unique within the company."
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of queues updated (0 or 1)"
                    type: integer
                    enum: [ 0, 1 ]
                }
                fields {
                    description: "Updated fields names and values"
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
    "2.13": ${update."2.4"} {
        metadata {
            description: "Queue metadata"
            type: object
            additionalProperties {
                "$ref": "#/definitions/metadata_item"
            }
        }
    }
}
delete {
    "2.4" {
        description: "Deletes a queue. If the queue is not empty and force is not set to true, queue will not be deleted."
        request {
            type: object
            required: [ queue ]
            properties {
                queue {
                    description: "Queue id"
                    type: string
                }
                force {
                    description: "Force delete of non-empty queue. Defaults to false"
                    type: boolean
                    default: false
                }
            }
        }
        response {
            type: object
            properties {
                deleted {
                    description: "Number of queues deleted (0 or 1)"
                    type: integer
                    enum: [ 0, 1 ]
                }
            }
        }
    }
}
add_task {
    "2.4" {
        description: "Adds a task entry to the queue."
        request {
            type: object
            required: [
                queue
                task
            ]
            properties {
                queue {
                    description: "Queue id"
                    type: string
                }
                task {
                    description: "Task id"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                added {
                    description: "Number of tasks added (0 or 1)"
                    type: integer
                    enum: [ 0, 1 ]
                }
            }
        }
    }
}
get_next_task {
    "2.4" {
        description: "Gets the next task from the top of the queue (FIFO). The task entry is removed from the queue."
        request {
            type: object
            required: [ queue ]
            properties {
                queue {
                    description: "Queue id"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                entry {
                    description: "Entry information"
                    "$ref": "#/definitions/entry"
                }
            }
        }
    }
    "2.14": ${get_next_task."2.4"} {
        request.properties.get_task_info {
            description: "If set then additional task info is returned"
            type: boolean
            default: false
        }
        response.properties.task_info {
            description: "Info about the returned task. Returned only if get_task_info is set to True"
            type: object
            properties {
                company {
                    description: Task company ID
                    type: string
                }
                user {
                    description: ID of the user who created the task
                    type: string
                }
            }
        }
    }
    "2.21": ${get_next_task."2.14"} {
        request.properties.task {
            description: Task company ID
            type: string
        }
    }
}
remove_task {
    "2.4" {
        description: "Removes a task entry from the queue."
        request {
            type: object
            required: [
                queue
                task
            ]
            properties {
                queue {
                    description: "Queue id"
                    type: string
                }
                task {
                    description: "Task id"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                removed {
                    description: "Number of tasks removed (0 or 1)"
                    type: integer
                    enum: [ 0, 1 ]
                }
            }
        }
    }
}

move_task_forward: {
    "2.4" {
        description: "Moves a task entry one step forward towards the top of the queue."
        request {
            type: object
            required: [
                queue
                task
            ]
            properties {
                queue {
                    description: "Queue id"
                    type: string
                }
                task {
                    description: "Task id"
                    type: string
                }
                count {
                    description: "Number of positions in the queue to move the task forward relative to the current position. Optional, the default value is 1."
                    type: integer
                }
            }
        }
        response {
            type: object
            properties {
                position {
                    description: "The new position of the task entry in the queue (index, -1 represents bottom of queue)"
                    type: integer
                }
            }
        }
    }
}
move_task_backward: {
    "2.4" {
        description: ""
        request {
            type: object
            required: [
                queue
                task
            ]
            properties {
                queue {
                    description: "Queue id"
                    type: string
                }
                task {
                    description: "Task id"
                    type: string
                }
                count {
                    description: "Number of positions in the queue to move the task forward relative to the current position. Optional, the default value is 1."
                    type: integer
                }
            }
        }
        response {
            type: object
            properties {
                position {
                    description: "The new position of the task entry in the queue (index, -1 represents bottom of queue)"
                    type: integer
                }
            }
        }
    }
}
move_task_to_front: {
    "2.4" {
        description: ""
        request {
            type: object
            required: [
                queue
                task
            ]
            properties {
                queue {
                    description: "Queue id"
                    type: string
                }
                task {
                    description: "Task id"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                position {
                    description: "The new position of the task entry in the queue (index, -1 represents bottom of queue)"
                    type: integer
                }
            }
        }
    }
}
move_task_to_back: {
    "2.4" {
        description: ""
        request {
            type: object
            required: [
                queue
                task
            ]
            properties {
                queue {
                    description: "Queue id"
                    type: string
                }
                task {
                    description: "Task id"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                position {
                    description: "The new position of the task entry in the queue (index, -1 represents bottom of queue)"
                    type: integer
                }
            }
        }
    }
}
get_queue_metrics : {
    "2.4" {
        description: "Returns metrics of the company queues. The metrics are avaraged in the specified interval."
        request {
            type: object
            required: [from_date, to_date, interval]
            properties: {
                from_date {
                    description: "Starting time (in seconds from epoch) for collecting metrics"
                    type: number
                }
                to_date {
                    description: "Ending time (in seconds from epoch) for collecting metrics"
                    type: number
                }
                interval {
                    description: "Time interval in seconds for a single metrics point. The minimal value is 1"
                    type: integer
                }
                queue_ids {
                    description: "List of queue ids to collect metrics for. If not provided or empty then all then average metrics across all the company queues will be returned."
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties: {
                queues {
                    type: array
                    description: "List of the requested queues with their metrics. If no queue ids were requested then 'all' queue is returned with the metrics averaged accross all the company queues."
                    items { "$ref": "#/definitions/queue_metrics" }
                }
            }
        }
    }
    "2.20": ${get_queue_metrics."2.4"} {
        request.properties.refresh {
            type: boolean
            default: false
            description: If set then the new queue metrics is taken
        }
    }
}
add_or_update_metadata {
    "2.13" {
        description: "Add or update queue metadata"
        request {
            type: object
            required: [queue, metadata]
            properties {
                queue {
                    description: "ID of the queue"
                    type: string
                }
                metadata {
                    type: array
                    description: "Metadata items to add or update"
                    items {"$ref": "#/definitions/metadata_item"}
                }
                replace_metadata {
                    description: "If set then the all the metadata items will be replaced with the provided ones. Otherwise only the provided metadata items will be updated or added"
                    type: boolean
                    default: false
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of queues updated (0 or 1)"
                    type: integer
                    enum: [0, 1]
                }
            }
        }
    }
}
delete_metadata {
    "2.13" {
        description: "Delete metadata from queue"
        request {
            type: object
            required: [ queue, keys ]
            properties {
                queue {
                    description: "ID of the queue"
                    type: string
                }
                keys {
                    description: "The list of metadata keys to delete"
                    type: array
                    items {type: string}
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of queues updated (0 or 1)"
                    type: integer
                    enum: [0, 1]
                }
            }
        }
    }
}
peek_task {
    "2.15" {
        description: "Peek the next task from a given queue"
        request {
            type: object
            required: [ queue ]
            properties {
                queue {
                    description: "ID of the queue"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
            }
        }
    }
}
get_num_entries {
    "2.15" {
        description: "Get the number of task entries in the given queue"
        request {
            type: object
            required: [ queue ]
            properties {
                queue {
                    description: "ID of the queue"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                num {
                    description: "Number of entries"
                    type: integer
                }
            }
        }
    }
}
