_description: "server utilities"
_default {
    internal: true
    allow_roles: ["root", "system"]
}
get_stats {
    "2.1" {
        description: "Get the server collected statistics."
        request {
            type: object
            properties {
                interval {
                    description: "The period for statistics collection in seconds."
                    type: long
                }
            }
        }
        response {
            type: object
            properties: {
            }
        }
    }
}
config {
    "2.1" {
        description: "Get server configuration. Secure section is not returned."
        request {
            type: object
            properties {
                path {
                    description: "Path of config value. Defaults to root"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
            }
        }
    }
}
info {
    authorize = false
    allow_roles = [ "*" ]
    "2.1" {
        description: "Get server information, including version and build number"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                version {
                    description: "Version string"
                    type: string
                }
                build {
                    description: "Build number"
                    type: string
                }
                commit {
                    description: "VCS commit number"
                    type: string
                }
            }
        }
    }
    "2.8": ${info."2.1"} {
        response {
            type: object
            properties {
                uid {
                    description: "Server UID"
                    type: string
                }
                api_version {
                    description: "Max API version supported"
                    type: string
                }
            }
        }
    }
    "2.12": ${info."2.8"} {
        response {
            type: object
            properties {
                api_version {
                    description: "Max API version supported"
                    type: string
                }
            }
        }
    }
}
endpoints {
    "2.1" {
        description: "Show available endpoints"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
            }
        }
    }
}
report_stats_option {
    allow_roles = [ "*" ]
    "2.4" {
        description: "Get or set the report statistics option per-company"
        request {
            type: object
            properties {
                enabled {
                    description: "If provided, sets the report statistics option (true/false)"
                    type: boolean
                }
            }
        }
        response {
            type: object
            properties {
                supported {
                    description: "Is this feature supported by the server"
                    type: boolean
                }
                enabled {
                    description: "Returns the current report stats option value"
                    type: boolean
                }
                enabled_time {
                    description: "If enabled, returns the time at which option was enabled"
                    type: string
                    format: date-time
                }
                enabled_version {
                    description: "If enabled, returns the server version at the time option was enabled"
                    type: string
                }
                enabled_user {
                    description: "If enabled, returns Id of the user who enabled the option"
                    type: string
                }
                current_version {
                    description: "Returns the current server version"
                    type: string
                }
            }
        }
    }
}