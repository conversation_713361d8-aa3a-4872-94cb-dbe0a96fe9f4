_description: "Provides a management API for reports in the system."
_definitions {
    include "_tasks_common.conf"
    include "_events_common.conf"
    update_response {
        type: object
        properties {
            updated {
                description: "Number of reports updated (0 or 1)"
                type: integer
                enum: [ 0, 1 ]
            }
            fields {
                description: "Updated fields names and values"
                type: object
                additionalProperties: true
            }
        }
    }
    report_status_enum {
        type: string
        enum: [
            created
            published
        ]
    }
    report {
        type: object
        properties {
            id {
                description: "Report id"
                type: string
            }
            name {
                description: "Report Name"
                type: string
            }
            user {
                description: "Associated user id"
                type: string
            }
            company {
                description: "Company ID"
                type: string
            }
            status {
                description: ""
                "$ref": "#/definitions/report_status_enum"
            }
            comment {
                description: "Free text comment"
                type: string
            }
            report {
                description: "Report template"
                type: string
            }
            report_assets {
                description: "List of the external report assets"
                type: array
                items { type: string }
            }
            created {
                description: "Report creation time (UTC) "
                type: string
                format: "date-time"
            }
            project {
                description: "Project ID of the project to which this report is assigned"
                type: string
            }
            tags {
                description: "User-defined tags list"
                type: array
                items { type: string }
            }
            system_tags {
                description: "System tags list. This field is reserved for system use, please don't use it."
                type: array
                items { type: string }
            }
            status_changed {
                description: "Last status change time"
                type: string
                format: "date-time"
            }
            status_message {
                description: "free text string representing info about the status"
                type: string
            }
            status_reason {
                description: "Reason for last status change"
                type: string
            }
            published {
                description: "Report publish time"
                type: string
                format: "date-time"
            }
            last_update {
                description: "Last time this report was created, edited, changed"
                type: string
                format: "date-time"
            }
        }
    }
}
create {
    "2.23" {
        description: "Create a new report"
        request {
            type: object
            required: [
                name
            ]
            properties {
                name {
                    description: "Report name. Unique within the company."
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                comment {
                    description: "Free text comment "
                    type: string
                }
                report {
                    description: "Report template"
                    type: string
                }
                project {
                    description: "Project ID of the project to which this report is assigned Must exist[ab]"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "ID of the report"
                    type: string
                }
                project_id {
                    description: "ID of the project that the report belongs to"
                    type: string
                }
            }
        }
    }
    "2.24": ${create."2.23"} {
        request.properties.report_assets {
            description: "List of the external report assets"
            type: array
            items { type: string }
        }
    }
}
update {
    "2.23" {
        description: "Create a new report"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    description: "The ID of the report task to update"
                    type: string
                }
                name {
                    description: "Report name. Unique within the company."
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                comment {
                    description: "Free text comment "
                    type: string
                }
                report {
                    description: "Report template"
                    type: string
                }
            }
        }
        response: ${_definitions.update_response}
    }
    "2.24": ${update."2.23"} {
        request.properties.report_assets {
            description: "List of the external report assets"
            type: array
            items { type: string }
        }
    }
}
move {
    "2.23" {
        description: "Move reports to a project"
        request {
            type: object
            required: [task]
            properties {
                task {
                    description: "ID of the report to move"
                    type: string
                }
                project {
                    description: "Target project ID. If not provided, `project_name` must be provided. Use null for the root project"
                    type: string
                }
                project_name {
                    description: "Target project name. If provided and a project with this name does not exist, a new project will be created. If not provided, `project` must be provided."
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                project_id: {
                    description: The ID of the target project
                    type: string
                }
            }
        }
    }
}
publish {
    "2.23" {
        description: "Publish report"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    description: "The ID of the report task to publish"
                    type: string
                }
                comment {
                    description: "The client message"
                    type: string
                }
            }
        }
        response: ${_definitions.update_response}
    }
}
archive {
    "2.23" {
        description: "Archive report"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    description: "The ID of the report task to archive"
                    type: string
                }
                comment {
                    description: "The client message"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                archived {
                    description: "Number of reports archived (0 or 1)"
                    type: integer
                    enum: [0, 1]
                }
            }
        }
    }
}
unarchive {
    "2.23" {
        description: "Unarchive report"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    description: "The ID of the report task to unarchive"
                    type: string
                }
                comment {
                    description: "The client message"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                unarchived {
                    description: "Number of reports unarchived (0 or 1)"
                    type: integer
                    enum: [0, 1]
                }
            }
        }
    }
}
//share {
//    "999.0" {
//        description: "Share or unshare report"
//        request {
//            type: object
//            required: [
//                task
//            ]
//            properties {
//                task {
//                    description: "The ID of the report task to share/unshare"
//                    type: string
//                }
//                share {
//                    description: "If set to 'true' then the report will be shared. Otherwise unshared."
//                    type: boolean
//                    default: true
//                }
//            }
//        }
//        response {
//            type: object
//            properties {
//                changed {
//                    description: "Number of changed reports (0 or 1)"
//                    type: integer
//                    enum: [0, 1]
//                }
//            }
//        }
//    }
//}
delete {
    "2.23" {
        description: "Delete report"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    description: "The ID of the report task to delete"
                    type: string
                }
                force {
                    description: "If not set then published or unarchived reports cannot be deleted"
                    type: boolean
                    default: false
                }
            }
        }
        response {
            type: object
            properties {
                deleted {
                    description: "Number of deleted reports (0 or 1)"
                    type: integer
                    enum: [0, 1]
                }
            }
        }
    }
}
get_task_data {
    "2.23" {
        description: "Get the tasks data according the passed search criteria + requested events"
        request {
            type: object
            properties {
                id {
                    description: "List of IDs to filter by"
                    type: array
                    items { type: string }
                }
                name {
                    description: "Get only tasks whose name matches this pattern (python regular expression syntax)"
                    type: string
                }
                user {
                    description: "List of user IDs used to filter results by the task's creating user"
                    type: array
                    items { type: string }
                }
                size {
                    type: integer
                    minimum: 1
                    description: "The number of tasks to retrieve"
                }
                order_by {
                    description: "List of field names to order by. When search_text is used, '@text_score' can be used as a field representing the text score of returned documents. Use '-' prefix to specify descending order. Optional, recommended when using page"
                    type: array
                    items { type: string }
                }
                type {
                    description: "List of task types. One or more of: 'import', 'annotation', 'training' or 'testing' (case insensitive)"
                    type: array
                    items { type: string }
                }
                tags {
                    description: "List of task user-defined tags. Use '-' prefix to exclude tags"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "List of task system tags. Use '-' prefix to exclude system tags"
                    type: array
                    items { type: string }
                }
                status {
                    description: "List of task status."
                    type: array
                    items { "$ref": "#/definitions/task_status_enum" }
                }
                project {
                    description: "List of project IDs"
                    type: array
                    items { type: string }
                }
                only_fields {
                    description: "List of task field names (nesting is supported using '.', e.g. execution.model_labels). If provided, this list defines the query's projection (only these fields will be returned for each result entry)"
                    type: array
                    items { type: string }
                }
                parent {
                    description: "Parent ID"
                    type: string
                }
                status_changed {
                    description: "List of status changed constraint strings (utcformat, epoch) with an optional prefix modifier (>, >=, <, <=)"
                    type: array
                    items {
                        type: string
                        pattern: "^(>=|>|<=|<)?.*$"
                    }
                }
                search_text {
                    description: "Free text search query"
                    type: string
                }
                allow_public {
                    description: "Allow public tasks to be returned in the results"
                    type: boolean
                    default: true
                }
                _all_ {
                    description: "Multi-field pattern condition (all fields match pattern)"
                    "$ref": "#/definitions/multi_field_pattern_data"
                }
                _any_ {
                    description: "Multi-field pattern condition (any field matches pattern)"
                    "$ref": "#/definitions/multi_field_pattern_data"
                }
                "input.view.entries.dataset" {
                    description: "List of input dataset IDs"
                    type: array
                    items { type: string }
                }
                "input.view.entries.version" {
                    description: "List of input dataset version IDs"
                    type: array
                    items { type: string }
                }
                search_hidden {
                    description: "If set to 'true' then hidden tasks are included in the search results"
                    type: boolean
                    default: false
                }
                include_subprojects {
                    description: "If set to 'true' and project field is set then tasks from the subprojects are searched too"
                    type: boolean
                    default: false
                }
                plots {
                    type: object
                    properties {
                        iters {
                            type: integer
                            description: "Max number of latest iterations for which to return plots"
                        }
                        metrics {
                            type: array
                            description: List of metrics and variants
                            items { "$ref": "#/definitions/metric_variants" }
                        }
                    }
                }
                debug_images {
                    type: object
                    properties {
                        iters {
                            type: integer
                            description: "Max number of latest iterations for which to return debug images"
                        }
                        metrics {
                            type: array
                            description: List of metrics and variants
                            items { "$ref": "#/definitions/metric_variants" }
                        }
                    }
                }
                scalar_metrics_iter_histogram {
                    type: object
                    properties {
                        samples {
                            description: "The amount of histogram points to return (0 to return all the points). Optional, the default value is 6000."
                            type: integer
                        }
                        key {
                            description: """
                            Histogram x axis to use:
                            iter - iteration number
                            iso_time - event time as ISO formatted string
                            timestamp - event timestamp as milliseconds since epoch
                            """
                            "$ref": "#/definitions/scalar_key_enum"
                        }
                        metrics {
                            type: array
                            description: List of metrics and variants
                            items { "$ref": "#/definitions/metric_variants" }
                        }
                    }
                }
            }
        }
        response {
            type: object
            properties {
                tasks {
                    description: "List of tasks"
                    type: array
                    items { "$ref": "#/definitions/task" }
                }
                plots {
                    type: object
                    description: "Plots mapped by metric, variant, task and iteration"
                    additionalProperties: true
                }
                debug_images {
                    type: array
                    description: "Debug image events grouped by tasks and iterations"
                    items {"$ref": "#/definitions/debug_images_response_task_metrics"}
                }
                scalar_metrics_iter_histogram {
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
    "2.25": ${get_task_data."2.23"} {
        request.properties {
            model_events {
                type: boolean
                description: If set then the retrieving model events. Otherwise task events
                default: false
            }
            single_value_metrics {
                type: object
                description: If passed then task single value metrics are returned
                additionalProperties: false
            }
        }
        response.properties.single_value_metrics {
            type: array
            description: Single value metrics grouped by task
            items {"$ref": "#/definitions/single_value_task_metrics"}
        }
    }
    "2.26": ${get_task_data."2.25"} {
        request.properties.plots.properties.last_iters_per_task_metric {
            type: boolean
            description: If set to 'true' and iters passed then last iterations for each task metrics are retrieved. Otherwise last iterations for the whole task are retrieved
            default: true
        }
    }
}
get_all_ex {
    "2.23" {
        description: "Get all the company's and public report tasks"
        request {
            type: object
            properties {
                id {
                    description: "List of IDs to filter by"
                    type: array
                    items { type: string }
                }
                name {
                    description: "Get only reports whose name matches this pattern (python regular expression syntax)"
                    type: string
                }
                user {
                    description: "List of user IDs used to filter results by the reports's creating user"
                    type: array
                    items { type: string }
                }
                page {
                    description: "Page number, returns a specific page out of the resulting list of reports"
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                order_by {
                    description: "List of field names to order by. When search_text is used, '@text_score' can be used as a field representing the text score of returned documents. Use '-' prefix to specify descending order. Optional, recommended when using page"
                    type: array
                    items { type: string }
                }
                tags {
                    description: "List of report user-defined tags. Use '-' prefix to exclude tags"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "List of report system tags. Use '-' prefix to exclude system tags"
                    type: array
                    items { type: string }
                }
                status {
                    description: "List of report status."
                    type: array
                    items { "$ref": "#/definitions/report_status_enum" }
                }
                project {
                    description: "List of project IDs"
                    type: array
                    items { type: string }
                }
                only_fields {
                    description: "List of report field names (nesting is supported using '.'). If provided, this list defines the query's projection (only these fields will be returned for each result entry)"
                    type: array
                    items { type: string }
                }
                status_changed {
                    description: "List of status changed constraint strings (utcformat, epoch) with an optional prefix modifier (>, >=, <, <=)"
                    type: array
                    items {
                        type: string
                        pattern: "^(>=|>|<=|<)?.*$"
                    }
                }
                search_text {
                    description: "Free text search query"
                    type: string
                }
                scroll_id {
                    type: string
                    description: "Scroll ID returned from the previos calls to get_all"
                }
                refresh_scroll {
                    type: boolean
                    description: "If set then all the data received with this scroll will be requeried"
                }
                size {
                    type: integer
                    minimum: 1
                    description: "The number of tasks to retrieve"
                }
                allow_public {
                    description: "Allow public reports to be returned in the results"
                    type: boolean
                    default: true
                }
                _all_ {
                    description: "Multi-field pattern condition (all fields match pattern)"
                    "$ref": "#/definitions/multi_field_pattern_data"
                }
                _any_ {
                    description: "Multi-field pattern condition (any field matches pattern)"
                    "$ref": "#/definitions/multi_field_pattern_data"
                }
            }
        }
        response {
            type: object
            properties {
                tasks {
                    description: "List of report tasks"
                    type: array
                    items { "$ref": "#/definitions/report" }
                }
                scroll_id {
                    type: string
                    description: "Scroll ID that can be used with the next calls to get_all to retrieve more data"
                }
            }
        }
    }
    "2.26": ${get_all_ex."2.23"} {
        request.properties.include_subprojects {
            description: "If set to 'true' and project field is set then reports from the subprojects are searched too"
            type: boolean
            default: false
        }
    }
    "2.27": ${get_all_ex."2.26"} {
        request.properties {
            filters {
                type: object
                additionalProperties: ${_definitions.field_filter}
            }
        }
    }
}
get_tags {
    "2.23" {
        description: "Get all the user tags used for the company reports"
        request {
            type: object
            additionalProperties: false
        }
        response {
            type: object
            properties {
                tags {
                    description: "The list of unique tag values"
                    type: array
                    items {type: string}
                }
            }
        }
    }
}
