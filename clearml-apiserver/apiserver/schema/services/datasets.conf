_description: "Provides a management API for datasets in the system."
_definitions:{
    dataset {
        type: object
        properties {
            id {
                description: "Dataset ID"
                type: string
            }
            name {
                description: "Dataset Name"
                type: string
            }
            description {
                description: "Dataset description"
                type: string
            }
            type {
                description: "Dataset type"
                type: string
            }
            size {
                description: "Dataset szie"
                type: string
            }
            is_public {
                description: "This dataset is public or not"
                type: boolean
            }
            versions_num {
                description: "The count number of datasets' versions"
                type: string
            }
            last_update {
                description: "Last dataset update time"
                type: string
                format: "date-time"
            }
            create_time {
                description: "Dataset create time"
                type: string
                format: "date-time"
            }
        }
    }
}
dataset_file_list{
    "1.0" {
        description: "The dataset list"
        request{
            type: object
            properties{
                dataset_name {
                    description: "The dataset name of file to upload"
                    type: string
                }
                version {
                    description: "The file version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "Datasets names"
                    type: string
                }
                size {
                    description: "Dataset szie"
                    type: string
                }
                Etag {
                    description: "Dataset etag"
                    type: string
                }
                last_update {
                    description: "Last dataset update time"
                    type: string
                    format: "date-time"
                }      
            }
        }
    }
}
upload_dataset_file {
    "1.0" {
        description: "upload file to dataset"
        request {
            type: object
            properties {
                dir_name {
                    description: "The name of directory to upload"
                    type: string
                }
                dataset_name {
                    description: "The dataset name of file to upload"
                    type: string
                }
                version {
                    description: "The file version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "Datasets names"
                    type: string
                }
                size {
                    description: "Dataset szie"
                    type: string
                }
                last_update {
                    description: "Last dataset update time"
                    type: string
                    format: "date-time"
                }      
            }
        }
    }
}
upload_dataset_file_by_cmd {
    "1.0" {
        description: "upload file to dataset"
        request {
            type: object
            properties {
                dataset_name {
                    description: "The dataset id of file to upload"
                    type: string
                }
                version {
                    description: "The dataset version"
                    type: string
                }
                dir_name {
                    description: "The name of directory to upload"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "Datasets names"
                    type: string
                }
                size {
                    description: "Dataset szie"
                    type: string
                }
                last_update {
                    description: "Last dataset update time"
                    type: string
                    format: "date-time"
                }      
            }
        }
    }
}
delete_dataset_file {
    "1.0" {
        description: "upload file to dataset"
        request {
            type: object
            properties {
                file_name {
                    description: "The name of file to upload"
                    type: string
                }
                dataset_name {
                    description: "The dataset name of file to upload"
                    type: string
                }
                version {
                    description: "The file version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "Datasets names"
                    type: string
                }
                size {
                    description: "Dataset szie"
                    type: string
                }
                last_update {
                    description: "Last dataset update time"
                    type: string
                    format: "date-time"
                }      
            }
        }
    }
}
batch_delete_dataset_file {
    "1.0" {
        description: "delete file from dataset"
        request {
            type: object
            properties {
                batch_file_names {
                    description: "The names of files to download"
                    type: string
                }
                dataset_name {
                    description: "The dataset name of file to download"
                    type: string
                }
                version {
                    description: "The dataset version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                result {
                    description: "The files name which were deleted"
                    type: string
                }      
            }
        }
    }
}
download_dataset_file {
    "1.0" {
        description: "upload file to dataset"
        request {
            type: object
            properties {
                file_name {
                    description: "The name of file to upload"
                    type: string
                }
                dataset_name {
                    description: "The dataset name of file to upload"
                    type: string
                }
                version {
                    description: "The file version"
                    type: string
                }
                download_location{
                    description: "The location to download"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "Datasets names"
                    type: string
                }
                size {
                    description: "Dataset szie"
                    type: string
                }
                last_update {
                    description: "Last dataset update time"
                    type: string
                    format: "date-time"
                }      
            }
        }
    }
}
batch_download_dataset_file {
    "1.0" {
        description: "download file from dataset"
        request {
            type: object
            properties {
                batch_file_names {
                    description: "The names of files to download"
                    type: string
                }
                dataset_name {
                    description: "The dataset name of file to download"
                    type: string
                }
                version {
                    description: "The dataset version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                url {
                    description: "Supply url to download the zip file which contains all files you want to download"
                    type: string
                }      
            }
        }
    }
}
create_dataset {
    "1.0" {
        description: "Create a new dataset"
        request {
            type: object
            required :[name,is_public,dataset_type]
            properties {
                name {
                    description: "Dataset name"
                    type: string
                }
                description {
                    description: "Dataset description."
                    type: string
                }
                is_public {
                    description: "Dataset is public or not"
                    type: string
                    enum: [private, presets, public]
                }
                dataset_type {
                    description: "The type of Dataset"
                    type: string
                    enum: [ img, txt, char, radio, video, self-defined ]
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "Dataset id"
                    type: string
                }
            }
        }
    }
}
get_id_by_name {
    "1.0" {
        description: "Find dataset's id by its name"
        request {
            type: object
            required :[dataset_name]
            properties {
                dataset_name {
                    description: "Dataset name"
                    type: string
                }
                description {
                    description: "Dataset description."
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "Dataset id"
                    type: string
                }
            }
        }
    }
}

# delete {
#     "1.0" {
#         description: "Deletes a dataset"
#         request {
#             type: object
#             required: [ dataset ]
#             properties {
#                 dataset {
#                     description: "dataset ID"
#                     type: string
#                 }
#                 force {
#                     description: """If not true, fails if dataset has tasks.
#                     If true, and dataset has data file, they will be unassigned"""
#                     type: boolean
#                     default: false
#                 }
#             }
#         }
#         response {
#             type: object
#             properties {
#                 deleted {
#                     description: "Number of datasets deleted (0 or 1)"
#                     type: integer
#                 }
#                 disassociated_tasks {
#                     description: "Number of data file disassociated from the deleted dataset"
#                     type: integer
#                 }
#             }
#         }
#     }
# }
get_all_dataset {
    "1.0" {
        description: "Get all datasets"
        request {
            type: object
            properties {
                name {
                    description: "Dataset name"
                    type: string
                }
                dataset_type{
                    description: "Dataset type"
                    type: string
                }
                is_public{
                    description:"is publish"
                    type: string
                }
                only_fields {
                    description: "List of dataset field names"
                    type: array
                    items { type: string }
                }
                page {
                    description: "Page number, returns a specific page out of the resulting list of users"
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                order_by {
                    description: "List of field names to order by. Use '-' prefix to specify descending order. Optional, recommended when using page"
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties {
                datasets {
                    description: "datasets list"
                    type: array
                    items { "$ref": "#/definitions/dataset" }
                }
            }
        }
    }
}
delete_dataset{
        "1.0" {
        description: "delete dataset from mongo"
        request {
            type: object
            required :[id]
            properties {
                id {
                    description: "id of dataset for delete"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "Dataset id"
                    type: string
                }
            }
        }
    }
}
update_dataset {
    internal: false
    "1.0" {
        description: "Update dataset"
        request {
            type: object
            required: [ id ]
            properties {
                id {
                    description: "id of dataset for update"
                    type: string
                }
                description {
                    description: "Dataset desc"
                    type: string
                }
                is_public {
                    description: "Dataset is public or not"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: Number of updated user objects (0 or 1)
                    type: integer
                }
                fields {
                    description: Updated fields names and values
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
}
create_dataset_version {
    "1.0" {
        description: "Create a new dataset"
        request {
            type: object
            required :[dataset_id, version]
            properties {
                dataset_id {
                    description: "Dataset id"
                    type: string
                }
                version {
                    description: "Dataset version name"
                    type: string
                }
                description {
                    description: "Dataset version description."
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "Dataset version name "
                    type: string
                }
            }
        }
    }
}
get_all_dataset_version {
    "1.0" {
        description: "Get all datasets"
        required :[dataset_id]
        request {
            type: object
            properties {
                dataset_id {
                    description: "Dataset id"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                datasets {
                    description: "datasets version list"
                    type: array
                    items { "$ref": "#/definitions/dataset" }
                }
            }
        }
    }
}
download_dataset_version{
    "1.0" {
        description: "download version file to dataset"
        request {
            type: object
            properties {
                dataset_id {
                    description: "Dataset id"
                    type: string
                }
                version {
                    description: "Dataset version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                url {
                    description: "Download Datasets version file url"
                    type: string
                }  
            }
        }
    }
}
statistics{
    "1.0" {
        description: "statistics count"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                statistics {
                    description: "statistics count"
                    type: object
                }  
            }
        }
    }
}

kinds{
    allow_roles = [ "*" ]
    "1.0" {
        description: "获取数据类型"
        request{
        }
        response {
            type: object
            properties {
                value {
                    description: "类型枚举值"
                    type: string
                }
                name {
                    description: "类型枚举中文名"
                    type: string
                }
            }
        }
    }
}

list{
    allow_roles = [ "*" ]
    "1.0" {
        description: "The dataset list"
        request{
            type: object
            properties{
                name {
                    description: "数据集合模糊查询名称"
                    type: string
                }
                kind {
                    description: "数据集合的类型"
                    type: string
                }
                algo_id {
                    description: "关联的算法ID"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "Datasets names"
                    type: string
                }
            }
        }
    }
}

list_test{
    allow_roles = [ "*" ]
    "1.0" {
        description: "The dataset list"
        request{
            type: object
            properties{
                name {
                    description: "数据集合模糊查询名称"
                    type: string
                }
                kind {
                    description: "数据集合的类型"
                    type: string
                }
                algo_id {
                    description: "关联的算法ID"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "Datasets names"
                    type: string
                }
            }
        }
    }
}


versions{
    allow_roles = [ "*" ]
    "1.0" {
        description: "获取数据集合版本列表"
        request{
            type: object
            properties{
                dataset_id {
                    description: "数据集合ID"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "版本id"
                    type: int
                }
                version {
                    description: "版本号"
                    type: string
                }
            }
        }
    }
}

details{
    allow_roles = [ "*" ]
    "1.0" {
        description: "获取版本对应的文件列表"
        request{
            type: object
            properties{
                dataset_id {
                    description: "数据集合ID"
                    type: string
                }
                version_id {
                    description: "版本ID"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                filename {
                    description: "文件名"
                    type: string
                }
                filesize {
                    description: "文件大小"
                    type: int
                }
            }
        }
    }
}

