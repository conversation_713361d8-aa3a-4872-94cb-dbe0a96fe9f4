_description: """This service provides an administrator management interface to the company's users login information."""

_default {
    internal: false
    allow_roles: ["system", "root", "admin"]
}

supported_modes {
    authorize: null
    "2.9" {
        description:  """ Return supported login modes."""
        request {
            type: object
            additionalProperties: false
        }
        response {
            type: object
            properties {
                basic {
                    type: object
                    properties {
                        enabled {
                            description: "Basic aothentication (fixed users mode) mode enabled"
                            type: boolean
                        }
                        guest {
                            type: object
                            properties {
                                enabled {
                                    description: "Basic aothentication guest mode enabled"
                                    type: boolean
                                }
                                name {
                                    description: "Guest name"
                                    type: string
                                }
                                username {
                                    description: "Guest username"
                                    type: string
                                }
                                password {
                                    description: "Guest password"
                                    type: string
                                }
                            }
                        }
                    }
                }
                sso {
                    description: "SSO authentication providers"
                    type: object
                    additionalProperties {
                        description: "Provider redirect URL"
                        type: string
                    }
                }
                sso_providers {
                    description: "The list of SSO authentication providers"
                    type: array
                    items {
                        type: object
                        additionalProperties: true
                    }
                }
                server_errors {
                    description: "Server initialization errors"
                    type: object
                    properties {
                        missed_es_upgrade {
                            description: "Indicate that Elasticsearch database was not upgraded from version 5"
                            type: boolean
                        }
                        es_connection_error {
                            description: "Indicate an error communicating to Elasticsearch"
                            type: boolean
                        }
                    }
                }
                authenticated {
                    description: "Is user authenticated"
                    type: boolean
                }
            }
        }
    }
}

logout {
    authorize: null
    allow_roles = [ "*" ]
    "2.13" {
        description: """ Logout (including SSO, if used)) """
        request {
            type: object
            additionalProperties: false
        }
        response {
            type: object
            additionalProperties: false
        }
    }
}
