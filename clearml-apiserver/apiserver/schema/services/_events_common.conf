scalar_key_enum {
    type: string
    enum: [
        iter
        timestamp
        iso_time
    ]
}
metric_variants {
    type: object
    properties {
        metric {
            description: The metric name
            type: string
        }
        variants {
            type: array
            description: The names of the metric variants
            items {type: string}
        }
    }
}
debug_images_response_task_metrics {
    type: object
    properties {
        task {
            type: string
            description: Task ID
        }
        iterations {
            type: array
            items {
                type: object
                properties {
                    iter {
                        type: integer
                        description: Iteration number
                    }
                    events {
                        type: array
                        items {
                            type: object
                            description: Debug image event
                        }
                    }
                }
            }
        }
    }
}
debug_images_response {
    type: object
    properties {
        scroll_id {
            type: string
            description: "Scroll ID for getting more results"
        }
        metrics {
            type: array
            description: "Debug image events grouped by tasks and iterations"
            items {"$ref": "#/definitions/debug_images_response_task_metrics"}
        }
    }
}
plots_response_task_metrics {
    type: object
    properties {
        task {
            type: string
            description: Task ID
        }
        iterations {
            type: array
            items {
                type: object
                properties {
                    iter {
                        type: integer
                        description: Iteration number
                    }
                    events {
                        type: array
                        items {
                            type: object
                            description: Plot event
                        }
                    }
                }
            }
        }
    }
}
plots_response {
    type: object
    properties {
        scroll_id {
            type: string
            description: "Scroll ID for getting more results"
        }
        metrics {
            type: array
            description: "Plot events grouped by tasks and iterations"
            items {"$ref": "#/definitions/plots_response_task_metrics"}
        }
    }
}
single_value_task_metrics {
    type: object
    properties {
        task {
            type: string
            description: Task ID
        }
        task_name {
            type: string
            description: Task name
        }
        values {
            type: array
            items {
                type: object
                properties {
                    metric { type: string }
                    variant { type: string}
                    value { type: number }
                    timestamp { type: number }
                }
            }
        }
    }
}
single_value_metrics_response {
    type: object
    properties {
        tasks {
            description: Single value metrics grouped by task
            type: array
            items {"$ref": "#/definitions/single_value_task_metrics"}
        }
    }
}