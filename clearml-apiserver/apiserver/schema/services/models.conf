_description: """This service provides a management interface for models (results of training tasks) stored in the system."""
_definitions {
    include "_tasks_common.conf"
    multi_field_pattern_data {
        type: object
        properties {
            pattern {
                description: "Pattern string (regex)"
                type: string
            }
            fields {
                description: "List of field names"
                type: array
                items { type: string }
            }
        }
    }
    model {
        type: object
        properties {
            id {
                description: "Model id"
                type: string
            }
            name {
                description: "Model name"
                type: string
            }
            user {
                description: "Associated user id"
                type: string
            }
            company {
                description: "Company id"
                type: string
            }
            created {
                description: "Model creation time"
                type: string
                format: "date-time"
            }
            last_update {
                description: "Model last update time"
                type: string
                format: "date-time"
            }
            task {
                description: "Task ID of task in which the model was created"
                type: string
            }
            parent {
                description: "Parent model ID"
                type: string
            }
            project {
                description: "Associated project ID"
                type: string
            }
            comment {
                description: "Model comment"
                type: string
            }
            tags {
                type: array
                description: "User-defined tags"
                items { type: string }
            }
            system_tags {
                type: array
                description: "System tags. This field is reserved for system use, please don't use it."
                items { type: string }
            }
            framework {
                description: "Framework on which the model is based. Should be identical to the framework of the task which created the model"
                type: string
            }
            design {
                description: "Json object representing the model design. Should be identical to the network design of the task which created the model"
                type: object
                additionalProperties: true
            }
            labels {
                description: "<PERSON>son object representing the ids of the labels in the model. The keys are the layers' names and the values are the ids."
                type: object
                additionalProperties { type: integer }
            }
            uri {
                description: "URI for the model, pointing to the destination storage."
                type: string
            }
            ready {
                description: "Indication if the model is final and can be used by other tasks"
                type: boolean
            }
            ui_cache {
                description: "UI cache for this model"
                type: object
                additionalProperties: true
            }
            metadata {
                description: "Model metadata"
                type: object
                additionalProperties {
                    "$ref": "#/definitions/metadata_item"
                }
            }
            last_iteration {
                description: "Last iteration reported for this model"
                type: integer
            }
            last_metrics {
                description: "Last metric variants (hash to events), one for each metric hash"
                type: object
                additionalProperties {
                    "$ref": "#/definitions/last_metrics_variants"
                }
            }
            stats {
                description: "Model statistics"
                type: object
                properties {
                    labels_count {
                        description: Number of the model labels
                        type: integer
                    }
                }
            }
        }
    }
    published_task_item {
        description: "Result of publishing of the model's associated task (if exists). Returned only if the task was published successfully as part of the model publishing."
        type: object
        properties {
            id {
                description: "Task id"
                type: string
            }
            data {
                description: "Data returned from the task publishing operation."
                type: object
                properties {
                    updated {
                        description: "Number of tasks updated (0 or 1)"
                        type: integer
                        enum: [ 0, 1 ]
                    }
                    fields {
                        description: "Updated fields names and values"
                        type: object
                        additionalProperties: true
                    }
                }
            }
        }
    }
}

get_by_id {
    "2.1" {
        description: "Gets model information"
        request {
            type: object
            required: [ model ]
            properties {
                model {
                    description: "Model id"
                    type: string
                }
            }
        }

        response {
            type: object
            properties {
                model {
                    description: "Model info"
                    "$ref": "#/definitions/model"
                }
            }
        }
    }
}

get_by_task_id {
    "2.1" {
        description: "Gets model information"
        request {
            type: object
            properties {
                task {
                    description: "Task id"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                model {
                    description: "Model info"
                    "$ref": "#/definitions/model"
                }
            }
        }
    }
}
get_by_id_ex {
    internal: true
    "2.11": ${get_all_ex."2.1"}
}
get_all_ex {
    internal: true
    "2.1": ${get_all."2.1"}
    "2.13": ${get_all_ex."2.1"} {
        request {
            properties {
                include_subprojects {
                    description: "If set to 'true' and project field is set then models from the subprojects are searched too"
                    type: boolean
                    default: false
                }
            }
        }
    }
    "2.15": ${get_all_ex."2.13"} {
        request {
            properties {
                scroll_id {
                    type: string
                    description: "Scroll ID returned from the previos calls to get_all_ex"
                }
                refresh_scroll {
                    type: boolean
                    description: "If set then all the data received with this scroll will be requeried"
                }
                size {
                    type: integer
                    minimum: 1
                    description: "The number of models to retrieve"
                }
            }
        }
        response.properties.scroll_id {
            type: string
            description: "Scroll ID that can be used with the next calls to get_all_ex to retrieve more data"
        }
    }
    "2.20": ${get_all_ex."2.15"} {
        request.properties.include_stats {
            description: "If true, include models statistic in response"
            type: boolean
            default: false
        }
    }
    "2.23": ${get_all_ex."2.20"} {
        request.properties {
            allow_public {
                description: "Allow public models to be returned in the results"
                type: boolean
                default: true
            }
        }
    }
    "2.27": ${get_all_ex."2.23"} {
        request.properties {
            filters {
                type: object
                additionalProperties: ${_definitions.field_filter}
            }
        }
    }
}
get_all {
    "2.1" {
        description: "Get all models"
        request {
            type: object
            properties {
                name {
                    description: "Get only models whose name matches this pattern (python regular expression syntax)"
                    type: string
                }
                user {
                    description: "List of user IDs used to filter results by the model's creating user"
                    type: array
                    items { type: string }
                }
                ready {
                    description: "Indication whether to retrieve only models that are marked ready If not supplied returns both ready and not-ready projects."
                    type: boolean
                }
                tags {
                    description: "User-defined tags list used to filter results. Prepend '-' to tag name to indicate exclusion"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list used to filter results. Prepend '-' to system tag name to indicate exclusion"
                    type: array
                    items { type: string }
                }
                only_fields {
                    description: "List of model field names (if applicable, nesting is supported using '.'). If provided, this list defines the query's projection (only these fields will be returned for each result entry)"
                    type: array
                    items { type: string }
                }
                page {
                    description: "Page number, returns a specific page out of the resulting list of models"
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                project {
                    description: "List of associated project IDs"
                    type: array
                    items { type: string }
                }
                order_by {
                    description: "List of field names to order by. When search_text is used, '@text_score' can be used as a field representing the text score of returned documents. Use '-' prefix to specify descending order. Optional, recommended when using page"
                    type: array
                    items { type: string }
                }
                task {
                    description: "List of associated task IDs"
                    type: array
                    items { type: string }
                }
                id {
                    description: "List of model IDs"
                    type: array
                    items { type: string }
                }
                search_text {
                    description: "Free text search query"
                    type: string
                }
                framework {
                    description: "List of frameworks"
                    type: array
                    items { type: string }
                }
                uri {
                    description: "List of model URIs"
                    type: array
                    items { type: string }
                }
                last_update {
                    description: "List of last_update constraint strings (utcformat, epoch) with an optional prefix modifier (>, >=, <, <=)"
                    type: array
                    items {
                        type: string
                        pattern: "^(>=|>|<=|<)?.*$"
                    }
                }
                _all_ {
                    description: "Multi-field pattern condition (all fields match pattern)"
                    "$ref": "#/definitions/multi_field_pattern_data"
                }
                _any_ {
                    description: "Multi-field pattern condition (any field matches pattern)"
                    "$ref": "#/definitions/multi_field_pattern_data"
                }
            }
        }
        response {
            type: object
            properties {
                models: {
                    description: "Models list"
                    type: array
                    items { "$ref": "#/definitions/model" }
                }
            }
        }
    }
    "2.15": ${get_all."2.1"} {
        request {
            properties {
                scroll_id {
                    type: string
                    description: "Scroll ID returned from the previos calls to get_all"
                }
                refresh_scroll {
                    type: boolean
                    description: "If set then all the data received with this scroll will be requeried"
                }
                size {
                    type: integer
                    minimum: 1
                    description: "The number of models to retrieve"
                }
            }
        }
        response.properties.scroll_id {
            type: string
            description: "Scroll ID that can be used with the next calls to get_all to retrieve more data"
        }
    }
    "2.26": ${get_all."2.15"} {
        request {
            properties {
                include_subprojects {
                    description: "If set to 'true' and project field is set then models from the subprojects are searched too"
                    type: boolean
                    default: false
                }
            }
        }
    }
}
get_frameworks {
    "2.8" {
        description: "Get the list of frameworks used in the company models"
        request {
            type: object
            properties {
                projects {
                    description: "The list of projects which models will be analyzed. If not passed or empty then all the company and public models will be analyzed"
                    type: array
                    items: {type: string}
                }
            }
        }
        response {
            type: object
            properties {
                frameworks {
                    description: "Unique list of the frameworks used in the company models"
                    type: array
                    items: {type: string}
                }
            }
        }
    }
}
update_for_task {
    "2.1" {
        description: "Create or update a new model for a task"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    description: "Task id"
                    type: string
                }
                uri {
                    description: "URI for the model. Exactly one of uri or override_model_id is a required."
                    type: string
                }
                name {
                    description: "Model name Unique within the company."
                    type: string
                }
                comment {
                    description: "Model comment"
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
                override_model_id {
                    description: "Override model ID. If provided, this model is updated in the task. Exactly one of override_model_id or uri is required."
                    type: string
                }
                iteration {
                    description: "Iteration (used to update task statistics)"
                    type: integer
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "ID of the model"
                    type: string
                }
                created {
                    description: "Was the model created"
                    type: boolean
                }
                updated {
                    description: "Number of models updated (0 or 1)"
                    type: integer
                }
                fields {
                    description: "Updated fields names and values"
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
}
create {
    "2.1" {
        description: "Create a new model not associated with a task"
        request {
            type: object
            required: [
                uri
                name
            ]
            properties {
                uri {
                    description: "URI for the model"
                    type: string
                }
                name {
                    description: "Model name Unique within the company."
                    type: string
                }
                comment {
                    description: "Model comment"
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
                framework {
                    description: "Framework on which the model is based. Case insensitive. Should be identical to the framework of the task which created the model."
                    type: string
                }
                design {
                    description: "Json[d] object representing the model design. Should be identical to the network design of the task which created the model"
                    type: object
                    additionalProperties: true
                }
                labels {
                    description: "Json object"
                    type: object
                    additionalProperties { type: integer }
                }
                ready {
                    description: "Indication if the model is final and can be used by other tasks. Default is false."
                    type: boolean
                    default: false
                }
                public {
                    description: "Create a public model Default is false."
                    type: boolean
                    default: false
                }
                project {
                    description: "Project to which to model belongs"
                    type: string
                }
                parent {
                    description: "Parent model"
                    type: string
                }
                task {
                    description: "Associated task ID"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "ID of the model"
                    type: string
                }
                created {
                    description: "Was the model created"
                    type: boolean
                }
            }
        }
    }
    "2.13": ${create."2.1"} {
        metadata {
            description: "Model metadata"
            type: object
            additionalProperties {
                "$ref": "#/definitions/metadata_item"
            }
        }
    }
}
edit {
    "2.1" {
        description: "Edit an existing model"
        request {
            type: object
            required: [
                model
            ]
            properties {
                model {
                    description: "Model ID"
                    type: string
                }
                uri {
                    description: "URI for the model"
                    type: string
                }
                name {
                    description: "Model name Unique within the company."
                    type: string
                }
                comment {
                    description: "Model comment"
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
                framework {
                    description: "Framework on which the model is based. Case insensitive. Should be identical to the framework of the task which created the model."
                    type: string
                }
                design {
                    description: "Json[d] object representing the model design. Should be identical to the network design of the task which created the model"
                    type: object
                    additionalProperties: true
                }
                labels {
                    description: "Json object"
                    type: object
                    additionalProperties { type: integer }
                }
                ready {
                    description: "Indication if the model is final and can be used by other tasks"
                    type: boolean
                }
                project {
                    description: "Project to which to model belongs"
                    type: string
                }
                parent {
                    description: "Parent model"
                    type: string
                }
                task {
                    description: "Associated task ID"
                    type: string
                }
                iteration {
                    description: "Iteration (used to update task statistics)"
                    type: integer
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of models updated (0 or 1)"
                    type: integer
                    enum: [0, 1]
                }
                fields {
                    description: "Updated fields names and values"
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
    "2.13": ${edit."2.1"} {
        metadata {
            description: "Model metadata"
            type: object
            additionalProperties {
                "$ref": "#/definitions/metadata_item"
            }
        }
    }
}
update {
    "2.1" {
        description: "Update a model"
        request {
            type: object
            required: [ model ]
            properties {
                model {
                    description: "Model id"
                    type: string
                }
                name {
                    description: "Model name Unique within the company."
                    type: string
                }
                comment {
                    description: "Model comment"
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
                ready {
                    description: "Indication if the model is final and can be used by other tasks Default is false."
                    type: boolean
                    default: false
                }
                created {
                    description: "Model creation time (UTC) "
                    type: string
                    format: "date-time"
                }
                ui_cache {
                    description: "UI cache for this model"
                    type: object
                    additionalProperties: true
                }
                project {
                    description: "Project to which to model belongs"
                    type: string
                }
                task {
                    description: "Associated task ID"
                    type: "string"
                }
                iteration {
                    description: "Iteration (used to update task statistics if an associated task is reported)"
                    type: integer
                }

            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of models updated (0 or 1)"
                    type: integer
                    enum: [0, 1]
                }
                fields {
                    description: "Updated fields names and values"
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
    "2.13": ${update."2.1"} {
        metadata {
            description: "Model metadata"
            type: object
            additionalProperties {
                "$ref": "#/definitions/metadata_item"
            }
        }
    }
}
publish_many {
    "2.13": ${_definitions.batch_operation} {
        description: Publish models
        request {
            properties {
                ids.description: "IDs of the models to publish"
                force_publish_task {
                    description: "Publish the associated tasks (if exist) even if they are not in the 'stopped' state. Optional, the default value is False."
                    type: boolean
                }
                publish_tasks {
                    description: "Indicates that the associated tasks (if exist) should be published. Optional, the default value is True."
                    type: boolean
                }
            }
        }
        response {
            properties {
                succeeded.items.properties.updated {
                    description: "Indicates whether the model was updated"
                    type: boolean
                }
                succeeded.items.properties.published_task: ${_definitions.published_task_item}
            }
        }
    }
}
set_ready {
    "2.1" {
        description: "Set the model ready flag to True. If the model is an output model of a task then try to publish the task."
        request {
            type: object
            required: [ model ]
            properties {
                model {
                    description: "Model id"
                    type: string
                }
                force_publish_task {
                    description: "Publish the associated task (if exists) even if it is not in the 'stopped' state. Optional, the default value is False."
                    type: boolean
                }
                publish_task {
                    description: "Indicates that the associated task (if exists) should be published. Optional, the default value is True."
                    type: boolean
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of models updated (0 or 1)"
                    type: integer
                    enum: [0, 1]
                }
                published_task: ${_definitions.published_task_item}
            }
        }
    }
}
archive_many {
    "2.13": ${_definitions.batch_operation} {
        description: Archive models
        request {
            properties {
                ids.description: "IDs of the models to archive"
            }
        }
        response {
            properties {
                succeeded.items.properties.archived {
                    description: "Indicates whether the model was archived"
                    type: boolean
                }
            }
        }
    }
}
unarchive_many {
    "2.13": ${_definitions.batch_operation} {
        description: Unarchive models
        request {
            properties {
                ids.description: "IDs of the models to unarchive"
            }
        }
        response {
            properties {
                succeeded.items.properties.unarchived {
                    description: "Indicates whether the model was unarchived"
                    type: boolean
                }
            }
        }
    }
}
delete_many {
    "2.13": ${_definitions.batch_operation} {
        description: Delete models
        request {
            properties {
                ids.description: "IDs of the models to delete"
                force {
                    description: "Force. Required if there are tasks that use the model as an execution model, or if the model's creating task is published."
                    type: boolean
                }
            }
        }
        response {
            properties {
                succeeded.items.properties.deleted {
                    description: "Indicates whether the model was deleted"
                    type: boolean
                }
                succeeded.items.properties.url {
                    description: "The url of the model file"
                    type: string
                }
            }
        }
    }
}
delete {
    "2.1" {
        description: "Delete a model."
        request {
            required: [
                model
            ]
            type: object
            properties {
                model {
                    description: "Model ID"
                    type: string
                }
                force {
                    description: """Force. Required if there are tasks that use the model as an execution model, or if the model's creating task is published.
                    """
                    type: boolean
                }

            }
        }
        response {
            type: object
            properties {
                deleted {
                    description: "Indicates whether the model was deleted"
                    type: boolean
                }

            }
        }
    }
    "2.13": ${delete."2.1"} {
        response {
            properties {
                url {
                    description: "The url of the model file"
                    type: string
                }
            }
        }
    }
}

make_public {
    "2.9" {
        description: """Convert company models to public"""
        request {
            type: object
            properties {
                ids {
                    description: "Ids of the models to convert"
                    type: array
                    items { type: string}
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of models updated"
                    type: integer
                }
            }
        }
    }
}

make_private {
    "2.9" {
        description: """Convert public models to private"""
        request {
            type: object
            properties {
                ids {
                    description: "Ids of the models to convert. Only the models originated by the company can be converted"
                    type: array
                    items { type: string}
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of models updated"
                    type: integer
                }
            }
        }
    }
}

move {
    "2.12" {
        description: "Move models to a project"
        request {
            type: object
            required: [ids]
            properties {
                ids {
                    description: "Models to move"
                    type: array
                    items { type: string }
                }
                project {
                    description: "Target project ID. If not provided, `project_name` must be provided. Use null for the root project"
                    type: string
                }
                project_name {
                    description: "Target project name. If provided and a project with this name does not exist, a new project will be created. If not provided, `project` must be provided."
                    type: string
                }
            }
        }
        response {
            type: object
            additionalProperties: true
        }
    }
}
add_or_update_metadata {
    "2.13" {
        description: "Add or update model metadata"
        request {
            type: object
            required: [model, metadata]
            properties {
                model {
                    description: "ID of the model"
                    type: string
                }
                metadata {
                    type: array
                    description: "Metadata items to add or update"
                    items {"$ref": "#/definitions/metadata_item"}
                }
                replace_metadata {
                    description: "If set then the all the metadata items will be replaced with the provided ones. Otherwise only the provided metadata items will be updated or added"
                    type: boolean
                    default: false
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of models updated (0 or 1)"
                    type: integer
                    enum: [0, 1]
                }
            }
        }
    }
}
delete_metadata {
    "2.13" {
        description: "Delete metadata from model"
        request {
            type: object
            required: [ model, keys ]
            properties {
                model {
                    description: "ID of the model"
                    type: string
                }
                keys {
                    description: "The list of metadata keys to delete"
                    type: array
                    items {type: string}
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of models updated (0 or 1)"
                    type: integer
                    enum: [0, 1]
                }
            }
        }
    }
}
update_tags {
    "2.27" {
        description: Add or remove tags from multiple models
        request {
            type: object
            properties {
                ids {
                    type: array
                    description: IDs of the models to update
                    items {type: string}
                }
                add_tags {
                    type: array
                    description: User tags to add
                    items {type: string}
                }
                remove_tags {
                    type: array
                    description: User tags to remove
                    items {type: string}
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    type: integer
                    description: The number of updated models
                }
            }
        }
    }
}