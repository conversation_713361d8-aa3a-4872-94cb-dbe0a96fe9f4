
_description: """This service provides authentication management and authorization
validation for the entire system."""
_default {
    internal: true
    allow_roles: ["system", "root"]
}

_definitions {
    include "_common.conf"
    credential_key {
        type: object
        properties {
            access_key {
                type: string
                description: ""
            }
            label {
                type: string
                description: Optional credentials label
            }
            last_used {
                type: string
                description: ""
                format: "date-time"
            }
            last_used_from {
                type: string
                description: ""
            }
        }
    }
    role {
        type: string
        enum: [ admin, superuser, user, annotator ]
    }
}

create_user {
    allow_roles: ["system", "root", "admin"]
    "2.1" {
        description: """Creates a new user auth entry. Intended for internal use. """
        request {
            type: object
            required: [
                name
                company
                email
            ]
            properties {
                name {
                    type: string
                    description: User name (makes the auth entry more readable)
                }
                password {
                    type: string
                }
                company {
                    type: string
                    description: Associated company ID
                }
                email {
                    type: string
                    description: Email address uniquely identifying the user
                }
                role {
                    description: User role
                    default: user
                    "$ref": "#/definitions/role"
                }
                given_name {
                    type: string
                    description: Given name
                }
                family_name {
                    type: string
                    description: Family name
                }
                avatar {
                    type: string
                    description: Avatar URL
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    type: string
                    description: New user ID
                }
            }
        }
    }
}


get_all_user {
    allow_roles: ["system", "root", "admin"]
    "2.1" {
        description: Get all user objects
        request {
            type: object
            properties {
                name {
                    description: "Get only users whose name matches this pattern (python regular expression syntax)"
                    type: string
                }
                id {
                    description: "List of user IDs used to filter results"
                    type: array
                    items { type: string }
                }
                only_fields {
                    description: "List of user field names (if applicable, nesting is supported using '.'). If provided, this list defines the query's projection (only these fields will be returned for each result entry)"
                    type: array
                    items { type: string }
                }
                page {
                    description: "Page number, returns a specific page out of the resulting list of users"
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                order_by {
                    description: "List of field names to order by. Use '-' prefix to specify descending order. Optional, recommended when using page"
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties {
                users {
                    description: User list
                    type: array
                    items { "$ref": "#/definitions/user" }
                }
            }
        }
    }
}

user_by_id {
    internal: false
    allow_roles: ["system", "root", "admin", "user"]
    "2.1" {
        description: Gets user information
        request {
            type: object
            required: [ user ]
            properties {
                user {
                    description: User ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                user {
                    description: User info
                    "$ref": "#/definitions/user"
                }
            }
        }
    }
}

verify_code {
    internal: false
    authorize: false
    "2.1" {
        description: verify code
        request {
            type: object
            required: [ captcha_image_url ]
            properties {
                file {
                    description: file ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                image_base64 {
                    type: string
                    description: image base64
                }
            }
        }
    }
}


delete_user {
    allow_roles: ["system", "root", "admin"]
    "2.1" {
        description: Delete user
        description: Delete a user
        request {
            type: object
            required: [ user ]
            properties {
                user {
                    description: ID of user to delete
                    type: string
                }
            }
        }
        response {
            type: object
            additionalProperties: false
        }
    }
}

update_user {
    allow_roles: ["root", "admin", "user"]
    "2.1" {
        description:  """ Edit a users' auth data properties"""
        request {
            type: object
            properties {
                user {
                    description: "User ID"
                    type: string
                }
                password {
                    description: "password"
                    type: string
                }
                email {
                    description: "email"
                    type: string
                }
                role {
                    description: "The new user's role within the company"
                    type: string
                    enum: [admin, superuser, user, annotator]
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of users updated (0 or 1)"
                    type: number
                    enum: [ 0, 1 ]
                }
                fields {
                    description: "Updated fields names and values"
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
}



login {
    internal: false
    authorize: false
    "2.1" {
        description: """Get a token based on supplied credentials (key/secret).
        Intended for use by users with key/secret credentials that wish to obtain a token
        for use with other services."""
        request {
            type: object
            properties {
                expiration_sec {
                    type: integer
                        description: """Requested token expiration time in seconds. 
                        Not guaranteed,  might be overridden by the service"""
                }
            }
        }
        response {
            type: object
            properties {
                token {
                    type: string
                    description: Token string
                }
            }
        }
    }
}

logout {
    allow_roles = [ "*" ]
    "2.2" {
        description: """Removes the authentication cookie from the current session"""
        request {
            type: object
            additionalProperties: false
        }
        response {
            type: object
            additionalProperties: false
        }
    }
}


refresh_captcha_image {
    authorize: false
    "2.2" {
        description: """refresh captcha image"""
        request {
            type: object
            additionalProperties: false
        }
        response {
            type: object
            additionalProperties: false
        }
    }
}

update_password {
    allow_roles = [ "*" ]
    "2.2" {
        description: """refresh captcha image"""
        request {
            type: object
            additionalProperties: false
        }
        response {
            type: object
            additionalProperties: false
        }
    }
}


get_other_session {
    allow_roles: ["system", "root", "admin", "user"]
    "2.1" {
        description: get other session
        request {
            type: object
            additionalProperties: false
        }
        response {
            type: object
            additionalProperties: false
        }
    }
}


reset {
    allow_roles: ["system", "root", "admin"]
    "2.2" {
        description: """refresh captcha image"""
        request {
            type: object
            additionalProperties: false
        }
        response {
            type: object
            additionalProperties: false
        }
    }
}