_description: """This service provides authentication management and authorization
validation for the entire system."""
task_statistics{
    "1.0" {
        description: "statistics count"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                statistics {
                    description: "statistics count"
                    type: object
                }  
            }
        }
    }
}
project_statistics{
    "1.0" {
        description: "statistics count"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                statistics {
                    description: "statistics count"
                    type: object
                }  
            }
        }
    }
}
get_all_project{
    "1.0" {
        description: "statistics count"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                statistics {
                    description: "statistics count"
                    type: object
                }  
            }
        }
    }
}
get_task_by_project{
    "1.0" {
        description: "statistics count"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                statistics {
                    description: "statistics count"
                    type: object
                }  
            }
        }
    }
}
get_project_status{
    "1.0" {
        description: "statistics count"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                statistics {
                    description: "statistics count"
                    type: object
                }  
            }
        }
    }
}