_description: "Provides support for defining Projects containing Tasks, Models and Dataset Versions."
_definitions {
    include "_common.conf"
    ide {
        type: object
        properties {
            id {
                description: "Ide id"
                type: string
            }
            name {
                description: "Ide name"
                type: string
            }
            description {
                description: "Ide description"
                type: string
            }
            user {
                description: "Associated user id"
                type: string
            }
            company {
                description: "Company id"
                type: string
            }
            created {
                description: "Creation time"
                type: string
                format: "date-time"
            }
            tags {
                description: "User-defined tags"
                type: array
                items { type: string }
            }
            last_update {
                description: "Last project update time. Reflects the last time the project metadata was changed or a task in this project has changed status"
                type: string
                format: "date-time"
            }
            cpu {
                description: "cpu"
                type: integer
                minimum: 0
            }
            mem {
                description: "mem"
                type: integer
                minimum: 0
            }
            gpu {
                description: "gpu"
                type: integer
                minimum: 0
            }
            image {
                description: "gpu"
                type: string
                format: "int"
            }
            is_public {
                description: "This ide is public or not"
                type: string
            }
        }
    }
}

get_by_id {
    internal: false
    "2.1" {
        description: Gets ide information
        request {
            type: object
            required: [ ide ]
            properties {
                ide {
                    description: Ide ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                ide {
                    description: Ide info
                    "$ref": "#/definitions/ide"
                }
            }
        }
    }
}

get_by_algo {
    internal: false
    "2.1" {
        description: Gets ide status
        request {
            type: object
            required: [ ide ]
            properties {
                algo {
                    description: algo ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                ide {
                    description: Ide info
                    "$ref": "#/definitions/ide"
                }
            }
        }
    }
}

get_all{
    "2.1" {
        description: Get all user objects
        request {
            type: object
            properties {
                name {
                    description: "Get only ides whose name matches this pattern (python regular expression syntax)"
                    type: string
                }
                id {
                    description: "List of ides IDs used to filter results"
                    type: array
                    items { type: string }
                }
                is_public{
                    description:"is publish"
                    type: string
                }
                status{
                    description: "List of ides status used to filter results"
                    type: array
                    items { type: string }
                }
                only_fields {
                    description: "List of ides field names (if applicable, nesting is supported using '.'). If provided, this list defines the query's projection (only these fields will be returned for each result entry)"
                    type: array
                    items { type: string }
                }
                page {
                    description: "Page number, returns a specific page out of the resulting list of ides"
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                order_by {
                    description: "List of field names to order by. Use '-' prefix to specify descending order. Optional, recommended when using page"
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties {
                users {
                    description: User list
                    type: array
                    items { "$ref": "#/definitions/ide" }
                }
            }
        }
    }
}

update{
    "2.1" {
        description: "Create a new ide"
        request {
            type: object
            required :[name]
            properties {
                name {
                    description: "Ide name Unique within the company."
                    type: string
                }
                description {
                    description: "Ide description."
                    type: string
                }
                tags {
                    description: "User-defined tags"
                    type: array
                    items { type: string }
                }
                algorithms {
                    description: "System tags. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
                image  {
                    description: "Ide image"
                    type: string
                }
                cpu  {
                    description: "Ide image"
                    type: int
                }
                mem  {
                    description: "Ide image"
                    type: int
                }
                gpu  {
                    description: "Ide image"
                    type: int
                }
                is_public  {
                    description: "Ide image"
                    type: string
                }
                resourceId  {
                    description: "Ide image"
                    type: int
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of updated user objects"
                    type: integer
                }
                fields {
                    description: Updated fields names and values
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
}

create{
    "2.1" {
        description: "Create a new ide"
        request {
            type: object
            required :[name]
            properties {
                name {
                    description: "Ide name Unique within the company."
                    type: string
                }
                description {
                    description: "Ide description."
                    type: string
                }
                tags {
                    description: "User-defined tags"
                    type: array
                    items { type: string }
                }
                algorithms {
                    description: "System tags. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
                image  {
                    description: "Ide image"
                    type: string
                }
                cpu  {
                    description: "Ide image"
                    type: int
                }
                mem  {
                    description: "Ide image"
                    type: int
                }
                is_public{
                    description:"is publish"
                    type: string
                }
                gpu  {
                    description: "Ide image"
                    type: int
                }

            }
        }
        response {
            type: object
            properties {
                id {
                    description: "Ide id"
                    type: string
                }
            }
        }
    }
}

create {
    "2.1" {
        description: Create a new ide object. Reserved for internal use.
        request {
            type: object
            required: [
                name
            ]
            properties {
                company {
                    description: Company ID
                    type: string
                }
                name {
                    description: Full name
                    type: string
                }
                description {
                    description: description
                    type: string
                }
                cpu {
                    description: cpu count
                    type: int
                }
                mem {
                    description: mem count
                    type: int
                }
                gpu {
                    description: gpu count
                    type: int
                }
            }
        }
        response {
            type: object
            properties {}
            additionalProperties: false
        }
    }
}

stop{
    "2.1" {
        description: "stops ide"
        request {
            type: object
            required: [
                ide
            ]
            properties {
                ide {
                    description: Company ID
                    type: array
                    items: string
                }
            }
        }
        response {
            type: object
            properties {}
            additionalProperties: false
        }
    }
}

start{
    "2.1" {
        description: "stops ide"
        request {
            type: object
            required: [
                ide
            ]
            properties {
                ide {
                    description: Ide ID
                    type: array
                    items: string
                }
            }
        }
        response {
            type: object
            properties {}
            additionalProperties: false
        }
    }
}

statistics{
    "2.1" {
        description: "stops ide"
        request {
            type: object
            required: [
            ]
            properties {
            }
        }
        response {
            type: object
            properties {}
            additionalProperties: false
        }
    }
}

delete{
    "2.1" {
        description: "delete ide"
        request {
            type: object
            required: [
                ide
            ]
            properties {
                ide {
                    description: Company ID
                    type: array
                    items: string
                }
            }
        }
        response {
            type: object
            properties {}
            additionalProperties: false
        }
    }
}

get_images{
    "2.1" {
        description: "get image lsit for ide"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                users {
                    description: User list
                    type: array
                    items: string
                }
            }
        }
    }
}

get_image_tags{
    "2.1" {
        description: "get image tags for ide image"
        request {
            type: object
            required: [
                image_name
            ]
            properties {
                image_name {
                    description: "image name"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                tags {
                    description: image tag list
                    type: array
                    items: string
                }
            }
        }
    }
}
get_algos{
    "2.1" {
        description: "get_algos"
        request {
            type: object
            required: [
            ]
            properties {
            }
        }
        response {
            type: object
            properties {}
            additionalProperties: false
        }
    }
}
get_resource{
    "2.1" {
        description: "get_resource"
        request {
            type: object
            required: [
            ]
            properties {
            }
        }
        response {
            type: object
            properties {}
            additionalProperties: false
        }
    }
}