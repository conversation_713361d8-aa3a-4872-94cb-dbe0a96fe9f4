_description: "Provides a management API for pipelines in the system."
_definitions {
    include "_common.conf"
}
delete_runs {
    "2.26": ${_definitions.batch_operation} {
        description: Delete pipeline runs
        request {
            required: [ids, project]
            properties {
                ids.description: "IDs of the pipeline runs to delete. Should be the ids of pipeline controller tasks"
                project {
                    description: "Pipeline project ids. When deleting at least one run should be left"
                    type: string
                }
            }
        }
        response {
            properties {
                succeeded.items.properties.deleted {
                    description: "Indicates whether the task was deleted"
                    type: boolean
                }
                succeeded.items.properties.updated_children {
                    description: "Number of child tasks whose parent property was updated"
                    type: integer
                }
                succeeded.items.properties.updated_models {
                    description: "Number of models whose task property was updated"
                    type: integer
                }
                succeeded.items.properties.deleted_models {
                    description: "Number of deleted output models"
                    type: integer
                }
            }
        }
    }
}
start_pipeline {
    "2.17" {
        description: "Start a pipeline"
        request {
            type: object
            required: [ task ]
            properties {
                task {
                    description: "ID of the task on which the pipeline will be based"
                    type: string
                }
                queue {
                    description: "Queue ID in which the created pipeline task will be enqueued"
                    type: string
                }
                args {
                    description: "Task arguments, name/value to be placed in the hyperparameters Args section"
                    type: array
                    items {
                        type: object
                        properties {
                            name: { type: string }
                            value: { type: string }
                        }
                    }
                }
            }
        }
        response {
            type: object
            properties {
                pipeline {
                    description: "ID of the new pipeline task"
                    type: string
                }
                enqueued {
                    description: "True if the task was successfuly enqueued"
                    type: boolean
                }
            }
        }
    }
    "2.28": ${start_pipeline."2.17"} {
        request.properties.verify_watched_queue {
            description: If passed then check wheter there are any workers watiching the queue
            type: boolean
            default: false
        }
        response.properties.queue_watched {
            description: Returns true if there are workers or autscalers working with the queue
            type: boolean
        }
    }
}