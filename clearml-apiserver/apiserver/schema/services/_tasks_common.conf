include "_common.conf"
task_type_enum {
    type: string
    enum: [
        training
        testing
        inference
        data_processing
        application
        monitor
        controller
        optimizer
        service
        qc
        custom
    ]
}
script {
    type: object
    properties {
        binary {
            description: "Binary to use when running the script"
            type: string
            default: python
        }
        repository {
            description: "Name of the repository where the script is located"
            type: string
        }
        tag {
            description: "Repository tag"
            type: string
        }
        branch  {
            description: "Repository branch id If not provided and tag not provided, default repository branch is used."
            type: string
        }
        version_num {
            description: "Version (changeset) number. Optional (default is head version) Unused if tag is provided."
            type: string
        }
        entry_point {
            description: "Path to execute within the repository"
            type: string
        }
        working_dir {
            description: "Path to the folder from which to run the script Default - root folder of repository"
            type: string
        }
        requirements {
            description: "A JSON object containing requirements strings by key"
            type: object
        }
        diff {
            description: "Uncommitted changes found in the repository when task was run"
            type: string
        }
    }
}
model_type_enum {
    type: string
    enum: ["input", "output"]
}
task_model_item {
    type: object
    required: [ name, model]
    properties {
        name {
            description: "The task model name"
            type: string
        }
        model {
            description: "The model ID"
            type: string
        }
    }
}
output {
        type: object
        properties {
            destination {
                description: "Storage id. This is where output files will be stored."
                type: string
            }
            model {
                description: "Model id."
                type: string
            }
            result {
                description: "Task result. Values: 'success', 'failure'"
                type: string
            }
            error {
                description: "Last error text"
                type: string
            }
        }
    }
task_execution_progress_enum {
    type: string
    enum: [
        unknown
        running
        stopping
        stopped
    ]
}
artifact_type_data {
    type: object
    properties {
        preview {
            description: "Description or textual data"
            type: string
        }
        content_type {
            description: "System defined raw data content type"
            type: string
        }
        data_hash {
            description: "Hash of raw data, without any headers or descriptive parts"
            type: string
        }
    }
}
artifact_mode_enum {
    type: string
    enum: [
        input
        output
    ]
    default: output
}
artifact {
    type: object
    required: [key, type]
    properties {
        key {
            description: "Entry key"
            type: string
        }
        type {
            description: "System defined type"
            type: string
        }
        mode {
            description: "System defined input/output indication"
            "$ref": "#/definitions/artifact_mode_enum"
        }
        uri {
            description: "Raw data location"
            type: string
        }
        content_size {
            description: "Raw data length in bytes"
            type: integer
        }
        hash {
            description: "Hash of entire raw data"
            type: string
        }
        timestamp {
            description: "Epoch time when artifact was created"
            type: integer
        }
        type_data {
            description: "Additional fields defined by the system"
            "$ref": "#/definitions/artifact_type_data"
        }
        display_data {
            description: "User-defined list of key/value pairs, sorted"
            type: array
            items {
                type: array
                items {
                    type: string  # can also be a number... TODO: upgrade the generator
                }
            }
        }
    }
}
artifact_id {
    type: object
    required: [key]
    properties {
        key {
            description: "Entry key"
            type: string
        }
        mode {
            description: "System defined input/output indication"
            "$ref": "#/definitions/artifact_mode_enum"
        }
    }
}
task_models {
    type: object
    properties {
        input {
            description: "The list of task input models"
            type: array
            items {"$ref": "#/definitions/task_model_item"}

        }
        output {
            description: "The list of task output models"
            type: array
            items {"$ref": "#/definitions/task_model_item"}
        }
    }
}
execution {
    type: object
    properties {
        queue {
            description: "Queue ID where task was queued."
            type: string
        }
        parameters {
            description: "Json object containing the Task parameters"
            type: object
            additionalProperties: true
        }
        model {
            description: "Execution input model ID Not applicable for Register (Import) tasks"
            type: string
        }
        model_desc {
            description: "Json object representing the Model descriptors"
            type: object
            additionalProperties: true
        }
        model_labels {
            description: """Json object representing the ids of the labels in the model.
            The keys are the layers' names and the values are the IDs.
            Not applicable for Register (Import) tasks.
            Mandatory for Training tasks"""
            type: object
            additionalProperties: { type: integer }
        }
        framework {
            description: """Framework related to the task. Case insensitive. Mandatory for Training tasks. """
            type: string
        }
        docker_cmd {
            description: "Command for running docker script for the execution of the task"
            type: string
        }
        artifacts {
            description: "Task artifacts"
            type: array
            items { "$ref": "#/definitions/artifact" }
        }
    }
}
last_metrics_event {
    type: object
    properties {
        metric {
            description: "Metric name"
            type: string
        }
        variant {
            description: "Variant name"
            type: string
        }
        value {
            description: "Last value reported"
            type: number
        }
        min_value {
            description: "Minimum value reported"
            type: number
        }
        min_value_iteration {
            description: "The iteration at which the minimum value was reported"
            type: integer
        }
        max_value {
            description: "Maximum value reported"
            type: number
        }
        max_value_iteration {
            description: "The iteration at which the maximum value was reported"
            type: integer
        }
    }
}
last_metrics_variants {
    type: object
    description: "Last metric events, one for each variant hash"
    additionalProperties {
        "$ref": "#/definitions/last_metrics_event"
    }
}
params_item {
    type: object
    properties {
        section {
            description: "Section that the parameter belongs to"
            type: string
        }
        name {
            description: "Name of the parameter. The combination of section and name should be unique"
            type: string
        }
        value {
            description: "Value of the parameter"
            type: string
        }
        type {
            description: "Type of the parameter. Optional"
            type: string
        }
        description {
            description: "The parameter description. Optional"
            type: string
        }
    }
}
configuration_item {
    type: object
    properties {
        name {
            description: "Name of the parameter. Should be unique"
            type: string
        }
        value {
            description: "Value of the parameter"
            type: string
        }
        type {
            description: "Type of the parameter. Optional"
            type: string
        }
        description {
            description: "The parameter description. Optional"
            type: string
        }
    }
}
section_params {
    description: "Task section params"
    type: object
    additionalProperties {
        "$ref": "#/definitions/params_item"
    }
}
task {
    type: object
    properties {
        id {
            description: "Task id"
            type: string
        }
        name {
            description: "Task Name"
            type: string
        }
        user {
            description: "Associated user id"
            type: string
        }
        company {
            description: "Company ID"
            type: string
        }
        type {
            description: "Type of task. Values: 'training', 'testing'"
            "$ref": "#/definitions/task_type_enum"
        }
        status {
            description: ""
            "$ref": "#/definitions/task_status_enum"
        }
        comment {
            description: "Free text comment"
            type: string
        }
        created {
            description: "Task creation time (UTC) "
            type: string
            format: "date-time"
        }
        started {
            description: "Task start time (UTC)"
            type: string
            format: "date-time"
        }
        completed {
            description: "Task end time (UTC)"
            type: string
            format: "date-time"
        }
        active_duration {
            description: "Task duration time (seconds)"
            type: integer
        }
        parent {
            description: "Parent task id"
            type: string
        }
        project {
            description: "Project ID of the project to which this task is assigned"
            type: string
        }
        output {
            description: "Task output params"
            "$ref": "#/definitions/output"
        }
        execution {
            description: "Task execution params"
            "$ref": "#/definitions/execution"
        }
        container {
            description: "Docker container parameters"
            type: object
            additionalProperties { type: string }
        }
        models {
            description: "Task models"
            "$ref": "#/definitions/task_models"
        }
        // TODO: will be removed
        script {
            description: "Script info"
            "$ref": "#/definitions/script"
        }
        tags {
            description: "User-defined tags list"
            type: array
            items { type: string }
        }
        system_tags {
            description: "System tags list. This field is reserved for system use, please don't use it."
            type: array
            items { type: string }
        }
        status_changed {
            description: "Last status change time"
            type: string
            format: "date-time"
        }
        status_message {
            description: "free text string representing info about the status"
            type: string
        }
        status_reason {
            description: "Reason for last status change"
            type: string
        }
        published {
            description: "Task publish time"
            type: string
            format: "date-time"
        }
        last_worker {
            description: "ID of last worker that handled the task"
            type: string
        }
        last_worker_report {
            description: "Last time a worker reported while working on this task"
            type: string
            format: "date-time"
        }
        last_update {
            description: "Last time this task was created, edited, changed or events for this task were reported"
            type: string
            format: "date-time"
        }
        last_change {
            description: "Last time any update was done to the task"
            type: string
            format: "date-time"
        }
        last_iteration {
            description: "Last iteration reported for this task"
            type: integer
        }
        last_metrics {
            description: "Last metric variants (hash to events), one for each metric hash"
            type: object
            additionalProperties {
                "$ref": "#/definitions/last_metrics_variants"
            }
        }
        hyperparams {
            description: "Task hyper params per section"
            type: object
            additionalProperties {
                "$ref": "#/definitions/section_params"
            }
        }
        configuration {
            description: "Task configuration params"
            type: object
            additionalProperties {
                "$ref": "#/definitions/configuration_item"
            }
        }
        runtime {
            description: "Task runtime mapping"
            type: object
            additionalProperties: true
        }
    }
}
