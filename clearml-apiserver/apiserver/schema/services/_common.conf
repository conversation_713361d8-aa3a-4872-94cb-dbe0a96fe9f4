field_filter {
    type: object
    description: Filter on a field that includes combination of 'any' or 'all' included and excluded terms
    properties {
        any {
            type: object
            description: All the terms in 'any' condition are combined with 'or' operation
            properties {
                "include" {
                    type: array
                    items {type: string}
                }
                exclude {
                    type: array
                    items {type: string}
                }
            }
        }
        all {
            type: object
            description: All the terms in 'all' condition are combined with 'and' operation
            properties {
                "include" {
                    type: array
                    items {type: string}
                }
                exclude {
                    type: array
                    items {type: string}
                }
            }
        }
        op {
            type: string
            description: The operation between 'any' and 'all' parts of the filter if both are provided
            default: and
            enum: [and, or]
        }
    }
}
metadata_item {
    type: object
    properties {
        key {
            type: string
            description: The key uniquely identifying the metadata item inside the given entity
        }
        type {
            type: string
            description: The type of the metadata item
        }
        value {
            type: string
            description: The value stored in the metadata item
        }
    }
}
task_status_enum {
    type: string
    enum: [
        created
        queued
        in_progress
        stopped
        published
        publishing
        closed
        failed
        completed
        unknown
    ]
}
multi_field_pattern_data {
    type: object
    properties {
        pattern {
            description: "Pattern string (regex)"
            type: string
        }
        fields {
            description: "List of field names"
            type: array
            items { type: string }
        }
    }
}
credentials {
    type: object
    properties {
        access_key {
            type: string
            description: Credentials access key
        }
        secret_key {
            type: string
            description: Credentials secret key
        }
        label {
            type: string
            description: Optional credentials label
        }
    }
}
batch_operation {
    request {
        type: object
        required: [ids]
        properties {
            ids {
                type: array
                items {type: string}
            }
        }
    }
    response {
        type: object
        properties {
            succeeded {
                type: array
                items {
                    type: object
                    properties {
                        id: {
                            description: ID of the succeeded entity
                            type: string
                        }
                    }
                }
            }
            failed {
                type: array
                items {
                    type: object
                    properties {
                        id: {
                            description: ID of the failed entity
                            type: string
                        }
                        error: {
                            description: Error info
                            type: object
                            properties {
                                codes {
                                    type: array
                                    items {type: integer}
                                }
                                msg {
                                    type: string
                                }
                                data {
                                    type: object
                                    additionalProperties: True
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}