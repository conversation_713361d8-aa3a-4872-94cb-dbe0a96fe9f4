_description: "Provides a management API for datasets in the system."
_definitions:{
    algorithm {
        type: object
        properties {
            id {
                description: "algorithm ID"
                type: string
            }
            name {
                description: "algorithm Name"
                type: string
            }
            description {
                description: "algorithm description"
                type: string
            }
            type {
                description: "algorithm type"
                type: string
            }
            size {
                description: "algorithm szie"
                type: string
            }
            is_public {
                description: "This algorithm is public or not"
                type: boolean
            }
            versions_num {
                description: "The count number of algorithm versions"
                type: string
            }
            last_update {
                description: "Last dataset update time"
                type: string
                format: "date-time"
            }
            create_time {
                description: "Dataset create time"
                type: string
                format: "date-time"
            }
        }
    }
}
get_images{
    "1.0" {
        description: "get image lsit for ide"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                users {
                    description: User list
                    type: array
                    items: string
                }
            }
        }
    }
}
create {
    "1.0" {
        description: "Create a new algorithm"
        request {
            type: object
            required :[name,is_public,dataset_type]
            properties {
                name {
                    description: "algorithm name"
                    type: string
                }
                description {
                    description: "algorithm description."
                    type: string
                }
                is_public {
                    description: "algorithm is public or not"
                    type: string
                    enum: [private, presets, public]
                }
                is_inference {
                    description: "algorithm is public or not"
                    type: string
                    enum: [private, presets, public]
                }
                algorithm_type {
                    description: "The type of algorithm"
                    type: string
                    enum: [ img, txt, char, radio, video, self-defined ]
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "algorithm id"
                    type: string
                }
            }
        }
    }
}
get_all {
    "1.0" {
        description: "Get all algorithm"
        request {
            type: object
            properties {
                name {
                    description: "algorithm name"
                    type: string
                }
                algorithm_type{
                    description: "algorithm type"
                    type: string
                }
                is_public{
                    description:"is publish"
                    type: string
                }
                only_fields {
                    description: "List of algorithm field names"
                    type: array
                    items { type: string }
                }
                page {
                    description: "Page number, returns a specific page out of the resulting list of users"
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                order_by {
                    description: "List of field names to order by. Use '-' prefix to specify descending order. Optional, recommended when using page"
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties {
                datasets {
                    description: "datasets list"
                    type: array
                    items { "$ref": "#/definitions/dataset" }
                }
            }
        }
    }
}
update {
    internal: false
    "1.0" {
        description: "Update algorithm"
        request {
            type: object
            required: [ id ]
            properties {
                algorithm_id {
                    description: "id of algorithm for update"
                    type: string
                }
                description {
                    description: "algorithm desc"
                    type: string
                }
                is_public {
                    description: "algorithm is public or not"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of updated user objects (0 or 1)"
                    type: integer
                }
                fields {
                    description: "Updated fields names and values"
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
}
delete{
        "1.0" {
        description: "delete algorithm from mongo"
        request {
            type: object
            required :[id]
            properties {
                id {
                    description: "id of algorithm for delete"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "algorithm id"
                    type: string
                }
            }
        }
    }
}
statistics{
    "1.0" {
        description: "statistics"
        request {
            type: object
            required: [
            ]
            properties {
            }
        }
        response {
            type: object
            properties {}
            additionalProperties: false
        }
    }
}
upload_file {
    "1.0" {
        description: "upload file to algorithm"
        request {
            type: object
            properties {
                dir_name {
                    description: "The name of directory to upload"
                    type: string
                }
                algorithm_name {
                    description: "The algorithm name of file to upload"
                    type: string
                }
                version {
                    description: "The file version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "algorithm names"
                    type: string
                }
                size {
                    description: "algorithm szie"
                    type: string
                }
                last_update {
                    description: "Last algorithm update time"
                    type: string
                    format: "date-time"
                }      
            }
        }
    }
}
get_algos{
    "1.0" {
        description: "get_algos"
        request {
            type: object
            required: [
            ]
            properties {
            }
        }
        response {
            type: object
            properties {}
            additionalProperties: false
        }
    }
}
upload_git{
        "1.0" {
        description: "upload git for juicefs"
        request {
            type: object
            required :[id]
            properties {
                algorithm_id {
                    description: "id of algorithm "
                    type: string
                }
                git_url {
                    description: "git url "
                    type: string
                }
            }
        }
         response {
            type: object
            properties {}
            additionalProperties: false
        }
    }
}
delete_file {
    "1.0" {
        description: "delete file to algo"
        request {
            type: object
            properties {
                file_name {
                    description: "The name of file to delete"
                    type: string
                }
                algorithm_id {
                    description: "The algo name of file to delete"
                    type: string
                }
                version {
                    description: "The file version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "algo names"
                    type: string
                }
                size {
                    description: "algo szie"
                    type: string
                }
                last_update {
                    description: "Last algo update time"
                    type: string
                    format: "date-time"
                }      
            }
        }
    }
}
download_file {
    "1.0" {
        description: "Download file to algorithm"
        request {
            type: object
            properties {
                file_name {
                    description: "The name of file to download"
                    type: string
                }
                algorithm_id {
                    description: "The algorithm name of file to download"
                    type: string
                }
                version {
                    description: "The file version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                 url {
                    description: "Download algorithm  file url"
                    type: string
                }       
            }
        }
    }
}
batch_delete_file {
    "1.0" {
        description: "delete file from dataset"
        request {
            type: object
            properties {
                file_name {
                    description: "The names of files to delete"
                    type: array
                }
                algorithm_id {
                    description: "The algo name of file to delete"
                    type: string
                }
                version {
                    description: "The algo version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                result {
                    description: "The files name which were deleted"
                    type: string
                }      
            }
        }
    }
}
batch_download_file {
    "1.0" {
        description: "download file from algo"
        request {
            type: object
            properties {
                file_name {
                    description: "The names of files to download"
                    type: array
                }
                algorithm_id {
                    description: "The algo name of file to download"
                    type: string
                }
                version {
                    description: "The algo version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                url {
                    description: "Supply url to download the zip file which contains all files you want to download"
                    type: string
                }      
            }
        }
    }
}
create_version {
    "1.0" {
        description: "Create a new algo version"
        request {
            type: object
            required :[algorithm_id, version]
            properties {
                algorithm_id {
                    description: "algo id"
                    type: string
                }
                version {
                    description: "algo version name"
                    type: string
                }
                description {
                    description: "algo version description."
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "algo version name "
                    type: string
                }
            }
        }
    }
}
get_all_version {
    "1.0" {
        description: "Get all algo version"
        required :[algorithm_id]
        request {
            type: object
            properties {
                algorithm_id {
                    description: "algo id"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                algo {
                    description: "algo version list"
                    type: array
                }
            }
        }
    }
}
download_version{
    "1.0" {
        description: "download version file to algo"
        request {
            type: object
            properties {
                algorithm_id {
                    description: "algo id"
                    type: string
                }
                version {
                    description: "algo version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                url {
                    description: "Download algo version file url"
                    type: string
                }  
            }
        }
    }
}
file_list{
    "1.0" {
        description: "The algo list"
        request{
            type: object
            properties{
                algorithm_id {
                    description: "The algo name of file to upload"
                    type: string
                }
                version {
                    description: "The file version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "algo names"
                    type: string
                }
                size {
                    description: "algo szie"
                    type: string
                }
                Etag {
                    description: "algo etag"
                    type: string
                }
                last_update {
                    description: "Last algo update time"
                    type: string
                    format: "date-time"
                }      
            }
        }
    }
}
read_file{
    "1.0" {
        description: "read readme file"
        request{
            type: object
            properties{
                algorithm_id {
                    description: "algo id"
                    type: string
                }
                version {
                    description: "algo version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                readmeFile {
                    description: "file"
                    type: string
                }   
            }
        }
    }
}