dict_list {
    "1.0" {
        description: "Algorithm release restart"
        request {
            type: object
            properties {
                algo_id {
                    description: "algo id"
                    type: string
                }
                version {
                    description: "algo version"
                    type: string
                }
            }
        }
        response {
            type: string
        }
    }
}

dict_type_satellite_type {
    "1.0" {
        description: "Algorithm release restart"
        request {
            type: object
            properties {
                algo_id {
                    description: "algo id"
                    type: string
                }
                version {
                    description: "algo version"
                    type: string
                }
            }
        }
        response {
            type: string
        }
    }
}

dict_type_station_type {
    "1.0" {
        description: "Algorithm release restart"
        request {
            type: object
            properties {
                algo_id {
                    description: "algo id"
                    type: string
                }
                version {
                    description: "algo version"
                    type: string
                }
            }
        }
        response {
            type: string
        }
    }
}
dict_type_target_classify {
    "1.0" {
        description: "Algorithm release restart"
        request {
            type: object
            properties {
                algo_id {
                    description: "algo id"
                    type: string
                }
                version {
                    description: "algo version"
                    type: string
                }
            }
        }
        response {
            type: string
        }
    }
}
dict_type_target_type {
    "1.0" {
        description: "Algorithm release restart"
        request {
            type: object
            properties {
                algo_id {
                    description: "algo id"
                    type: string
                }
                version {
                    description: "algo version"
                    type: string
                }
            }
        }
        response {
            type: string
        }
    }
}
dict_type_work_ability {
    "1.0" {
        description: "Algorithm release restart"
        request {
            type: object
            properties {
                algo_id {
                    description: "algo id"
                    type: string
                }
                version {
                    description: "algo version"
                    type: string
                }
            }
        }
        response {
            type: string
        }
    }
}

dict_type_task_type {
    "1.0" {
        description: "Algorithm release restart"
        request {
            type: object
            properties {
                algo_id {
                    description: "algo id"
                    type: string
                }
                version {
                    description: "algo version"
                    type: string
                }
            }
        }
        response {
            type: string
        }
    }
}
