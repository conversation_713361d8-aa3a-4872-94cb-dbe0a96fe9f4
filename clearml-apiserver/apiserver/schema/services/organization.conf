_description: "This service provides organization level operations"
_definitions {
    value_mapping {
        type: object
        required: [key, value]
        properties {
            key {
                description: Original value
                type: object
            }
            value {
                description: Translated value
                type: object
            }
        }
    }
    field_mapping {
        type: object
        required: [field]
        properties {
            field {
                description: The source field name as specified in the only_fields
                type: string
            }
            name {
                description: The column name in the exported csv file
                type: string
            }
            values {
                type: array
                items { "$ref": "#/definitions/value_mapping"}
            }
        }
    }
}
get_tags {
    "2.8" {
        description: "Get all the user and system tags used for the company tasks and models"
        request {
            type: object
            properties {
                include_system {
                    description: "If set to 'true' then the list of the system tags is also returned. The default value is 'false'"
                    type: boolean
                    default: false
                }
                filter {
                    description: "Filter on entities to collect tags from"
                    type: object
                    properties {
                        tags {
                            description: "The list of tag values to filter by. Use 'null' value to specify empty tags. Use '__Snot' value to specify that the following value should be excluded"
                            type: array
                            items {type: string}
                        }
                        system_tags {
                            description: "The list of system tag values to filter by. Use 'null' value to specify empty system tags. Use '__Snot' value to specify that the following value should be excluded"
                            type: array
                            items {type: string}
                        }
                    }
                }
            }
        }
        response {
            type: object
            properties {
                tags {
                    description: "The list of unique tag values"
                    type: array
                    items {type: string}
                }
                system_tags {
                    description: "The list of unique system tag values. Returned only if 'include_system' is set to 'true' in the request"
                    type: array
                    items {type: string}
                }
            }
        }
    }
}
get_user_companies {
    "2.12" {
        description: "Get details for all companies associated with the current user"
        request {
            type: object
            properties {}
            additionalProperties: false
        }
        response {
            type: object
            properties {
                companies {
                    description: "List of company information entries. First company is the user's own company"
                    type: array
                    items {
                        type: object
                        properties {
                            id {
                                description: "Company ID"
                                type: string
                            }
                            name {
                                description: "Company name"
                                type: string
                            }
                            allocated {
                                description: "Number of users allocated for company"
                                type: integer
                            }
                            owners {
                                description: "Company owners"
                                type: array
                                items {
                                    type: object
                                    properties {
                                        id {
                                            description: "User ID"
                                            type: string
                                        }
                                        name {
                                            description: "User Name"
                                            type: string
                                        }
                                        avatar {
                                            description: "User avatar (URL or base64-encoded data)"
                                            type: string
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
get_entities_count {
    "2.20": {
        description: "Get counts for the company entities according to the passed search criteria"
        request {
            type: object
            properties {
                projects {
                    type: object
                    additionalProperties: true
                    description: Search criteria for projects
                }
                tasks {
                    type: object
                    additionalProperties: true
                    description: Search criteria for experiments
                }
                models {
                    type: object
                    additionalProperties: true
                    description: Search criteria for models
                }
                pipelines {
                    type: object
                    additionalProperties: true
                    description: Search criteria for pipelines
                }
                datasets {
                    type: object
                    additionalProperties: true
                    description: Search criteria for datasets
                }
            }
        }
        response {
            type: object
            properties {
                projects {
                    type: integer
                    description: The number of projects matching the criteria
                }
                tasks {
                    type: integer
                    description: The number of experiments matching the criteria
                }
                models {
                    type: integer
                    description: The number of models matching the criteria
                }
                pipelines {
                    type: integer
                    description: The number of pipelines matching the criteria
                }
                datasets {
                    type: integer
                    description: The number of datasets matching the criteria
                }
            }
        }
    }
    "2.22": ${get_entities_count."2.20"} {
        request.properties {
            search_hidden {
                description: "If set to 'true' then hidden projects and tasks are included in the search results"
                type: boolean
                default: false
            }
            active_users {
                description: "The list of users that were active in the project. If passes then the resulting projects are filtered to the ones that have tasks created by these users"
                type: array
                items: {type: string}
            }
        }
    }
    "2.23": ${get_entities_count."2.22"} {
        request.properties {
            reports {
                type: object
                additionalProperties: true
                description: Search criteria for reports
            }
            allow_public {
                description: "Allow public entities to be counted in the results"
                type: boolean
                default: true
            }
        }
        response.properties {
            reports {
                type: integer
                description: The number of reports matching the criteria
            }
        }
    }
}
prepare_download_for_get_all {
    "2.26": {
        description: Prepares download from get_all_ex parameters
        request {
            type: object
            required: [ entity_type,  only_fields, field_mappings]
            properties {
                only_fields {
                    description: "List of task field names (nesting is supported using '.', e.g. execution.model_labels). If provided, this list defines the query's projection (only these fields will be returned for each result entry)"
                    type: array
                    items {type: string}
                }
                allow_public {
                    description: "Allow public entities to be returned in the results"
                    type: boolean
                    default: true
                }
                search_hidden {
                    description: "If set to 'true' then hidden entities are included in the search results"
                    type: boolean
                    default: false
                }
                entity_type {
                    description: "The type of the entity to retrieve"
                    type: string
                    enum: [
                        task
                        model
                    ]
                }
                field_mappings {
                    description: The name and value mappings for the exported fields. The fields that are not in the mappings will not be exported
                    type: array
                    items { "$ref": "#/definitions/field_mapping"}
                }
            }
        }
        response {
            type: object
            properties {
                prepare_id {
                    description: "Prepare ID (use when calling 'download_for_get_all')"
                    type: string
                }
            }
        }
    }
}
download_for_get_all {
    "2.26": {
        description: Generates a file for the download
        request {
            type: object
            required: [ prepare_id ]
            properties {
                prepare_id {
                    description: "Call ID returned by a call to prepare_download_for_get_all"
                    type: string
                }
            }
        }
        response {
            type: string
        }
    }
}