
_description: """This service provides authentication management and authorization
validation for the entire system."""
_default {
    internal: true
    allow_roles: ["system", "root"]
}

_definitions {
    include "_common.conf"
    credential_key {
        type: object
        properties {
            access_key {
                type: string
                description: ""
            }
            label {
                type: string
                description: Optional credentials label
            }
            last_used {
                type: string
                description: ""
                format: "date-time"
            }
            last_used_from {
                type: string
                description: ""
            }
        }
    }
    role {
        type: string
        enum: [ admin, superuser, user, annotator ]
    }
}

login {
    internal: false
    allow_roles = [ "*" ]
    "2.1" {
        description: """Get a token based on supplied credentials (key/secret).
        Intended for use by users with key/secret credentials that wish to obtain a token
        for use with other services."""
        request {
            type: object
            properties {
                expiration_sec {
                    type: integer
                        description: """Requested token expiration time in seconds. 
                        Not guaranteed,  might be overridden by the service"""
                }
            }
        }
        response {
            type: object
            properties {
                token {
                    type: string
                    description: Token string
                }
            }
        }
    }
}

logout {
    internal: false
    allow_roles = [ "*" ]
    "2.2" {
        description: """Removes the authentication cookie from the current session"""
        request {
            type: object
            additionalProperties: false
        }
        response {
            type: object
            additionalProperties: false
        }
    }
}

get_token_for_user {
    "2.1" {
        description: """Get a token for the specified user. Intended for internal use."""
        request {
            type: object
            required: [
                user
            ]
            properties {
                user {
                    type: string
                    description: User ID
                }
                company {
                    type: string
                    description: Company ID
                }
                expiration_sec {
                    type: integer
                    description: """Requested token expiration time in seconds.
                    Not guaranteed,  might be overridden by the service"""
                }
            }
        }
        response {
            type: object
            properties {
                token {
                    type: string
                    description: ""
                }
            }
        }
    }
}

validate_token {
    "2.1" {
        description: """Validate a token and return user identity if valid.
        Intended for internal use. """
        request {
            type: object
            required: [ token ]
            properties {
                token {
                    type: string
                    description: Token string
                }
            }
        }
        response {
            type: object
            properties {
                valid {
                    type: boolean
                    description: Boolean indicating if the token is valid
                }
                user {
                    type: string
                    description: Associated user ID
                }
                company {
                    type: string
                    description: Associated company ID
                }
            }
        }
    }
}

create_user {
    "2.1" {
        allow_roles: ["system", "root", "admin"]
        description: """Creates a new user auth entry. Intended for internal use. """
        request {
            type: object
            required: [
                name
                company
                email
            ]
            properties {
                name {
                    type: string
                    description: User name (makes the auth entry more readable)
                }
                company {
                    type: string
                    description: Associated company ID
                }
                email {
                    type: string
                    description: Email address uniquely identifying the user
                }
                role {
                    description: User role
                    default: user
                    "$ref": "#/definitions/role"
                }
                given_name {
                    type: string
                    description: Given name
                }
                family_name {
                    type: string
                    description: Family name
                }
                avatar {
                    type: string
                    description: Avatar URL
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    type: string
                    description: New user ID
                }
            }
        }
    }
}

create_credentials {
    allow_roles = [ "*" ]
    internal: false
    "2.1" {
        description: """Creates a new set of credentials for the authenticated user.
                        New key/secret is returned.
                        Note: Secret will never be returned in any other API call.
                        If a secret is lost or compromised, the key should be revoked
                        and a new set of credentials can be created."""
        request {
            type: object
            properties {}
            additionalProperties: false
        }
        response {
            type: object
            properties {
                credentials {
                    "$ref": "#/definitions/credentials"
                    description: Created credentials
                }
            }
        }
    }
    "2.17": ${create_credentials."2.1"} {
        request.properties.label {
            type: string
            description: Optional credentials label
        }
    }
}

get_credentials {
    allow_roles = [ "*" ]
    internal: false
    "2.1" {
        description: """Returns all existing credential keys for the authenticated user.
        Note: Only credential keys are returned."""
        request {
            type: object
            properties {}
            additionalProperties: false
        }
        response {
            type: object
            properties {
                credentials {
                    description: "List of credentials, each with an empty secret field."
                    type: array
                    items { "$ref": "#/definitions/credential_key" }
                }
            }
        }
    }
}

edit_credentials {
    allow_roles = [ "*" ]
    internal: false
    "2.19" {
        description: """Updates the label of the existing credentials for the authenticated user."""
        request {
            type: object
            required: [ access_key ]
            properties {
                access_key {
                    type: string
                    description: Existing credentials key
                }
                label {
                    type: string
                    description: New credentials label
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of credentials updated"
                    type: integer
                    enum: [0, 1]
                }
            }
        }
    }
}

revoke_credentials {
    allow_roles = [ "*" ]
    internal: false
    "2.1" {
        description: """Revokes (and deletes) a set (key, secret) of credentials for
        the authenticated user."""
        request {
            type: object
            required: [ key_id ]
            properties {
                access_key {
                    type: string
                    description: Credentials key
                }
            }
        }
        response {
            type: object
            properties {
                revoked {
                    description: "Number of credentials revoked"
                    type: integer
                    enum: [0, 1]
                }
            }
        }
    }
}

edit_user {
    internal: false
    allow_roles: ["system", "root", "admin"]
    "2.1" {
        description:  """ Edit a users' auth data properties"""
        request {
            type: object
            properties {
                user {
                    description: "User ID"
                    type: string
                }
                role {
                    description: "The new user's role within the company"
                    type: string
                    enum: [admin, superuser, user, annotator]
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of users updated (0 or 1)"
                    type: number
                    enum: [ 0, 1 ]
                }
                fields {
                    description: "Updated fields names and values"
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
}

fixed_users_mode {
    authorize: false
    "2.1" {
        description:  """ Return fixed users mode status"""
        request {
            type: object
            additionalProperties: false
        }
        response {
            type: object
            properties {
                enabled {
                    description: "Fixed users mode enabled"
                    type: boolean
                }
                server_errors {
                    description: "Server initialization errors"
                    type: object
                    additionalProperties: True
                }
            }
        }
    }
}