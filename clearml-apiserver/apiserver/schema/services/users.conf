_description: """This service provides a management interface to users information
and new users login restrictions."""
_default {
    internal: true
}
_definitions {
    user {
        type: object
        properties {
            id {
                description: User ID
                type: string
            }
            name {
                description: Full name
                type: string
            }
            given_name {
                description: Given name
                type: string
            }
            family_name {
                description: Family name
                type: string
            }
            avatar {
                description: Avatar URL
                type: string
            }
            company {
                description: Company ID
                type: string
            }
            # Admin only fields
            role {
                description: """User's role (admin only)"""
                type: string
            }
            providers {
                description: """Providers uses has logged-in with"""
                type: object
                additionalProperties: true
            }
            created {
                description: User creation date
                type: string
                format: date-time
            }
            email {
                description: User email
                type: string
                format: email
            }
        }
    }
    get_current_user_response_user_object {
        type: object
        description: "like user, but returns company object instead of ID"
        properties {
            id {
                type: string
            }
            name {
                type: string
            }
            given_name {
                type: string
            }
            family_name {
                type: string
            }
            role {
                type: string
            }
            avatar {
                type: string
            }
            company {
                type: object
                properties {
                    id {
                        type: string
                    }
                    name {
                        type: string
                    }
                }
            }
            preferences {
                description: User preferences
                type: object
                additionalProperties: true
            }
        }
    }
}

get_by_id {
    internal: false
    "2.1" {
        description: Gets user information
        request {
            type: object
            required: [ user ]
            properties {
                user {
                    description: User ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                user {
                    description: User info
                    "$ref": "#/definitions/user"
                }
            }
        }
    }
}

get_current_user {
    internal: false
    "2.1" {
        description: """Gets current user information, based on the authenticated user making the call."""
        request {
            type: object
            additionalProperties: false
        }
        response {
            type: object
            properties {
                user {
                    description: "User info"
                    "$ref": "#/definitions/get_current_user_response_user_object"
                }
            }
        }
    }
    "2.20": ${get_current_user."2.1"} {
        response {
            properties {
                getting_started {
                    type: object
                    description: Getting stated info
                    additionalProperties: true
                }
                created {
                    type: string
                    description: User creation time
                    format: date-time
                }
            }
        }
    }
    "2.26": ${get_current_user."2.20"} {
        response.properties.settings {
            type: object
            properties {
                max_download_items {
                    type: string
                    description: The maximum items downloaded for this user in csv file downloads
                }
            }
        }
    }
}

get_all_ex {
    internal: true
    "2.1": ${get_all."2.1"} {
    }
    "2.8": ${get_all."2.1"} {
        request {
            type: object
            properties {
                active_in_projects {
                    description: "List of project IDs. If provided, return only users that were active in these projects. If empty list is provided, return users that were active in all projects"
                    type: array
                    items { type: string }
                }
            }
        }
    }

}

get_all {
    "2.1" {
        description: Get all user objects
        request {
            type: object
            properties {
                name {
                    description: "Get only users whose name matches this pattern (python regular expression syntax)"
                    type: string
                }
                id {
                    description: "List of user IDs used to filter results"
                    type: array
                    items { type: string }
                }
                only_fields {
                    description: "List of user field names (if applicable, nesting is supported using '.'). If provided, this list defines the query's projection (only these fields will be returned for each result entry)"
                    type: array
                    items { type: string }
                }
                page {
                    description: "Page number, returns a specific page out of the resulting list of users"
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                order_by {
                    description: "List of field names to order by. Use '-' prefix to specify descending order. Optional, recommended when using page"
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties {
                users {
                    description: User list
                    type: array
                    items { "$ref": "#/definitions/user" }
                }
            }
        }
    }
}
delete {
    internal: true
    allow_roles: [ "system", "root" ]
    "2.1" {
        description: Delete user
        description: Delete a user
        request {
            type: object
            required: [ user ]
            properties {
                user {
                    description: ID of user to delete
                    type: string
                }
            }
        }
        response {
            type: object
            additionalProperties: false
        }
    }
}

create {
    allow_roles: [ "system", "root" ]
    "2.1" {
        description: Create a new user object. Reserved for internal use.
        request {
            type: object
            required: [
                company
                id
                name
            ]
            properties {
                id {
                    description: User ID
                    type: string
                }
                company {
                    description: Company ID
                    type: string
                }
                name {
                    description: Full name
                    type: string
                }
                given_name {
                    description: Given name
                    type: string
                }
                family_name {
                    description: Family name
                    type: string
                }
                avatar {
                    description: Avatar URL
                    type: string
                }
            }
        }
        response {
            type: object
            properties {}
            additionalProperties: false
        }
    }
}

update {
    internal: false
    "2.1" {
        description: Update a user object
        request {
            type: object
            required: [ user ]
            properties {
                user {
                    description: User ID
                    type: string
                }
                name {
                    description: Full name
                    type: string
                }
                given_name {
                    description: Given name
                    type: string
                }
                family_name {
                    description: Family name
                    type: string
                }
                avatar {
                    description: Avatar URL
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: Number of updated user objects (0 or 1)
                    type: integer
                }
                fields {
                    description: Updated fields names and values
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
}

get_preferences {
    internal: false
    "2.1" {
        description: Get user preferences
        request {
            type: object
            properties {}
        }
        response {
            type: object
            properties {
                preferences {
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
}

set_preferences {
    internal: false
    "2.1" {
        description: Set user preferences
        request {
            type: object
            required: [ preferences ]
            properties {
                preferences {
                    description: """Updates to user preferences. A mapping from keys in dot notation to values.
                    For example, `{"a.b": 0}` will set the key "b" in object "a" to 0."""
                    type: object
                    additionalProperties: true
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: Number of updated user objects (0 or 1)
                    type: integer
                }
                fields {
                    description: Updated fields names and values
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
}
