_description : "Provides an API for running tasks to report events collected by the system."
_definitions {
    include "_events_common.conf"
    metrics_scalar_event {
        description: "Used for reporting scalar metrics during training task"
        type: object
        required: [ task, type ]
        properties {
            timestamp {
                description: "Epoch milliseconds UTC, will be set by the server if not set."
                type: number
            }
            type {
                description: "'training_stats_scalar'"
                type: string
            }
            task {
                description: "Task ID (required)"
                type: string
            }
            iter {
                description: "Iteration"
                type: integer
            }
            metric  {
                description: "Metric name, e.g. 'count', 'loss', 'accuracy'"
                type: string
            }
            variant {
                description: "E.g. 'class_1', 'total', 'average"
                type: string
            }
            value {
                description: ""
                type: number
            }
        }
    }
    metrics_vector_event {
        description: "Used for reporting vector metrics during training task"
        type: object
        required: [ task ]
        properties {
            timestamp {
                description: "Epoch milliseconds UTC, will be set by the server if not set."
                type: number
            }
            type {
                description: "'training_stats_vector'"
                type: string
            }
            task {
                description: "Task ID (required)"
                type: string
            }
            iter {
                description: "Iteration"
                type: integer
            }
            metric {
                description: "Metric name, e.g. 'count', 'loss', 'accuracy'"
                type: string
            }
            variant {
                description: "E.g. 'class_1', 'total', 'average"
                type: string
            }
            values {
                description: "vector of float values"
                type: array
                items { type: number }
            }
        }
    }
    metrics_image_event {
        description: "An image or video was dumped to storage for debugging"
        type: object
        required: [ task, type ]
        properties {
            timestamp {
                description: "Epoch milliseconds UTC, will be set by the server if not set."
                type: number
            }
            type {
                description: "'training_debug_image'"
                type: string
            }
            task {
                description: "Task ID (required)"
                type: string
            }
            iter {
                description: "Iteration"
                type: integer
            }
            metric {
                description: "Metric name, e.g. 'count', 'loss', 'accuracy'"
                type: string
            }
            variant {
                description: "E.g. 'class_1', 'total', 'average"
                type: string
            }
            key {
                description: "File key"
                type: string
            }
            url {
                description: "File URL"
                type: string
            }
        }
    }
    metrics_plot_event {
        description: """ An entire plot (not single datapoint) and it's layout.
        Used for plotting ROC curves, confidence matrices, etc. when evaluating the net."""
        type: object
        required: [ task, type ]
        properties {
            timestamp {
                description: "Epoch milliseconds UTC, will be set by the server if not set."
                type: number
            }
            type {
                description: "'plot'"
                type: string
            }
            task {
                description: "Task ID (required)"
                type: string
            }
            iter {
                description: "Iteration"
                type: integer
            }
            metric {
                description: "Metric name, e.g. 'count', 'loss', 'accuracy'"
                type: string
            }
            variant {
                description: "E.g. 'class_1', 'total', 'average"
                type: string
            }
            plot_str {
                description: """An entire plot (not single datapoint) and it's layout.
                Used for plotting ROC curves, confidence matrices, etc. when evaluating the net.
                """
                type: string
            }
            skip_validation {
                description: "If set then plot_str is not checked for a valid json. The default is False"
                type: boolean
            }
        }
    }
    log_level_enum {
        type: string
        enum: [
            notset
            debug
            verbose
            info
            warn
            warning
            error
            fatal
            critical
        ]
    }
    event_type_enum {
        type: string
        enum: [
            training_stats_scalar
            training_stats_vector
            training_debug_image
            plot
            log
        ]
    }
    task_metric {
        type: object
        required: [task]
        properties {
            task {
                description: "Task ID"
                type: string
            }
            metric {
                description: "Metric name"
                type: string
            }
        }
    }
    task_metric_variants {
        type: object
        required: [task]
        properties {
            task {
                description: "Task ID"
                type: string
            }
            metric {
                description: "Metric name"
                type: string
            }
            variants {
                description: Metric variant names
                type: array
                items {type: string}
            }
        }
    }
    task_log_event {
        description: """A log event associated with a task."""
        type: object
        required: [ task, type ]
        properties {
            timestamp {
                description: "Epoch milliseconds UTC, will be set by the server if not set."
                type: number
            }
            type {
                description: "'log'"
                type: string
            }
            task {
                description: "Task ID (required)"
                type: string
            }
            level {
                description: "Log level."
                "$ref": "#/definitions/log_level_enum"
            }
            worker {
                description: "Name of machine running the task."
                type: string
            }
            msg {
                description: "Log message."
                type: string
            }
        }
    }
    debug_image_sample_response {
        type: object
        properties {
            scroll_id {
                type: string
                description: "Scroll ID to pass to the next calls to get_debug_image_sample or next_debug_image_sample"
            }
            event {
                type: object
                description: "Debug image event"
            }
            min_iteration {
                type: integer
                description: "minimal valid iteration for the variant"
            }
            max_iteration {
                type: integer
                description: "maximal valid iteration for the variant"
            }
        }
    }
    plot_sample_response {
        type: object
        properties {
            scroll_id {
                type: string
                description: "Scroll ID to pass to the next calls to get_plot_sample or next_plot_sample"
            }
            events {
                description: "Plot events"
                type: array
                items { type: object}
            }
            min_iteration {
                type: integer
                description: "minimal valid iteration for the metric"
            }
            max_iteration {
                type: integer
                description: "maximal valid iteration for the metric"
            }
        }
    }
}
add {
    "2.1" {
        description: "Adds a single event"
        request {
            type: object
            anyOf: [
                { "$ref": "#/definitions/metrics_scalar_event" }
                { "$ref": "#/definitions/metrics_vector_event" }
                { "$ref": "#/definitions/metrics_image_event" }
                { "$ref": "#/definitions/metrics_plot_event" }
                { "$ref": "#/definitions/task_log_event" }
            ]
        }
        response {
            type: object
            additionalProperties: true
        }
    }
    "2.22": ${add."2.1"} {
        request.properties {
            model_event {
                type: boolean
                description: If set then the event is for a model. Otherwise for a task. Cannot be used with task log events. If used in batch then all the events should be marked the same
                default: false
            }
            allow_locked {
                type: boolean
                description: Allow adding events to published tasks or models
                default: false
            }
        }
    }
}
add_batch {
    "2.1" {
        description: "Adds a batch of events in a single call (json-lines format, stream-friendly)"
        batch_request: {
            action: add
            version: 2.1
        }
        response {
            type: object
            properties {
                added { type: integer }
                errors { type: integer }
                errors_info { type: object }
            }
        }
    }
    "2.22": ${add_batch."2.1"} {
        batch_request: {
            action: add
            version: 2.22
        }
    }
}
delete_for_task {
    "2.1" {
        description: "Delete all task events. *This cannot be undone!*"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
                allow_locked {
                    type: boolean
                    description: "Allow deleting events even if the task is locked"
                    default: false
                }
            }
        }
        response {
            type: object
            properties {
                deleted {
                    type: boolean
                    description: "Number of deleted events"
                }
            }
        }
    }
}
delete_for_model {
    "2.22" {
        description: "Delete all model events. *This cannot be undone!*"
        request {
            type: object
            required: [
                model
            ]
            properties {
                model {
                    type: string
                    description: "Model ID"
                }
                allow_locked {
                    type: boolean
                    description: "Allow deleting events even if the model is locked"
                    default: false
                }
            }
        }
        response {
            type: object
            properties {
                deleted {
                    type: boolean
                    description: "Number of deleted events"
                }
            }
        }
    }
}
debug_images {
    "2.1" {
        description: "Get all debug images of a task"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
                iters {
                    type: integer
                    description: "Max number of latest iterations for which to return debug images"
                }
                scroll_id {
                    type: string
                    description: "Scroll ID of previous call (used for getting more results)"
                }
            }
        }
        response {
            type: object
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
                images {
                    type: array
                    items { type: object }
                    description: "Images list"
                }
                returned {
                    type: integer
                    description: "Number of results returned"
                }
                total {
                    type: number
                    description: "Total number of results available for this query. In case there are more than 10000 results it is set to 10000"
                }
                scroll_id {
                    type: string
                    description: "Scroll ID for getting more results"
                }
            }
        }
    }
    "2.7" {
        description: "Get the debug image events for the requested amount of iterations per each task"
        request {
            type: object
            required: [
                metrics
            ]
            properties {
                metrics {
                    type: array
                    items { "$ref": "#/definitions/task_metric" }
                    description: "List of task metrics for which the envents will be retreived"
                }
                iters {
                    type: integer
                    description: "Max number of latest iterations for which to return debug images"
                }
                navigate_earlier {
                    type: boolean
                    description: "If set then events are retreived from latest iterations to earliest ones. Otherwise from earliest iterations to the latest. The default is True"
                }
                refresh {
                    type: boolean
                    description: "If set then scroll will be moved to the latest iterations. The default is False"
                }
                scroll_id {
                    type: string
                    description: "Scroll ID of previous call (used for getting more results)"
                }
            }
        }
        response {"$ref": "#/definitions/debug_images_response"}
    }
    "2.14": ${debug_images."2.7"} {
        request {
            properties {
                metrics {
                    type: array
                    description: List of metrics and variants
                    items { "$ref": "#/definitions/task_metric_variants" }
                }
            }
        }
    }
    "2.22": ${debug_images."2.14"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model events. Otherwise task events
            default: false
        }
    }
}
plots {
    "2.20" {
        description: "Get plot events for the requested amount of iterations per each task"
        request {
            type: object
            required: [
                metrics
            ]
            properties {
                metrics {
                    type: array
                    description: List of metrics and variants
                    items { "$ref": "#/definitions/task_metric_variants" }
                }
                iters {
                    type: integer
                    description: "Max number of latest iterations for which to return plots"
                }
                navigate_earlier {
                    type: boolean
                    description: "If set then events are retreived from latest iterations to earliest ones. Otherwise from earliest iterations to the latest. The default is True"
                }
                refresh {
                    type: boolean
                    description: "If set then scroll will be moved to the latest iterations. The default is False"
                }
                scroll_id {
                    type: string
                    description: "Scroll ID of previous call (used for getting more results)"
                }
            }
        }
        response {"$ref": "#/definitions/plots_response"}
    }
    "2.22": ${plots."2.20"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model plots. Otherwise task plots
            default: false
        }
    }
}
get_debug_image_sample {
    "2.12": {
        description: "Return the debug image per metric and variant for the provided iteration"
        request {
            type: object
            required: [task, metric, variant]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
                metric {
                    description: "Metric name"
                    type: string
                }
                variant {
                    description: "Metric variant"
                    type: string
                }
                iteration {
                    description: "The iteration to bring debug image from. If not specified then the latest reported image is retrieved"
                    type: integer
                }
                refresh {
                    description: "If set then scroll state will be refreshed to reflect the latest changes in the debug images"
                    type: boolean
                }
                scroll_id {
                    type: string
                    description: "Scroll ID from the previous call to get_debug_image_sample or empty"
                }
            }
        }
        response {"$ref": "#/definitions/debug_image_sample_response"}
    }
    "2.20": ${get_debug_image_sample."2.12"} {
        request.properties.navigate_current_metric {
            description: If set then subsequent navigation with next_debug_image_sample is done on the debug images for the passed metric only. Otherwise for all the metrics
            type: boolean
            default: true
        }
    }
    "2.22": ${get_debug_image_sample."2.20"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model debug images. Otherwise task debug images
            default: false
        }
    }
}
next_debug_image_sample {
    "2.12": {
        description: "Get the image for the next variant for the same iteration or for the next iteration"
        request {
            type: object
            required: [task, scroll_id]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
                scroll_id {
                    type: string
                    description: "Scroll ID from the previous call to get_debug_image_sample"
                }
                navigate_earlier {
                    type: boolean
                    description: """If set then get the either previous variant event from the current iteration or (if does not exist) the last variant event from the previous iteration.
                    Otherwise next variant event from the current iteration or first variant event from the next iteration"""
                }
            }
        }
        response {"$ref": "#/definitions/debug_image_sample_response"}
    }
    "2.22": ${next_debug_image_sample."2.12"} {
        request.properties.next_iteration {
            type: boolean
            default: false
            description: If set then navigate to the next/previous iteration
        }
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model debug images. Otherwise task debug images
            default: false
        }
    }
}
get_plot_sample {
    "2.20": {
        description: "Return plots for the provided iteration"
        request {
            type: object
            required: [task, metric]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
                metric {
                    description: "Metric name"
                    type: string
                }
                iteration {
                    description: "The iteration to bring plot from. If not specified then the latest reported plot is retrieved"
                    type: integer
                }
                refresh {
                    description: "If set then scroll state will be refreshed to reflect the latest changes in the plots"
                    type: boolean
                }
                scroll_id {
                    type: string
                    description: "Scroll ID from the previous call to get_plot_sample or empty"
                }
                navigate_current_metric {
                    description: If set then subsequent navigation with next_plot_sample is done on the plots for the passed metric only. Otherwise for all the metrics
                    type: boolean
                    default: true
                }
            }
        }
        response {"$ref": "#/definitions/plot_sample_response"}
    }
    "2.22": ${get_plot_sample."2.20"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model plots. Otherwise task plots
            default: false
        }
    }
}
next_plot_sample {
    "2.20": {
        description: "Get the plot for the next metric for the same iteration or for the next iteration"
        request {
            type: object
            required: [task, scroll_id]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
                scroll_id {
                    type: string
                    description: "Scroll ID from the previous call to get_plot_sample"
                }
                navigate_earlier {
                    type: boolean
                    description: """If set then get the either previous metric events from the current iteration or (if does not exist) the last metric events from the previous iteration.
                    Otherwise next metric events from the current iteration or first metric events from the next iteration"""
                }
            }
        }
        response {"$ref": "#/definitions/plot_sample_response"}
    }
    "2.22": ${next_plot_sample."2.20"} {
        request.properties.next_iteration {
            type: boolean
            default: false
            description: If set then navigate to the next/previous iteration
        }
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model plots. Otherwise task plots
            default: false
        }
    }
}
get_task_metrics{
    "2.7": {
        description: "For each task, get a list of metrics for which the requested event type was reported"
        request {
            type: object
            required: [
                tasks
            ]
            properties {
                tasks {
                    type: array
                    items { type: string }
                    description: "Task IDs"
                }
                event_type {
                    "description": "Event type"
                    "$ref": "#/definitions/event_type_enum"
                }
            }
        }
        response {
            type: object
            properties {
                metrics {
                    type: array
                    items { type: object }
                    description: "List of task with their metrics"
                }
            }
        }
    }
    "2.22": ${get_task_metrics."2.7"} {
        request.properties.model_events {
            type: boolean
            description: If set then get metrics from model events. Otherwise from task events
            default: false
        }
    }
}
get_multi_task_metrics {
    "2.28" {
        description: """Get unique metrics and variants from the events of the specified type.
        Only events reported for the passed task or model ids are analyzed."""
        request {
            type: object
            required: [ tasks ]
            properties {
                tasks {
                    description: task ids to get metrics from
                    type: array
                    items {type: string}
                }
                model_events {
                    description: If not set or set to false then passed ids are task ids otherwise model ids
                    type: boolean
                    default: false
                }
                event_type {
                    "description": Event type. If not specified then metrics are collected from the reported events of all types
                    "$ref": "#/definitions/event_type_enum"
                }
            }
        }
        response {
            type: object
            properties {
                metrics {
                    type: array
                    description: List of metrics and variants
                    items { "$ref": "#/definitions/metric_variants" }
                }
            }
        }
    }
}
get_task_log {
    "1.5" {
        description: "Get all 'log' events for this task"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
                order {
                    type: string
                    description: "Timestamp order in which log events will be returned (defaults to ascending)"
                    enum: [
                        asc
                        desc
                    ]
                }
                scroll_id {
                    type: string
                    description: ""
                }
                batch_size {
                    type: integer
                    description: ""
                }
            }
        }
        response {
            type: object
            properties {
                events {
                    type: array
                    # TODO: items: log event
                    items { type: object }
                }
                returned { type: integer }
                total { type: integer }
                scroll_id { type: string }
            }
        }
    }
    "1.7" {
        description: "Get all 'log' events for this task"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
                order {
                    type: string
                    description: "Timestamp order in which log events will be returned (defaults to ascending)"
                    enum: [
                        asc
                        desc
                    ]
                }
                from {
                    type: string
                    description: "Where will the log entries be taken from (default to the head of the log)"
                    enum: [
                        head
                        tail
                    ]
                }
                scroll_id {
                    type: string
                    description: ""
                }
                batch_size {
                    type: integer
                    description: ""
                }
            }
        }
        response {
            type: object
            properties {
                events {
                    type: array
                    # TODO: items: log event
                    items { type: object }
                    description: "Log items list"
                }
                returned {
                    type: integer
                    description: "Number of results returned"
                }
                total {
                    type: number
                    description: "Total number of results available for this query. In case there are more than 10000 results it is set to 10000"
                }
                scroll_id {
                    type: string
                    description: "Scroll ID for getting more results"
                }
            }
        }
    }
    "2.9" {
        description: "Get 'log' events for this task"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
                batch_size {
                    type: integer
                    description: "The amount of log events to return"
                }
                navigate_earlier {
                    type: boolean
                    description: "If set then log events are retreived from the latest to the earliest ones (in timestamp descending order, unless order='asc'). Otherwise from the earliest to the latest ones (in timestamp ascending order, unless order='desc'). The default is True"
                }
                from_timestamp {
                    type: number
                    description: "Epoch time in UTC ms to use as the navigation start. Optional. If not provided, reference timestamp is determined by the 'navigate_earlier' parameter (if true, reference timestamp is the last timestamp and if false, reference timestamp is the first timestamp)"
                }
                order {
                    type: string
                    description: "If set, changes the order in which log events are returned based on the value of 'navigate_earlier'"
                    enum: [asc, desc]
                }
            }
        }
        response {
            type: object
            properties {
                events {
                    type: array
                    items { type: object }
                    description: "Log items list"
                }
                returned {
                    type: integer
                    description: "Number of log events returned"
                }
                total {
                    type: number
                    description: "Total number of log events available for this query. In case there are more than 10000 events it is set to 10000"
                }
            }
        }
    }
}
get_task_events {
    "2.1" {
        description: "Scroll through task events, sorted by timestamp"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
                order {
                    type:string
                    description: "'asc' (default) or 'desc'."
                    enum: [
                        asc
                        desc
                    ]
                }
                scroll_id {
                    type: string
                    description: "Pass this value on next call to get next page"
                }
                batch_size {
                    type: integer
                    description: "Number of events to return each time (default 500)"
                }
                event_type {
                    type: string
                    description: "Return only events of this type"
                }
            }
        }
        response {
            type: object
            properties {
                events {
                    type: array
                    items { type: object }
                    description: "Events list"
                }
                returned {
                    type: integer
                    description: "Number of results returned"
                }
                total {
                    type: number
                    description: "Total number of results available for this query. In case there are more than 10000 results it is set to 10000"
                }
                scroll_id {
                    type: string
                    description: "Scroll ID for getting more results"
                }
            }
        }
    }
    "2.22": ${get_task_events."2.1"} {
        request.properties {
            model_events {
                type: boolean
                description: If set then get retrieving model events. Otherwise task events
                default: false
            }
            metrics {
                type: array
                description: List of metrics and variants
                items { "$ref": "#/definitions/metric_variants" }
            }
        }
    }
}

download_task_log {
    "2.1" {
        description: "Get an attachment containing the task's log"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
                line_type {
                    description: "Line format type"
                    type: string
                    enum: [
                        json
                        text
                    ]
                }
                line_format {
                    type: string
                    description: "Line string format. Used if the line type is 'text'"
                    default: "{asctime} {worker} {level} {msg}"
                }
            }
        }
        response {
            type: string
        }
    }
}
get_task_plots {
    "2.1" {
        description: "Get all 'plot' events for this task"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
                iters {
                    type: integer
                    description: "Max number of latest iterations for which to return plots"
                }
                scroll_id {
                    type: string
                    description: "Scroll ID of previous call (used for getting more results)"
                }
            }
        }
        response {
            type: object
            properties {
                plots {
                    type: array
                    items {
                        type: object
                    }
                    description: "Plots list"
                }
                returned {
                    type: integer
                    description: "Number of results returned"
                }
                total {
                    type: number
                    description: "Total number of results available for this query. In case there are more than 10000 results it is set to 10000"
                }
                scroll_id {
                    type: string
                    description: "Scroll ID for getting more results"
                }
            }
        }
    }
    "2.14": ${get_task_plots."2.1"} {
        request {
            properties {
                metrics {
                    type: array
                    description: List of metrics and variants
                    items { "$ref": "#/definitions/metric_variants" }
                }
            }
        }
    }
    "2.16": ${get_task_plots."2.14"} {
        request.properties.no_scroll {
            description: If true then no scroll is created. Suitable for one time calls
            type: boolean
            default: false
        }
    }
    "2.22": ${get_task_plots."2.16"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model events. Otherwise task events
            default: false
        }
    }
}
get_multi_task_plots {
    "2.1" {
        description: "Get 'plot' events for the given tasks"
        request {
            type: object
            required: [
                tasks
            ]
            properties {
                tasks {
                    description: "List of task IDs"
                    type: array
                    items {
                        type: string
                        description: "Task ID"
                    }
                }
                iters {
                    type: integer
                    description: "Max number of latest iterations for which to return plots"
                }
                scroll_id {
                    type: string
                    description: "Scroll ID of previous call (used for getting more results)"
                }
            }
        }
        response {
            type: object
            properties {
                plots {
                    type: object
                    description: "Plots mapping (keyed by task name)"
                }
                returned {
                    type: integer
                    description: "Number of results returned"
                }
                total {
                    type: number
                    description: "Total number of results available for this query. In case there are more than 10000 results it is set to 10000"
                }
                scroll_id {
                    type: string
                    description: "Scroll ID for getting more results"
                }
            }
        }
    }
    "2.16": ${get_multi_task_plots."2.1"} {
        request.properties.no_scroll {
            description: If true then no scroll is created. Suitable for one time calls
            type: boolean
            default: false
        }
    }
    "2.22": ${get_multi_task_plots."2.16"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model events. Otherwise task events
            default: false
        }
    }
    "2.26": ${get_multi_task_plots."2.22"} {
        request.properties.last_iters_per_task_metric {
            type: boolean
            description: If set to 'true' and iters passed then last iterations for each task metrics are retrieved. Otherwise last iterations for the whole task are retrieved
            default: true
        }
    }
    "2.28": ${get_multi_task_plots."2.26"} {
        request.properties.metrics {
            type: array
            description: List of metrics and variants
            items { "$ref": "#/definitions/metric_variants" }
        }
    }
}
get_vector_metrics_and_variants {
    "2.1" {
        description: ""
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
            }
        }
        response {
            type: object
            properties {
                metrics {
                    description: ""
                    type: array
                    items: { type: object }
                    # TODO: items: ???
                }
            }
        }
    }
    "2.22": ${get_vector_metrics_and_variants."2.1"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model events. Otherwise task events
            default: false
        }
    }
}
vector_metrics_iter_histogram {
    "2.1" {
        description: "Get histogram data of all the scalar metrics and variants in the task"
        request {
            type: object
            required: [
                task
                metric
                variant
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
                metric {
                    type: string
                    description: ""
                }
                variant {
                    type: string
                    description: ""
                }
            }
        }
        response {
            type: object
            properties {
                images {
                    type: array
                    items {
                        type: object
                    }
                }
            }
        }
    }
    "2.22": ${vector_metrics_iter_histogram."2.1"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model events. Otherwise task events
            default: false
        }
    }
}
scalar_metrics_iter_histogram {
    "2.1" {
        description: "Get histogram data of all the vector metrics and variants in the task"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
                samples {
                    description: "The amount of histogram points to return (0 to return all the points). Optional, the default value is 6000."
                    type: integer
                }
                key {
                    description: """
                    Histogram x axis to use:
                    iter - iteration number
                    iso_time - event time as ISO formatted string
                    timestamp - event timestamp as milliseconds since epoch
                    """
                    "$ref": "#/definitions/scalar_key_enum"
                }
            }
        }
        response {
            type: object
            properties {
                images {
                    type: array
                    items {
                        type: object
                    }
                }

            }
        }
    }
    "2.14": ${scalar_metrics_iter_histogram."2.1"} {
        request {
            properties {
                metrics {
                    type: array
                    description: List of metrics and variants
                    items { "$ref": "#/definitions/metric_variants" }
                }
            }
        }
    }
    "2.22": ${scalar_metrics_iter_histogram."2.14"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model events. Otherwise task events
            default: false
        }
    }
}
multi_task_scalar_metrics_iter_histogram {
    "2.1" {
        description: "Used to compare scalar stats histogram of multiple tasks"
        request {
            type: object
            required: [
                tasks
            ]
            properties {
                tasks {
                    description: "List of task Task IDs. Maximum amount of tasks is 100"
                    type: array
                    items {
                        type: string
                        description: "Task ID"
                    }
                }
                samples {
                    description: "The amount of histogram points to return. Optional, the default value is 6000"
                    type: integer
                }
                key {
                    description: """
                    Histogram x axis to use:
                    iter - iteration number
                    iso_time - event time as ISO formatted string
                    timestamp - event timestamp as milliseconds since epoch
                    """
                    "$ref": "#/definitions/scalar_key_enum"
                }
            }
        }
        response {
            type: object
            // properties {}
            additionalProperties: true
        }
    }
    "2.22": ${multi_task_scalar_metrics_iter_histogram."2.1"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model events. Otherwise task events
            default: false
        }
    }
    "2.28": ${multi_task_scalar_metrics_iter_histogram."2.22"} {
        request.properties.metrics {
            type: array
            description: List of metrics and variants
            items { "$ref": "#/definitions/metric_variants" }
        }
    }
}
get_task_single_value_metrics {
    "2.20" {
        description: Get single value metrics for the passed tasks
        request {
            type: object
            required: [tasks]
            properties {
                tasks {
                    description: "List of task Task IDs"
                    type: array
                    items {
                        type: string
                        description: "Task ID"
                    }
                }
            }
        }
        response {"$ref": "#/definitions/single_value_metrics_response"}
    }
    "2.22": ${get_task_single_value_metrics."2.20"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model events. Otherwise task events
            default: false
        }
    }
    "2.28": ${get_task_single_value_metrics."2.22"} {
        request.properties.metrics {
            type: array
            description: List of metrics and variants
            items { "$ref": "#/definitions/metric_variants" }
        }
    }
}
get_task_latest_scalar_values {
    "2.1" {
        description: "Get the tasks's latest scalar values"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
            }
        }
        response {
            type: object
            properties {
                metrics {
                    type: array
                    items {
                        type: object
                        properties {
                            name {
                                type: string
                                description: "Metric name"
                            }
                            variants {
                                type: array
                                items {
                                    type: object
                                    properties {
                                        name {
                                            type: string
                                            description: "Variant name"
                                        }
                                        last_value {
                                            type: number
                                            description: "Last reported value"
                                        }
                                        last_100_value {
                                            type: number
                                            description: "Average of 100 last reported values"
                                        }

                                    }
                                }
                            }
                        }
                    }
                 }
             }
        }
    }
}
get_scalar_metrics_and_variants {
    "2.1" {
        description: get task scalar metrics and variants
        request {
            type: object
            required: [ task ]
            properties {
                task {
                    description: task ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                metrics {
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
    "2.22": ${get_scalar_metrics_and_variants."2.1"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model events. Otherwise task events
            default: false
        }
    }
}
get_scalar_metric_data {
    "2.1" {
        description: "get scalar metric data for task"
        request {
            type: object
            properties {
                task {
                    type: string
                    description: task ID
                }
                metric {
                    type: string
                    description: type of metric
                }
                scroll_id {
                    type: string
                    description: "Scroll ID of previous call (used for getting more results)"
                }
            }
        }
        response {
            type: object
            properties {
                events {
                    description: "task scalar metric events"
                    type: array
                    items {
                        type: object
                    }
                }
                returned {
                    type: integer
                    description: "amount of events returned"
                }
                total {
                    type: integer
                    description: "amount of events in task"
                }
                scroll_id {
                    type: string
                    description: "Scroll ID for getting more results"
                }
            }
        }
    }
    "2.16": ${get_scalar_metric_data."2.1"} {
        request.properties.no_scroll {
            description: If true then no scroll is created. Suitable for one time calls
            type: boolean
            default: false
        }
    }
    "2.22": ${get_scalar_metric_data."2.16"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model events. Otherwise task events
            default: false
        }
    }
}
scalar_metrics_iter_raw {
    "2.16" {
        description: "Get raw data for a specific metric variants in the task"
        request {
            type: object
            required: [
                task, metric
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
                metric {
                    description: "Metric and variants for which to return data points"
                    "$ref": "#/definitions/metric_variants"
                }
                key {
                    description: """Array of x axis to return. Supported values:
                    iter - iteration number
                    timestamp - event timestamp as milliseconds since epoch
                    """
                    "$ref": "#/definitions/scalar_key_enum"
                }
                batch_size {
                    description: "The number of data points to return for this call. Optional, the default value is 10000. Maximum batch size is 200000"
                    type: integer
                    default: 10000
                }
                count_total {
                    description: "Count the total number of data points. If false, total number of data points is not counted and null is returned"
                    type: boolean
                    default: false
                }
                scroll_id {
                    description: "Optional Scroll ID. Use to get more data points following a previous call"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                variants {
                    description: "Raw data points for each variant"
                    type: object
                    additionalProperties: true
                }
                total {
                    description: "Total data points count. If count_total is false, null is returned"
                    type: integer
                }
                returned {
                    description: "Number of data points returned in this call. If 0 results were returned, no more results are avilable"
                    type: integer
                }
                scroll_id {
                    description: "Scroll ID. Use to get more data points when calling this endpoint again"
                    type: string
                }
            }
        }
    }
    "2.22": ${scalar_metrics_iter_raw."2.16"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model events. Otherwise task events
            default: false
        }
    }
}
clear_scroll {
    "2.18" {
        description: "Clear an open Scroll ID"
        request {
            type: object
            required: [
                scroll_id
            ]
            properties {
                scroll_id {
                    description: "Scroll ID as returned by previous events service calls"
                    type: string
                }
            }
        }
        response {
            type: object
            additionalProperties: false
        }
    }
}

clear_task_log {
    "2.19" {
        description: Remove old logs from task
        request {
            type: object
            required: [task]
            properties {
                task {
                    description: Task ID
                    type: string
                }
                allow_locked {
                    type: boolean
                    description: Allow deleting events even if the task is locked
                    default: false
                }
                threshold_sec {
                    description: The amount of seconds ago to retain the log records. The older log records will be deleted. If not passed or 0 then all the log records for the task will be deleted
                    type: integer
                }
            }
        }
        response {
            type: object
            properties {
                deleted {
                    description: The number of deleted log records
                    type: integer
                }
            }
        }
    }
}

get_project_histogram {
    "2.1" {
        description: "Get histogram data of project"
        request {
            type: object
            required: [
                project
            ]
            properties {
                project {
                    type: string
                    description: "project ID"
                }
                samples {
                    description: "The amount of histogram points to return (0 to return all the points). Optional, the default value is 6000."
                    type: integer
                }
                key {
                    description: """
                    Histogram x axis to use:
                    iter - iteration number
                    iso_time - event time as ISO formatted string
                    timestamp - event timestamp as milliseconds since epoch
                    """
                    "$ref": "#/definitions/scalar_key_enum"
                }
            }
        }
        response {
            type: object
            properties {
                images {
                    type: array
                    items {
                        type: object
                    }
                }

            }
        }
    }
    "2.14": ${get_project_histogram."2.1"} {
        request {
            properties {
                metrics {
                    type: array
                    description: List of metrics and variants
                    items { "$ref": "#/definitions/metric_variants" }
                }
            }
        }
    }
    "2.22": ${get_project_histogram."2.14"} {
        request.properties.model_events {
            type: boolean
            description: If set then the retrieving model events. Otherwise task events
            default: false
        }
    }
}
