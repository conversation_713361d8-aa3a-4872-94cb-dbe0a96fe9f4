_description: "Provides a management API for tasks in the system."
_references {
    status_change_request {
        type: object
        properties {
            task {
                description: Task ID
                type: string
            }
            status_reason {
                description: Reason for status change
                type: string
            }
            status_message {
                description: Extra information regarding status change
                type: string
            }
        }
    }
    // "force" field with default description
    force_arg {
        type: boolean
        default: false
        description: "Allows forcing state change even if transition is not supported"
    }
}
_definitions {
    include "_tasks_common.conf"
    change_many_request: ${_definitions.batch_operation} {
        request {
            properties {
                status_reason {
                    description: Reason for status change
                    type: string
                }
                status_message {
                    description: Extra information regarding status change
                    type: string
                }
            }
        }
        response {
            properties {
                succeeded.items.properties.updated {
                    description: "Number of tasks updated (0 or 1)"
                    type: integer
                    enum: [ 0, 1 ]
                }
                succeeded.items.properties.fields {
                    description: "Updated fields names and values"
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
    update_response {
        type: object
        properties {
            updated {
                description: "Number of tasks updated (0 or 1)"
                type: integer
                enum: [ 0, 1 ]
            }
            fields {
                description: "Updated fields names and values"
                type: object
                additionalProperties: true
            }
        }
    }
    param_key {
        type: object
        properties {
            section {
                description: "Section that the parameter belongs to"
                type: string
            }
            name {
                description: "Name of the parameter. If the name is ommitted then the corresponding operation is performed on the whole section"
                type: string
            }
        }
    }
    replace_hyperparams_enum {
        type: string
        enum: [
            none,
            section,
            all
        ]
    }
    task_urls {
        type: object
        properties {
            model_urls {
                type: array
                items {type: string}
            }
            event_urls {
                type: array
                items {type: string}
            }
            artifact_urls {
                type: array
                items {type: string}
            }
        }
    }
}

get_by_id {
    "2.1" {
        description: "Gets task information"
        request {
            type: object
            required: [ task ]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                task {
                    description: "Task info"
                    "$ref": "#/definitions/task"
                }
            }
        }
    }
}
get_by_id_ex {
    internal: true
    "2.11": ${get_all_ex."2.1"}
}
get_all_ex {
    internal: true
    "2.1": ${get_all."2.1"}
    "2.13": ${get_all_ex."2.1"} {
        request {
            properties {
                include_subprojects {
                    description: "If set to 'true' and project field is set then tasks from the subprojects are searched too"
                    type: boolean
                    default: false
                }
            }
        }
    }
    "2.14": ${get_all_ex."2.13"} {
        request.properties.search_hidden {
            description: "If set to 'true' then hidden tasks are included in the search results"
            type: boolean
            default: false
        }
    }
    "2.15": ${get_all_ex."2.14"} {
        request {
            properties {
                scroll_id {
                    type: string
                    description: "Scroll ID returned from the previos calls to get_all_ex"
                }
                refresh_scroll {
                    type: boolean
                    description: "If set then all the data received with this scroll will be requeried"
                }
                size {
                    type: integer
                    minimum: 1
                    description: "The number of tasks to retrieve"
                }
            }
        }
        response.properties.scroll_id {
            type: string
            description: "Scroll ID that can be used with the next calls to get_all_ex to retrieve more data"
        }
    }
    "2.23": ${get_all_ex."2.15"} {
        request.properties {
            allow_public {
                description: "Allow public tasks to be returned in the results"
                type: boolean
                default: true
            }
        }
    }
    "2.27": ${get_all_ex."2.23"} {
        request.properties {
            filters {
                type: object
                additionalProperties: ${_definitions.field_filter}
            }
        }
    }
}
get_all {
    "2.1" {
        description: "Get all the company's tasks and all public tasks"
        request {
            type: object
            properties {
                id {
                    description: "List of IDs to filter by"
                    type: array
                    items { type: string }
                }
                name {
                    description: "Get only tasks whose name matches this pattern (python regular expression syntax)"
                    type: string
                }
                user {
                    description: "List of user IDs used to filter results by the task's creating user"
                    type: array
                    items { type: string }
                }
                project {
                    type: array
                    items { type: string }
                }
                page {
                    description: "Page number, returns a specific page out of the resulting list of tasks"
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                order_by {
                    description: """List of field names to order by. When search_text is used,
                    '@text_score' can be used as a field representing the text score of returned documents.
                    Use '-' prefix to specify descending order. Optional, recommended when using page.
                    If the first order field is a hyper parameter or metric then string values are ordered
                    according to numeric ordering rules where applicable"""
                    type: array
                    items { type: string }
                }
                type {
                    description: "List of task types. One or more of: 'import', 'annotation', 'training' or 'testing' (case insensitive)"
                    type: array
                    items { type: string }
                }
                tags {
                    description: "List of task user-defined tags. Use '-' prefix to exclude tags"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "List of task system tags. Use '-' prefix to exclude system tags"
                    type: array
                    items { type: string }
                }
                status {
                    description: "List of task status."
                    type: array
                    items { "$ref": "#/definitions/task_status_enum" }
                }
                project {
                    description: "List of project IDs"
                    type: array
                    items { type: string }
                }
                only_fields {
                    description: "List of task field names (nesting is supported using '.', e.g. execution.model_labels). If provided, this list defines the query's projection (only these fields will be returned for each result entry)"
                    type: array
                    items { type: string }
                }
                parent {
                    description: "Parent ID"
                    type: string
                }
                status_changed {
                    description: "List of status changed constraint strings (utcformat, epoch) with an optional prefix modifier (>, >=, <, <=)"
                    type: array
                    items {
                        type: string
                        pattern: "^(>=|>|<=|<)?.*$"
                    }
                }
                search_text {
                    description: "Free text search query"
                    type: string
                }
                _all_ {
                    description: "Multi-field pattern condition (all fields match pattern)"
                    "$ref": "#/definitions/multi_field_pattern_data"
                }
                _any_ {
                    description: "Multi-field pattern condition (any field matches pattern)"
                    "$ref": "#/definitions/multi_field_pattern_data"
                }
            }
        }
        response {
            type: object
            properties {
                tasks {
                    description: "List of tasks"
                    type: array
                    items { "$ref": "#/definitions/task" }
                }
            }
        }
    }
    "2.14": ${get_all."2.1"} {
        request.properties.search_hidden {
            description: "If set to 'true' then hidden tasks are included in the search results"
            type: boolean
            default: false
        }
    }
    "2.15": ${get_all."2.14"} {
        request {
            properties {
                scroll_id {
                    type: string
                    description: "Scroll ID returned from the previos calls to get_all"
                }
                refresh_scroll {
                    type: boolean
                    description: "If set then all the data received with this scroll will be requeried"
                }
                size {
                    type: integer
                    minimum: 1
                    description: "The number of tasks to retrieve"
                }
            }
        }
        response.properties.scroll_id {
            type: string
            description: "Scroll ID that can be used with the next calls to get_all to retrieve more data"
        }
    }
    "2.26": ${get_all."2.15"} {
        request {
            properties {
                include_subprojects {
                    description: "If set to 'true' and project field is set then tasks from the subprojects are searched too"
                    type: boolean
                    default: false
                }
            }
        }
    }
}
get_types {
    "2.8" {
        description: "Get the list of task types used in the specified projects"
        request {
            type: object
            properties {
                projects {
                    description: "The list of projects which tasks will be analyzed. If not passed or empty then all the company and public tasks will be analyzed"
                    type: array
                    items: {type: string}
                }
            }
        }
        response {
            type: object
            properties {
                types {
                    description: "Unique list of the task types used in the requested projects"
                    type: array
                    items: {type: string}
                }
            }
        }
    }
}
clone {
    "2.5" {
        description: "Clone an existing task"
        request {
            type: object
            required: [ task ]
            properties {
                task {
                    description: "ID of the task"
                    type: string
                }
                new_task_name {
                    description: "The name of the cloned task. If not provided then taken from the original task"
                    type: string
                }
                new_task_comment {
                    description: "The comment of the cloned task. If not provided then taken from the original task"
                    type: string
                }
                new_task_tags {
                    description: "The user-defined tags of the cloned task. If not provided then taken from the original task"
                    type: array
                    items { type: string }
                }
                new_task_system_tags {
                    description: "The system tags of the cloned task. If not provided then empty"
                    type: array
                    items { type: string }
                }
                new_task_parent {
                    description: "The parent of the cloned task. If not provided then taken from the original task"
                    type: string
                }
                new_task_project {
                    description: "The project of the cloned task. If not provided then taken from the original task"
                    type: string
                }
                new_task_hyperparams {
                    description: "The hyper params for the new task. If not provided then taken from the original task"
                    type: object
                    additionalProperties {
                        "$ref": "#/definitions/section_params"
                    }
                }
                new_task_configuration {
                    description: "The configuration for the new task. If not provided then taken from the original task"
                    type: object
                    additionalProperties {
                        "$ref": "#/definitions/configuration_item"
                    }
                }
                execution_overrides {
                    description: "The execution params for the cloned task. The params not specified are taken from the original task"
                    "$ref": "#/definitions/execution"
                }
                validate_references {
                    description: "If set to 'false' then the task fields that are copied from the original task are not validated. The default is false."
                    type: boolean
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "ID of the new task"
                    type: string
                }
            }
        }
    }
    "2.12": ${clone."2.5"} {
        request {
            properties {
                new_project_name {
                    description: "Clone task to a new project by this name (only if `new_task_project` is not provided). If a project by this name already exists, task will be cloned to existing project."
                    type: string
                }
            }
        }
        response {
            properties {
                new_project {
                    description: "In case the new_project_name was specified returns the target project details"
                    type: object
                    properties {
                        id {
                            description: "The ID of the target project"
                            type: string
                        }
                        name {
                            description: "The name of the target project"
                            type: string
                        }
                    }
                }
            }
        }
    }
    "2.13": ${clone."2.12"}{
        request {
            properties {
                new_task_input_models {
                    description: "The list of input models for the cloned task. If not specifed then copied from the original task"
                    type: array
                    items {"$ref": "#/definitions/task_model_item"}
                }
                new_task_container {
                    description: "The docker container properties for the new task. If not provided then taken from the original task"
                    type: object
                    additionalProperties { type: string }
                }
            }
        }
    }
}
add_or_update_model {
    "2.13" {
        description: "Add or update task model"
        request {
            type: object
            required: [task, name, model, type]
            properties {
                task {
                    description: "ID of the task"
                    type: string
                }
                name {
                    description: "The task model name"
                    type: string
                }
                model {
                    description: "The model ID"
                    type: string
                }
                type {
                    description: "The task model type"
                    "$ref": "#/definitions/model_type_enum"
                }
                iteration {
                    description: "Iteration (used to update task statistics)"
                    type: integer
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of tasks updated (0 or 1)"
                    type: integer
                    enum: [0, 1]
                }
            }
        }
    }
}
delete_models {
    "2.13" {
        description: "Delete models from task"
        request {
            type: object
            required: [ task, models ]
            properties {
                task {
                    description: "ID of the task"
                    type: string
                }
                models {
                    description: "The list of models to delete"
                    type: array
                    items {
                        type: object
                        required: [name, type]
                        properties {
                            name {
                                description: "The task model name"
                                type: string
                            }
                            type {
                                description: "The task model type"
                                "$ref": "#/definitions/model_type_enum"
                            }
                        }
                    }
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of tasks updated (0 or 1)"
                    type: integer
                    enum: [0, 1]
                }
            }
        }
    }
}
create {
    "2.1" {
        description: "Create a new task"
        request {
            type: object
            required: [
                name
                type
            ]
            properties {
                name {
                    description: "Task name. Unique within the company."
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
                type {
                    description: "Type of task"
                    "$ref": "#/definitions/task_type_enum"
                }
                comment {
                    description: "Free text comment "
                    type: string
                }
                parent {
                    description: "Parent task id Must be a completed task."
                    type: string
                }
                project {
                    description: "Project ID of the project to which this task is assigned Must exist[ab]"
                    type: string
                }
                output_dest {
                    description: "Output storage id Must be a reference to an existing storage."
                    type: string
                }
                execution {
                    description: "Task execution params"
                    "$ref": "#/definitions/execution"
                }
                script {
                    description: "Script info"
                    "$ref": "#/definitions/script"
                }
                hyperparams {
                    description: "Task hyper params per section"
                    type: object
                    additionalProperties {
                        "$ref": "#/definitions/section_params"
                    }
                }
                configuration {
                    description: "Task configuration params"
                    type: object
                    additionalProperties {
                        "$ref": "#/definitions/configuration_item"
                    }
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "ID of the task"
                    type: string
                }
            }
        }
    }
    "2.13": ${create."2.1"} {
        request {
            properties {
                models {
                    description: "Task models"
                    "$ref": "#/definitions/task_models"
                }
                container {
                    description: "Docker container parameters"
                    type: object
                    additionalProperties { type: string }
                }
            }
        }
    }
}
validate {
    "2.1" {
        description: "Validate task properties (before create)"
        request {
            type: object
            required: [
                name
                type
            ]
            properties {
                name {
                    description: "Task name. Unique within the company."
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
                type {
                    description: "Type of task"
                    "$ref": "#/definitions/task_type_enum"
                }
                comment {
                    description: "Free text comment "
                    type: string
                }
                parent {
                    description: "Parent task id Must be a completed task."
                    type: string
                }
                project {
                    description: "Project ID of the project to which this task is assigned Must exist[ab]"
                    type: string
                }
                output_dest {
                    description: "Output storage id Must be a reference to an existing storage."
                    type: string
                }
                execution {
                    description: "Task execution params"
                    "$ref": "#/definitions/execution"
                }
                script {
                    description: "Script info"
                    "$ref": "#/definitions/script"
                }
                hyperparams {
                    description: "Task hyper params per section"
                    type: object
                    additionalProperties {
                        "$ref": "#/definitions/section_params"
                    }
                }
                configuration {
                    description: "Task configuration params"
                    type: object
                    additionalProperties {
                        "$ref": "#/definitions/configuration_item"
                    }
                }
            }
        }
        response {
            type: object
            additionalProperties: false
        }
    }
    "2.13": ${validate."2.1"} {
        request {
            properties {
                models {
                    description: "Task models"
                    "$ref": "#/definitions/task_models"
                }
                container {
                    description: "Docker container parameters"
                    type: object
                    additionalProperties { type: string }
                }
            }
        }
    }
}
update {
    "2.1" {
        description: "Update task's runtime parameters"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    description: "ID of the task"
                    type: string
                }
                name {
                    description: "Task name Unique within the company."
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
                comment {
                    description: "Free text comment "
                    type: string
                }
                project {
                    description: "Project ID of the project to which this task is assigned"
                    type: string
                }
                output__error {
                    description: "Free text error"
                    type: string
                }
                created {
                    description: "Task creation time (UTC) "
                    type: string
                    format: "date-time"
                }
            }
        }
        response: ${_definitions.update_response}
    }
}
update_batch {
    "2.1" {
        description: """Updates a batch of tasks.
        Headers
        Content type should be 'application/json-lines'."""
        batch_request: {
            action: update
            version: 1.5
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of tasks updated (0 or 1)"
                    type: integer
                    enum: [ 0, 1 ]
                }
            }
        }
    }
}
edit {
    "2.1" {
        description: "Edit task's details."
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    description: "ID of the task"
                    type: string
                }
                force = ${_references.force_arg} {
                    description: "If not true, call fails if the task status is not 'created'"
                }
                name {
                    description: "Task name Unique within the company."
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
                type {
                    description: "Type of task"
                    "$ref": "#/definitions/task_type_enum"
                }
                comment {
                    description: "Free text comment "
                    type: string
                }
                parent {
                    description: "Parent task id Must be a completed task."
                    type: string
                }
                project {
                    description: "Project ID of the project to which this task is assigned Must exist[ab]"
                    type: string
                }
                output_dest {
                    description: "Output storage id Must be a reference to an existing storage."
                    type: string
                }
                execution {
                    description: "Task execution params"
                    "$ref": "#/definitions/execution"
                }
                script {
                    description: "Script info"
                    "$ref": "#/definitions/script"
                }
                hyperparams {
                    description: "Task hyper params per section"
                    type: object
                    additionalProperties {
                        "$ref": "#/definitions/section_params"
                    }
                }
                configuration {
                    description: "Task configuration params"
                    type: object
                    additionalProperties {
                        "$ref": "#/definitions/configuration_item"
                    }
                }
            }
        }
        response: ${_definitions.update_response}
    }
    "2.13": ${edit."2.1"} {
        request {
            properties {
                models {
                    description: "Task models"
                    "$ref": "#/definitions/task_models"
                }
                container {
                    description: "Docker container parameters"
                    type: object
                    additionalProperties { type: string }
                }
                runtime {
                    description: "Task runtime mapping"
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
}
reset {
    "2.1" {
        description: "Reset a task to its initial state, along with any information stored for it (statistics, frame updates etc.)."
        request = {
            type: object
            required: [
                task
            ]
            properties.force = ${_references.force_arg} {
                description: "If not true, call fails if the task status is 'completed'"
            }
            properties.clear_all {
                description: "Clear script and execution sections completely"
                type: boolean
                default: false
            }
        } ${_references.status_change_request}
        response: ${_definitions.update_response} {
            properties {
                dequeued {
                    description: "Response from queues.remove_task"
                    type: object
                    additionalProperties: true
                }
                events {
                    description: "Response from events.delete_for_task"
                    type: object
                    additionalProperties: true
                }
                deleted_models {
                    description: "Number of output models deleted by the reset"
                    type: integer
                }
            }
        }
    }
    "2.13": ${reset."2.1"} {
        request {
            properties {
                return_file_urls {
                    description: "If set to 'true' then return the urls of the files that were uploaded by this task. Default value is 'false'"
                    type: boolean
                }
                delete_output_models {
                    description: "If set to 'true' then delete output models of this task that are not referenced by other tasks. Default value is 'true'"
                    type: boolean
                }
            }
        }
        response {
            properties {
                urls {
                    description: "The urls of the files that were uploaded by this task. Returned if the 'return_file_urls' was set to 'true'"
                    "$ref": "#/definitions/task_urls"
                }
            }
        }
    }
    "2.21": ${reset."2.13"} {
        request.properties.delete_external_artifacts {
            description: "If set to 'true' then BE will try to delete the extenal artifacts associated with the task from the fileserver (if configured to do so)"
            type: boolean
            default: true
        }
    }
}
reset_many {
    "2.13": ${_definitions.batch_operation} {
        description: Reset tasks
        request {
            properties {
                ids.description: "IDs of the tasks to reset"
                force = ${_references.force_arg} {
                    description: "If not true, call fails if the task status is 'completed'"
                }
                clear_all {
                    description: "Clear script and execution sections completely"
                    type: boolean
                    default: false
                }
                return_file_urls {
                    description: "If set to 'true' then return the urls of the files that were uploaded by the tasks. Default value is 'false'"
                    type: boolean
                }
                delete_output_models {
                    description: "If set to 'true' then delete output models of the tasks that are not referenced by other tasks. Default value is 'true'"
                    type: boolean
                }
            }
        }
        response {
            properties {
                succeeded.items.properties.dequeued {
                    description: "Indicates whether the task was dequeued"
                    type: boolean
                }
                succeeded.items.properties.updated {
                    description: "Number of tasks updated (0 or 1)"
                    type: integer
                    enum: [ 0, 1 ]
                }
                succeeded.items.properties.fields {
                    description: "Updated fields names and values"
                    type: object
                    additionalProperties: true
                }
                succeeded.items.properties.deleted_models {
                    description: "Number of output models deleted by the reset"
                    type: integer
                }
                succeeded.items.properties.urls {
                    description: "The urls of the files that were uploaded by the task. Returned if the 'return_file_urls' was set to 'true'"
                    "$ref": "#/definitions/task_urls"
                }
            }
        }
    }
    "2.21": ${reset_many."2.13"} {
        request.properties.delete_external_artifacts {
            description: "If set to 'true' then BE will try to delete the extenal artifacts associated with the tasks from the fileserver (if configured to do so)"
            type: boolean
            default: true
        }
    }
}
delete_many {
    "2.13": ${_definitions.batch_operation} {
        description: Delete tasks
        request {
            properties {
                ids.description: "IDs of the tasks to delete"
                move_to_trash {
                    description: "Move task to trash instead of deleting it. For internal use only, tasks in the trash are not visible from the API and cannot be restored!"
                    type: boolean
                    default: false
                }
                force = ${_references.force_arg} {
                    description: "If not true, call fails if the task status is 'in_progress'"
                }
                return_file_urls {
                    description: "If set to 'true' then return the urls of the files that were uploaded by the tasks. Default value is 'false'"
                    type: boolean
                }
                delete_output_models {
                    description: "If set to 'true' then delete output models of the tasks that are not referenced by other tasks. Default value is 'true'"
                    type: boolean
                }
            }
        }
        response {
            properties {
                succeeded.items.properties.deleted {
                    description: "Indicates whether the task was deleted"
                    type: boolean
                }
                succeeded.items.properties.updated_children {
                    description: "Number of child tasks whose parent property was updated"
                    type: integer
                }
                succeeded.items.properties.updated_models {
                    description: "Number of models whose task property was updated"
                    type: integer
                }
                succeeded.items.properties.deleted_models {
                    description: "Number of deleted output models"
                    type: integer
                }
                succeeded.items.properties.urls {
                    description: "The urls of the files that were uploaded by the task. Returned if the 'return_file_urls' was set to 'true'"
                    "$ref": "#/definitions/task_urls"
                }
            }
        }
    }
    "2.21": ${delete_many."2.13"} {
        request.properties.delete_external_artifacts {
            description: "If set to 'true' then BE will try to delete the extenal artifacts associated with the tasks from the fileserver (if configured to do so)"
            type: boolean
            default: true
        }
    }
}
delete {
    "2.1" {
        description: """Delete a task along with any information stored for it (statistics, frame updates etc.)
        Unless Force flag is provided, operation will fail if task has objects associated with it - i.e. children tasks and projects.
        Models that refer to the deleted task will be updated with a task ID indicating a deleted task.
        """
        request = {
            type: object
            required: [
                task
            ]
            properties {
                move_to_trash {
                    description: "Move task to trash instead of deleting it. For internal use only, tasks in the trash are not visible from the API and cannot be restored!"
                    type: boolean
                    default: false
                }
                force = ${_references.force_arg} {
                    description: "If not true, call fails if the task status is 'in_progress'"
                }
            }
        } ${_references.status_change_request}
        response {
            type: object
            properties {
                deleted {
                    description: "Indicates whether the task was deleted"
                    type: boolean
                }
                updated_children {
                    description: "Number of child tasks whose parent property was updated"
                    type: integer
                }
                updated_models {
                    description: "Number of models whose task property was updated"
                    type: integer
                }
                events {
                    description: "Response from events.delete_for_task"
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
    "2.13": ${delete."2.1"} {
        request {
            properties {
                return_file_urls {
                    description: "If set to 'true' then return the urls of the files that were uploaded by this task. Default value is 'false'"
                    type: boolean
                }
                delete_output_models {
                    description: "If set to 'true' then delete output models of this task that are not referenced by other tasks. Default value is 'true'"
                    type: boolean
                }
            }
        }
        response {
            properties {
                urls {
                    description: "The urls of the files that were uploaded by this task. Returned if the 'return_file_urls' was set to 'true'"
                    "$ref": "#/definitions/task_urls"
                }
            }
        }
    }
    "2.21": ${delete."2.13"} {
        request.properties.delete_external_artifacts {
            description: "If set to 'true' then BE will try to delete the extenal artifacts associated with the task from the fileserver (if configured to do so)"
            type: boolean
            default: true
        }
    }
}
archive {
    "2.12" {
        description: """Archive tasks.
        If a task is queued it will first be dequeued and then archived.
        """
        request = {
            type: object
            required: [
                tasks
            ]
            properties {
                tasks {
                    description: "List of task ids"
                    type: array
                    items { type: string }
                }
                status_reason {
                    description: Reason for status change
                    type: string
                }
                status_message {
                    description: Extra information regarding status change
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                archived {
                    description: "Indicates number of archived tasks"
                    type: integer
                }
            }
        }
    }
}
archive_many {
    "2.13": ${_definitions.batch_operation} {
        description: Archive tasks
        request {
            properties {
                ids.description: "IDs of the tasks to archive"
                status_reason {
                    description: Reason for status change
                    type: string
                }
                status_message {
                    description: Extra information regarding status change
                    type: string
                }
            }
        }
        response {
            properties {
                succeeded.items.properties.archived {
                    description: "Indicates whether the task was archived"
                    type: boolean
                }
            }
        }
    }
}
unarchive_many {
    "2.13": ${_definitions.batch_operation} {
        description: Unarchive tasks
        request {
            properties {
                ids.description: "IDs of the tasks to unarchive"
                status_reason {
                    description: Reason for status change
                    type: string
                }
                status_message {
                    description: Extra information regarding status change
                    type: string
                }
            }
        }
        response {
            properties {
                succeeded.items.properties.unarchived {
                    description: "Indicates whether the task was unarchived"
                    type: boolean
                }
            }
        }
    }
}
started {
    "2.1" {
        description: "Mark a task status as in_progress. Optionally allows to set the task's execution progress."
        request = {
            type: object
            required: [
                task
            ]
            properties.force = ${_references.force_arg} {
                description: "If not true, call fails if the task status is not 'not_started'"
            }
        } ${_references.status_change_request}
        response: ${_definitions.update_response} {
            properties {
                started {
                    description: "Number of tasks started (0 or 1)"
                    type: integer
                    enum: [ 0, 1 ]
                }
            }
        }
    }
}
stop {
    "2.1" {
        description: "Request to stop a running task"
        request = {
            type: object
            required: [
                task
            ]
            properties.force = ${_references.force_arg} {
                description: "If not true, call fails if the task status is not 'in_progress'"
            }
        } ${_references.status_change_request}
        response: ${_definitions.update_response}
    }
}
stop_many {
    "2.13": ${_definitions.change_many_request} {
        description: "Request to stop running tasks"
        request {
            properties {
                ids.description: "IDs of the tasks to stop"
                force = ${_references.force_arg} {
                    description: "If not true, call fails if the task status is not 'in_progress'"
                }
            }
        }
    }
}
stopped {
    "2.1" {
        description: "Signal a task has stopped"
        request = {
            type: object
            required: [
                task
            ]
            properties.force = ${_references.force_arg} {
                description: "If not true, call fails if the task status is not 'stopped'"
            }
        } ${_references.status_change_request}
        response: ${_definitions.update_response}
    }
}
failed {
    "2.1" {
        description: "Indicates that task has failed"
        request = {
            type: object
            required: [
                task
            ]
            properties.force = ${_references.force_arg}
        } ${_references.status_change_request}
        response: ${_definitions.update_response}
    }
}
close {
    "2.1" {
        description: "Indicates that task is closed"
        request = {
            type: object
            required: [
                task
            ]
            properties.force = ${_references.force_arg}
        } ${_references.status_change_request}
        response: ${_definitions.update_response}
    }
}
publish {
    "2.1" {
        description: """Mark a task status as published.

        For Annotation tasks - if any changes were committed by this task, a new version in the dataset together with an output view are created.

        For Training tasks - if a model was created, it should be set to ready."""
        request = {
            type: object
            required: [
                task
            ]
            properties {
                force = ${_references.force_arg} {
                    description: "If not true, call fails if the task status is not 'stopped'"
                }
                publish_model {
                    description: "Indicates that the task output model (if exists) should be published. Optional, the default value is True."
                    type: boolean
                }
            }
        } ${_references.status_change_request}
        response: ${_definitions.update_response}
    }
}
publish_many {
    "2.13": ${_definitions.change_many_request} {
        description: Publish tasks
        request {
            properties {
                ids.description: "IDs of the tasks to publish"
                force = ${_references.force_arg} {
                    description: "If not true, call fails if the task status is not 'stopped'"
                }
                publish_model {
                    description: "Indicates that the task output model (if exists) should be published. Optional, the default value is True."
                    type: boolean
                }
            }
        }
    }
}
enqueue {
    "1.5" {
        description: """Adds a task into a queue.

Fails if task state is not 'created'.

Fails if the following parameters in the task were not filled:

* execution.script.repository

* execution.script.entrypoint
"""
        request = {
            type: object
            required: [
                task
            ]
            properties {
                queue {
                    description: "Queue id. If not provided and no queue name is passed then task is added to the default queue."
                    type: string
                }
            }

        } ${_references.status_change_request}
        response: ${_definitions.update_response} {
            properties {
                queued {
                    description: "Number of tasks queued (0 or 1)"
                    type: integer
                    enum: [ 0, 1 ]
                }
            }
        }
    }
    "2.19": ${enqueue."1.5"} {
        request.properties.queue_name {
            description: The name of the queue. If the queue does not exist then it is auto-created. Cannot be used together with the queue id
            type: string
        }
    }
    "2.22": ${enqueue."2.19"} {
        request.properties.verify_watched_queue {
            description: If passed then check wheter there are any workers watiching the queue
            type: boolean
            default: false
        }
        response.properties.queue_watched {
            description: Returns true if there are workers or autscalers working with the queue
            type: boolean
        }
    }
}
enqueue_many {
    "2.13": ${_definitions.change_many_request} {
        description: Enqueue tasks
        request {
            properties {
                ids.description: "IDs of the tasks to enqueue"
                queue {
                    description: "Queue id. If not provided and no queue name is passed then tasks are added to the default queue."
                    type: string
                }
                validate_tasks {
                    description: "If set then tasks are validated before enqueue"
                    type: boolean
                    default: false
                }
            }
        }
        response {
            properties {
                succeeded.items.properties.queued {
                    description: "Indicates whether the task was queued"
                    type: boolean
                }
            }
        }
    }
    "2.19": ${enqueue_many."2.13"} {
        request.properties.queue_name {
            description: The name of the queue. If the queue does not exist then it is auto-created. Cannot be used together with the queue id
            type: string
        }
    }
    "2.22": ${enqueue_many."2.19"} {
        request.properties.verify_watched_queue {
            description: If passed then check wheter there are any workers watiching the queue
            type: boolean
            default: false
        }
        response.properties.queue_watched {
            description: Returns true if there are workers or autscalers working with the queue
            type: boolean
        }
    }
}
dequeue {
    "1.5" {
        description: """Remove a task from its queue.
        Fails if task status is not queued."""
        request = {
            type: object
            required: [
                task
            ]
        } ${_references.status_change_request}
        response: ${_definitions.update_response} {
            properties {
                dequeued {
                    description: "Number of tasks dequeued (0 or 1)"
                    type: integer
                    enum: [ 0, 1 ]
                }
            }
        }
    }
    "2.25": ${dequeue."1.5"} {
        request.properties.remove_from_all_queues {
            type: boolean
            description: If set to 'true' then the task is searched and removed from all the queues. Otherwise only from the queue stored in the task execution parameters
            default: false
        }
    }
    "2.26": ${dequeue."2.25"} {
        request.properties.new_status {
            type: string
            description: The new status to assign to the task after the dequeue instead of the default one
        }
    }
}
dequeue_many {
    "2.13": ${_definitions.change_many_request} {
        description: Dequeue tasks
        request {
            properties {
                ids.description: "IDs of the tasks to dequeue"
            }
        }
        response {
            properties {
                succeeded.items.properties.dequeued {
                    description: "Indicates whether the task was dequeued"
                    type: boolean
                }
            }
        }
    }
    "2.25": ${dequeue_many."2.13"} {
        request.properties.remove_from_all_queues {
            type: boolean
            description: If set to 'true' then the tasks are searched and removed from all the queues. Otherwise only from the queue stored in the task execution parameters
            default: false
        }
    }
    "2.26": ${dequeue_many."2.25"} {
        request.properties.new_status {
            type: string
            description: The new status to assign to the task after the dequeue instead of the default one
        }
    }
}
set_requirements {
    "2.1" {
        description: """Set the script requirements for a task"""
        request {
            type: object
            required: [
                task
                requirements
            ]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
                requirements {
                    description: "A JSON object containing requirements strings by key"
                    type: object
                }
            }
        }
        response: ${_definitions.update_response}
    }
}

completed {
    "2.2" {
        description: "Signal a task has completed"
        request = {
            type: object
            required: [
                task
            ]
            properties.force = ${_references.force_arg} {
                description: "If not true, call fails if the task status is not in_progress/stopped"
            }
        } ${_references.status_change_request}
        response: ${_definitions.update_response}
    }
    "2.20": ${completed."2.2"} {
        request.properties.publish {
            type: boolean
            default: false
            description: If set and the task is completed successfully then it is published
        }
        response.properties.published {
            description: "Number of tasks published (0 or 1)"
            type: integer
            enum: [0, 1]
        }
    }
}

ping {
    "2.1" {
        description: """ Refresh the task's last update time"""
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
            }
        }
        response {
            type: object
            additionalProperties: false
        }
    }
}

add_or_update_artifacts {
    "2.10" {
        description: """Update existing artifacts (search by key/mode) and add new ones"""
        request {
            type: object
            required: [task, artifacts]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
                artifacts {
                    description: "Artifacts to add or update"
                    type: array
                    items {"$ref": "#/definitions/artifact"}
                }
                force {
                    description: "If set to True then both new and running task artifacts can be edited. Otherwise only the new task ones. Default is False"
                    type: boolean
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Indicates if the task was updated successfully"
                    type: integer
                }
            }
        }
    }
}
delete_artifacts {
    "2.10" {
        description: """Delete existing artifacts (search by key/mode)"""
        request {
            type: object
            required: [task, artifacts]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
                artifacts {
                    description: "Artifacts to delete"
                    type: array
                    items {"$ref": "#/definitions/artifact_id"}
                }
                force {
                    description: "If set to True then both new and running task artifacts can be deleted. Otherwise only the new task ones. Default is False"
                    type: boolean
                }
            }
        }
        response {
            type: object
            properties {
                deleted {
                    description: "Indicates if the task was updated successfully"
                    type: integer
                }
            }
        }
    }
}

make_public {
    "2.9" {
        description: """Convert company tasks to public"""
        request {
            type: object
            properties {
                ids {
                    description: "Ids of the tasks to convert"
                    type: array
                    items { type: string}
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of tasks updated"
                    type: integer
                }
            }
        }
    }
}

make_private {
    "2.9" {
        description: """Convert public tasks to private"""
        request {
            type: object
            properties {
                ids {
                    description: "Ids of the tasks to convert. Only the tasks originated by the company can be converted"
                    type: array
                    items { type: string}
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of tasks updated"
                    type: integer
                }
            }
        }
    }
}

get_hyper_params {
    "2.9": {
        description: "Get the list of task hyper parameters"
        request {
            type: object
            required: [tasks]
            properties {
                tasks {
                    description: "Task IDs"
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties {
                params {
                    description: "Hyper parameters (keyed by task ID)"
                    type: array
                    items {
                        type: object
                        properties {
                            "task": {
                                description: "Task ID"
                                type: string
                            }
                            "hyperparams": {
                                description: "Hyper parameters"
                                type: array
                                items {"$ref": "#/definitions/params_item"}
                            }
                        }
                    }
                }
            }
        }
    }
}
edit_hyper_params {
    "2.9" {
        description: "Add or update task hyper parameters"
        request {
            type: object
            required: [ task, hyperparams ]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
                hyperparams {
                    description: "Task hyper parameters. The new ones will be added and the already existing ones will be updated"
                    type: array
                    items {"$ref": "#/definitions/params_item"}
                }
                replace_hyperparams {
                    description: """Can be set to one of the following:
                     'all' - all the hyper parameters will be replaced with the provided ones
                     'section' - the sections that present in the new parameters will be replaced with the provided parameters
                     'none' (the default value) - only the specific parameters will be updated or added"""
                    "$ref": "#/definitions/replace_hyperparams_enum"
                }
                force {
                    description: "If set to True then both new and running task hyper params can be edited. Otherwise only the new task ones. Default is False"
                    type: boolean
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Indicates if the task was updated successfully"
                    type: integer
                }
            }
        }
    }
}
delete_hyper_params {
    "2.9": {
        description: "Delete task hyper parameters"
        request {
            type: object
            required: [ task, hyperparams ]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
                hyperparams {
                    description: "List of hyper parameters to delete. In case a parameter with an empty name is passed all the section will be deleted"
                    type: array
                    items { "$ref": "#/definitions/param_key" }
                }
                force {
                    description: "If set to True then both new and running task hyper params can be deleted. Otherwise only the new task ones. Default is False"
                    type: boolean
                }
            }
        }
        response {
            type: object
            properties {
                deleted {
                    description: "Indicates if the task was updated successfully"
                    type: integer
                }
            }
        }
    }
}

get_configurations {
    "2.9": {
        description: "Get the list of task configurations"
        request {
            type: object
            required: [tasks]
            properties {
                tasks {
                    description: "Task IDs"
                    type: array
                    items { type: string }
                }
                names {
                    description: "Names of the configuration items to retreive. If not passed or empty then all the configurations will be retreived."
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties {
                configurations {
                    description: "Configurations (keyed by task ID)"
                    type: array
                    items {
                        type: object
                        properties {
                            "task" {
                                description: "Task ID"
                                type: string
                            }
                            "configuration" {
                                description: "Configuration list"
                                type: array
                                items {"$ref": "#/definitions/configuration_item"}
                            }
                        }
                    }
                }
            }
        }
    }
}
get_configuration_names {
    "2.9": {
        description: "Get the list of task configuration items names"
        request {
            type: object
            required: [tasks]
            properties {
                tasks {
                    description: "Task IDs"
                    type: array
                    items { type: string }
                }
                skip_empty {
                    description: If set to 'true' then the names for configurations with missing values are not returned
                    type: boolean
                    default: true
                }
            }
        }
        response {
            type: object
            properties {
                configurations {
                    description: "Names of task configuration items (keyed by task ID)"
                    type: object
                    properties {
                        task {
                            description: "Task ID"
                            type: string
                        }
                        names {
                            description: "Configuration names"
                            type: array
                            items {type: string}
                        }
                    }
                }
            }
        }
    }
}
edit_configuration {
    "2.9" {
        description: "Add or update task configuration"
        request {
            type: object
            required: [ task, configuration ]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
                configuration {
                    description: "Task configuration items. The new ones will be added and the already existing ones will be updated"
                    type: array
                    items {"$ref": "#/definitions/configuration_item"}
                }
                replace_configuration {
                    description: "If set then the all the configuration items will be replaced with the provided ones. Otherwise only the provided configuration items will be updated or added"
                    type: boolean
                }
                force {
                    description: "If set to True then both new and running task configuration can be edited. Otherwise only the new task ones. Default is False"
                    type: boolean
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Indicates if the task was updated successfully"
                    type: integer
                }
            }
        }
    }
}
delete_configuration {
    "2.9": {
        description: "Delete task configuration items"
        request {
            type: object
            required: [ task, configuration ]
            properties {
                task {
                    description: "Task ID"
                    type: string
                }
                configuration {
                    description: "List of configuration itemss to delete"
                    type: array
                    items { type: string }
                }
                force {
                    description: "If set to True then both new and running task configuration can be deleted. Otherwise only the new task ones. Default is False"
                    type: boolean
                }
            }
        }
        response {
            type: object
            properties {
                deleted {
                    description: "Indicates if the task was updated successfully"
                    type: integer
                }
            }
        }
    }
}
move {
    "2.12" {
        description: "Move tasks to a project"
        request {
            type: object
            required: [ids]
            properties {
                ids {
                    description: "Tasks to move"
                    type: array
                    items { type: string }
                }
                project {
                    description: "Target project ID. If not provided, `project_name` must be provided. Use null for the root project"
                    type: string
                }
                project_name {
                    description: "Target project name. If provided and a project with this name does not exist, a new project will be created. If not provided, `project` must be provided."
                    type: string
                }
            }
        }
        response {
            type: object
            additionalProperties: true
        }
    }
}
update_tags {
    "2.27" {
        description: Add or remove tags from multiple tasks
        request {
            type: object
            properties {
                ids {
                    type: array
                    description: IDs of the tasks to update
                    items {type: string}
                }
                add_tags {
                    type: array
                    description: User tags to add
                    items {type: string}
                }
                remove_tags {
                    type: array
                    description: User tags to remove
                    items {type: string}
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    type: integer
                    description: The number of updated tasks
                }
            }
        }
    }
}