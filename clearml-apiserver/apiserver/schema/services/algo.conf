_description: "Provides a management API for datasets in the system."
_definitions:{
    algo {
        type: object
        properties {
            id {
                description: "algorithm ID"
                type: string
            }
            name {
                description: "algorithm Name"
                type: string
            }
            description {
                description: "algorithm description"
                type: string
            }
            type {
                description: "algorithm type"
                type: string
            }
            size {
                description: "algorithm szie"
                type: string
            }
            is_public {
                description: "This algorithm is public or not"
                type: boolean
            }
            versions_num {
                description: "The count number of algorithm versions"
                type: string
            }
            last_update {
                description: "Last dataset update time"
                type: string
                format: "date-time"
            }
            create_time {
                description: "Dataset create time"
                type: string
                format: "date-time"
            }
        }
    }
}
create {
    "1.0" {
        description: "Create a new algorithm"
        request {
            type: object
            properties {
                name {
                    description: "algorithm name"
                    type: string
                }
                description {
                    description: "algorithm description."
                    type: string
                }
                disabled {
                    description: "algorithm description."
                    type: boolean
                }
                type {
                    description: "algorithm name"
                    type: string
                }
                category {
                    description: "algorithm description."
                    type: string
                }
                source {
                    description: "algorithm description."
                    type: string
                }

            }
        }
        response {
            type: object
            properties {
                id {
                    description: "algorithm id"
                    type: string
                }
            }
        }
    }
}

upload_file {
    "1.0" {
        description: "upload file to algorithm"
        request {
            type: object
            properties {
                algo_id {
                    description: "The algorithm name of file to upload"
                    type: string
                }
                version {
                    description: "The file version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "algorithm names"
                    type: string
                }
                size {
                    description: "algorithm szie"
                    type: string
                }
                last_update {
                    description: "Last algorithm update time"
                    type: string
                    format: "date-time"
                }      
            }
        }
    }
}

file_list{
    "1.0" {
        description: "The algo list"
        request{
            type: object
            properties{
                algo_id {
                    description: "The algo name of file to upload"
                    type: string
                }
                version {
                    description: "The file version"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                name {
                    description: "algo names"
                    type: string
                }
                size {
                    description: "algo szie"
                    type: string
                }
                Etag {
                    description: "algo etag"
                    type: string
                }
                last_update {
                    description: "Last algo update time"
                    type: string
                    format: "date-time"
                }      
            }
        }
    }
}
get_all {
    "1.0" {
        description: "Get all algorithm"
        request {
            type: object
            properties {
                name {
                    description: "algorithm name"
                    type: string
                }
                type{
                    description: "algorithm type"
                    type: string
                }
                category{
                    description:"is publish"
                    type: string
                }
                source{
                    description:"source"
                    type: string
                }
                only_fields {
                    description: "List of algorithm field names"
                    type: array
                    items { type: string }
                }
                page {
                    description: "Page number, returns a specific page out of the resulting list of users"
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                order_by {
                    description: "List of field names to order by. Use '-' prefix to specify descending order. Optional, recommended when using page"
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: "datasets list"
                    type: array
                }
            }
        }
    }
}
get_all_release {
    "1.0" {
        description: "Get all algorithm"
        request {
            type: object
            properties {
                algo {
                    description: "algorithm name"
                    type: string
                }
                page {
                    description: "Page number, returns a specific page out of the resulting list of users"
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                order_by {
                    description: "List of field names to order by. Use '-' prefix to specify descending order. Optional, recommended when using page"
                    type: array
                    items { type: string }
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: "datasets list"
                    type: array
                }
            }
        }
    }
}

get_images{
    "2.1" {
        description: "get image lsit for ide"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                image {
                    description: User list
                    type: array
                    items: string
                }
            }
        }
    }
}
get_image_tag{
    "2.1" {
        description: "get image lsit for ide"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                tag {
                    description: User list
                    type: array
                    items: string
                }
            }
        }
    }
}


set_offline{
    "1.0" {
        description: "Algorithm offline"
        request {
            type: object
            properties {
                algo_id {
                    description: "algo id"
                    type: string
                }
                version {
                    description: "algo version"
                    type: string
                }
            }
        }
        response {
            type: string
        }
    }
}

set_online{
    "1.0" {
        description: "Algorithm online"
        request {
            type: object
            properties {
                algo_id {
                    description: "algo id"
                    type: string
                }
                version {
                    description: "algo version"
                    type: string
                }
            }
        }
        response {
            type: string
        }
    }
}

get_resource{
    "2.1" {
        description: "get_resource"
        request {
            type: object
            required: [
            ]
            properties {
            }
        }
        response {
            type: object
            properties {}
            additionalProperties: false
        }
    }
}

get_status{
    "2.1"{
        description: "get_status"
        request {
            type: object
            required: [
            ]
            properties {
            }
        }
        response {
            type: object
            properties {}
            description: "algorithm status"
        }
    }
}
get_version_compare{
    "2.1"{
        description: "get_status"
        request {
            type: object
            required: [
            ]
            properties {
            }
        }
        response {
            type: object
            properties {
              properties: {
                previous_version {
                    description: "Data from the previous version"
                    type: object
                    properties {
                        input_param {
                            description: "Input parameters from the previous version"
                            type: string
                        }
                        output_param{
                            description: "Output parameters from the previous version"
                            type: string
                        }
                    }
                    required : ["input_param", "output_param"]
                    additionalProperties: false
                }
                current_version {
                    description: "Data from the current version"
                    type: object
                    properties {
                        input_param {
                            description: "Input parameters from the current version"
                        }
                        output_param {
                            description: "Output parameters from the current version"
                            type: string
                        }
                    }
                    required: ["input_param", "output_param"]
                    additionalProperties: false
                }
            }
            required: ["previous_version", "current_version"]
            additionalProperties: false
            }
            additionalProperties: false
        }
    }
}
delete{
    "2.1" {
        description: "Delete algorithm"
        request {
            type: object
            properties {
                algo_id {
                    description: "algorithm id"
                    type: string
                }
            }
        }
        response {
            type:object
        }
    }
}
delete_release{
    "2.1" {
        description: "Delete algorithm"
        request {
            type: object
            properties {
                algo_id {
                    description: "algorithm id"
                    type: string
                }
                version {
                    description: "algorithm version"
                    type: string
                }
            }
        }
        response {
            type: boolean
            description: "True if the algorithm was deleted successfully, false otherwise"
        }
    }
}

download_release{
    "2.1" {
        description: "Download algorithm"
        request {
            type: object
            properties {
                algo_id {
                    description: "algorithm id"
                    type: string
                }
            }
        }
        response{
            type: string
            description: "The download url of algorithm"
        }
    }
}
get_baseline_dataset{
    "2.1" {
        description: "get dataset"
        request {
            type: object
            properties {
            }
        }
        response{
            type: string
            description: "dataset name"
        }
    }
}
get_algo_type{
     "2.1" {
        description: "get tag"
        request {
            type: object
            properties {
            }
        }
        response{
            type: string
            description: "tag name"
        }
    }
}
get_by_id{
    "2.1" {
        description: "get tag"
        request {
            type: object
            properties {
            }
        }
        response{
            type: object
            properties {
            }
            description: "tag name"
        }
    }
}

set_current{
    "2.1" {
        description: "set current"
        request {
            type: object
            properties {
            }
        }
        response{
            type: object
            properties {
            }
        }
    }
}

statistics{
    "2.1" {
        description: "set current"
        request {
            type: object
            properties {
            }
        }
        response{
            type: object
            properties {
            }
        }
    }
}
build_image{
    "2.1" {
        description: "set current"
        request {
            type: object
            properties {
            }
        }
        response{
            type: string
            description: "image name"
        }
    }
}
storage_training_result{
    "2.1" {
        description: "storage_training_result"
        request {
            type: object
            properties {
            }
        }
        response{
            type: object
            properties {
            }
        }
    }
}
update{
    "2.1" {
        description: "storage_training_result"
        request {
            type: object
            properties {
            }
        }
        response{
            type: object
            properties {
            }
        }
    }
}
update_param{
    "2.1" {
        description: "storage_training_result"
        request {
            type: object
            properties {
            }
        }
        response{
            type: object
            properties {
            }
        }
    }
}
get_right{
    "2.1" {
        description: "storage_training_result"
        request {
            type: object
            properties {
            }
        }
        response{
            type: object
            properties {
            }
        }
    }
}