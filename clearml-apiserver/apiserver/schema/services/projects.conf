_description: "Provides support for defining Projects containing Tasks, Models and Dataset Versions."
_definitions {
    include "_common.conf"
    multi_field_pattern_data {
        type: object
        properties {
            pattern {
                description: "Pattern string (regex)"
                type: string
            }
            fields {
                description: "List of field names"
                type: array
                items { type: string }
            }
        }
    }
    project {
        type: object
        properties {
            id {
                description: "Project id"
                type: string
            }
            name {
                description: "Project name"
                type: string
            }
            basename {
                description: "Project base name"
                type: string
            }
            description {
                description: "Project description"
                type: string
            }
            user {
                description: "Associated user id"
                type: string
            }
            company {
                description: "Company id"
                type: string
            }
            created {
                description: "Creation time"
                type: string
                format: "date-time"
            }
            tags {
                description: "User-defined tags"
                type: array
                items { type: string }
            }
            system_tags {
                description: "System tags. This field is reserved for system use, please don't use it."
                type: array
                items { type: string }
            }
            default_output_destination {
                description: "The default output destination URL for new tasks under this project"
                type: string
            }
            last_update {
                description: "Last project update time. Reflects the last time the project metadata was changed or a task in this project has changed status"
                type: string
                format: "date-time"
            }
        }
    }
    stats_datasets {
        type: object
        properties {
            count {
                description: Number of datasets
                type: integer
            }
            tags {
                description: Dataset tags
                type: array
                items {type: string}
            }
        }
    }
    stats_status_count {
        type: object
        properties {
            total_runtime {
                description: "Total run time of all tasks in project (in seconds)"
                type: integer
            }
            total_tasks {
                description: "Number of tasks"
                type: integer
            }
            completed_tasks_24h {
                description: "Number of tasks completed in the last 24 hours"
                type: integer
            }
            last_task_run {
                description: "The most recent started time of a task"
                type: integer
            }
            status_count {
                description: "Status counts"
                type: object
                properties {
                    created {
                        description: "Number of 'created' tasks in project"
                        type: integer
                    }
                    completed {
                        description: "Number of 'completed' tasks in project"
                        type: integer
                    }
                    queued {
                        description: "Number of 'queued' tasks in project"
                        type: integer
                    }
                    in_progress {
                        description: "Number of 'in_progress' tasks in project"
                        type: integer
                    }
                    stopped {
                        description: "Number of 'stopped' tasks in project"
                        type: integer
                    }
                    published {
                        description: "Number of 'published' tasks in project"
                        type: integer
                    }
                    closed {
                        description: "Number of 'closed' tasks in project"
                        type: integer
                    }
                    failed {
                        description: "Number of 'failed' tasks in project"
                        type: integer
                    }
                    unknown {
                        description: "Number of 'unknown' tasks in project"
                        type: integer
                    }
                }
            }
        }
    }
    stats  {
        type: object
        properties {
            active {
                description: "Stats for active tasks"
                "$ref": "#/definitions/stats_status_count"
            }
            archived {
                description: "Stats for archived tasks"
                "$ref": "#/definitions/stats_status_count"
            }
            datasets {
                description: "Stats for childrent datasets"
                "$ref": "#/definitions/stats_datasets"
            }
        }
    }
    projects_get_all_response_single {
        // copy-paste from project definition
        type: object
        properties {
            id {
                description: "Project id"
                type: string
            }
            name {
                description: "Project name"
                type: string
            }
            basename {
                description: "Project base name"
                type: string
            }
            description {
                description: "Project description"
                type: string
            }
            user {
                description: "Associated user id"
                type: string
            }
            company {
                description: "Company id"
                type: string
            }
            created {
                description: "Creation time"
                type: string
                format: "date-time"
            }
            tags {
                description: "User-defined tags"
                type: array
                items { type: string }
            }
            system_tags {
                description: "System tags. This field is reserved for system use, please don't use it."
                type: array
                items { type: string }
            }
            default_output_destination {
                description: "The default output destination URL for new tasks under this project"
                type: string
            }
            last_update {
                description: "Last project update time. Reflects the last time the project metadata was changed or a task in this project has changed status"
                type: string
                format: "date-time"
            }
            // extra properties
            hidden {
                description: "Returned if the search_hidden flag was specified in the get_all_ex call and the project is hidden"
                type: boolean
            }
            stats {
                description: "Additional project stats"
                "$ref": "#/definitions/stats"
            }
            sub_projects {
                description: "The list of sub projects"
                type: array
                items {
                    type: object
                    properties {
                        id {
                            description: "Subproject ID"
                            type: string
                        }
                        name {
                            description: "Subproject name"
                            type: string
                        }
                    }
                }
            }
            own_datasets {
                description: "The amount of datasets/hyperdatasers under this project (without children projects). Returned if 'check_own_contents' flag is set in the request and children_type is set to 'dataset' or 'hyperdataset'"
                type: integer
            }
            own_tasks {
                description: "The amount of tasks under this project (without children projects). Returned if 'check_own_contents' flag is set in the request"
                type: integer
            }
            own_models {
                description: "The amount of models under this project (without children projects). Returned if 'check_own_contents' flag is set in the request"
                type: integer
            }
            dataset_stats {
                description: Project dataset statistics
                type: object
                properties {
                    file_count {
                        type: integer
                        description: The number of files stored in the dataset
                    }
                    total_size {
                        type: integer
                        description: The total dataset size in bytes
                    }
                }
            }
        }
    }
    metric_variant_result {
        type: object
        properties {
            metric {
                description: "Metric name"
                type: string
            }
            metric_hash {
                description: """Metric name hash. Used instead of the metric name when categorizing
                last metrics events in task objects."""
                type: string
            }
            variant {
                description: "Variant name"
                type: string
            }
            variant_hash {
                description: """Variant name hash. Used instead of the variant name when categorizing
                last metrics events in task objects."""
                type: string
            }
        }
    }
    tags_request {
        type: object
        properties {
            include_system {
                description: "If set to 'true' then the list of the system tags is also returned. The default value is 'false'"
                type: boolean
                default: false
            }
            projects {
                description: "The list of projects under which the tags are searched. If not passed or empty then all the projects are searched"
                type: array
                items { type: string }
            }
            filter {
                description: "Filter on entities to collect tags from"
                type: object
                properties {
                    tags {
                        description: "The list of tag values to filter by. Use 'null' value to specify empty tags. Use '__Snot' value to specify that the following value should be excluded"
                        type: array
                        items {type: string}
                    }
                    system_tags {
                        description: "The list of system tag values to filter by. Use 'null' value to specify empty system tags. Use '__Snot' value to specify that the following value should be excluded"
                        type: array
                        items {type: string}
                    }
                }
            }
        }
    }
    tags_response {
        type: object
        properties {
            tags {
                description: "The list of unique tag values"
                type: array
                items {type: string}
            }
            system_tags {
                description: "The list of unique system tag values. Returned only if 'include_system' is set to 'true' in the request"
                type: array
                items {type: string}
            }
        }
    }
    urls {
        type: object
        properties {
            model_urls {
                type: array
                items {type: string}
            }
            event_urls {
                type: array
                items {type: string}
            }
            artifact_urls {
                type: array
                items {type: string}
            }
        }
    }
}

create {
    "2.1" {
        description: "Create a new project"
        request {
            type: object
            required :[name]
            properties {
                name {
                    description: "Project name Unique within the company."
                    type: string
                }
                description {
                    description: "Project description."
                    type: string
                }
                tags {
                    description: "User-defined tags"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
                default_output_destination  {
                    description: "The default output destination URL for new tasks under this project"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                id {
                    description: "Project id"
                    type: string
                }
            }
        }
    }
}
get_by_id {
    "2.1" {
        description: ""
        request {
            type: object
            required: [ project ]
            properties {
                project {
                    description: "Project id"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                project {
                    description: "Project info"
                    "$ref": "#/definitions/project"
                }
            }
        }
    }
}
get_all {
    "2.1" {
        description: "Get all the company's projects and all public projects"
        request {
            type: object
            properties {
                id {
                    description: "List of IDs to filter by"
                    type: array
                    items { type: string }
                }
                name {
                    description: "Get only projects whose name matches this pattern (python regular expression syntax)"
                    type: string
                }
                basename {
                    description: "Project base name"
                    type: string
                }
                description {
                    description: "Get only projects whose description matches this pattern (python regular expression syntax)"
                    type: string
                }
                tags {
                    description: "User-defined tags list used to filter results. Prepend '-' to tag name to indicate exclusion"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list used to filter results. Prepend '-' to system tag name to indicate exclusion"
                    type: array
                    items { type: string }
                }
                order_by {
                    description: "List of field names to order by. When search_text is used, '@text_score' can be used as a field representing the text score of returned documents. Use '-' prefix to specify descending order. Optional, recommended when using page"
                    type: array
                    items { type: string }
                }
                page {
                    description: "Page number, returns a specific page out of the resulting list of projects"
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                search_text {
                    description: "Free text search query"
                    type: string
                }
                only_fields {
                    description: "List of document's field names (nesting is supported using '.', e.g. execution.model_labels). If provided, this list defines the query's projection (only these fields will be returned for each result entry)"
                    type: array
                    items { type: string }
                }
                _all_ {
                    description: "Multi-field pattern condition (all fields match pattern)"
                    "$ref": "#/definitions/multi_field_pattern_data"
                }
                _any_ {
                    description: "Multi-field pattern condition (any field matches pattern)"
                    "$ref": "#/definitions/multi_field_pattern_data"
                }
            }
        }
        response {
            type: object
            properties {
                projects {
                    description: "Projects list"
                    type: array
                    items { "$ref": "#/definitions/projects_get_all_response_single" }
                }
            }
        }
    }
    "2.13": ${get_all."2.1"} {
        request {
            properties {
                shallow_search {
                    description: "If set to 'true' then the search with the specified criteria is performed among top level projects only (or if parents specified, among the direct children of the these parents). Otherwise the search is performed among all the company projects (or among all of the descendants of the specified parents)."
                    type: boolean
                    default: false
                }
            }
        }
    }
    "2.14": ${get_all."2.13"} {
        request.properties.search_hidden {
            description: "If set to 'true' then hidden projects are included in the search results"
            type: boolean
            default: false
        }
    }
    "2.15": ${get_all."2.14"} {
        request {
            properties {
                scroll_id {
                    type: string
                    description: "Scroll ID returned from the previos calls to get_all_ex"
                }
                refresh_scroll {
                    type: boolean
                    description: "If set then all the data received with this scroll will be requeried"
                }
                size {
                    type: integer
                    minimum: 1
                    description: "The number of projects to retrieve"
                }
            }
        }
        response.properties.scroll_id {
            type: string
            description: "Scroll ID that can be used with the next calls to get_all_ex to retrieve more data"
        }
    }
}
get_all_ex {
    internal: true
    "2.1": ${get_all."2.1"} {
        request {
            properties {
                include_stats {
                    description: "If true, include project statistic in response."
                    type: boolean
                    default: false
                }
                stats_for_state {
                    description: "Report stats include only statistics for tasks in the specified state. If Null is provided, stats for all task states will be returned."
                    type: string
                    enum: [ active, archived ]
                    default: active
                }
                non_public {
                    description: "Return only non-public projects"
                    type: boolean
                    default: false
                }
            }
        }
    }
    "2.13": ${get_all_ex."2.1"} {
        request {
            properties {
                active_users {
                    description: "The list of users that were active in the project. If passes then the resulting projects are filtered to the ones that have tasks created by these users"
                    type: array
                    items: {type: string}
                }
                shallow_search {
                    description: "If set to 'true' then the search with the specified criteria is performed among top level projects only (or if parents specified, among the direct children of the these parents). Otherwise the search is performed among all the company projects (or among all of the descendants of the specified parents)."
                    type: boolean
                    default: false
                }
                check_own_contents {
                    description: "If set to 'true' and project ids are passed to the query then for these projects their own tasks and models are counted"
                    type: boolean
                    default: false
                }
            }
        }
    }
    "2.14": ${get_all_ex."2.13"} {
        request.properties.search_hidden {
            description: "If set to 'true' then hidden projects are included in the search results"
            type: boolean
            default: false
        }
    }
    "2.15": ${get_all_ex."2.14"} {
        request {
            properties {
                scroll_id {
                    type: string
                    description: "Scroll ID returned from the previos calls to get_all"
                }
                refresh_scroll {
                    type: boolean
                    description: "If set then all the data received with this scroll will be requeried"
                }
                size {
                    type: integer
                    minimum: 1
                    description: "The number of projects to retrieve"
                }
            }
        }
        response.properties.scroll_id {
            type: string
            description: "Scroll ID that can be used with the next calls to get_all to retrieve more data"
        }
    }
    "2.16": ${get_all_ex."2.15"} {
        request.properties.stats_with_children {
            description: "If include_stats flag is set then this flag contols whether the child projects tasks are taken into statistics or not"
            type: boolean
            default: true
        }
    }
    "2.17": ${get_all_ex."2.16"} {
        request.properties.include_stats_filter {
            description: The filter for selecting entities that participate in statistics calculation. For each task field that you want to filter on pass the list of allowed values. Prepend the value with '-' to exclude
            type: object
            additionalProperties: true
        }
    }
    "2.20": ${get_all_ex."2.17"} {
        request.properties.include_dataset_stats {
            description: "If true, include project dataset statistic in response"
            type: boolean
            default: false
        }
    }
    "2.23": ${get_all_ex."2.20"} {
        request.properties {
            allow_public {
                description: "Allow public projects to be returned in the results"
                type: boolean
                default: true
            }
        }
    }
    "2.24": ${get_all_ex."2.23"} {
        request.properties.children_type {
            description: If specified that only the projects under which the entities of this type can be found will be returned
            type: string
            enum: [pipeline, report, dataset]
        }
    }
    "2.25": ${get_all_ex."2.24"} {
        request.properties.children_tags {
            description: "The list of tag values to filter children by. Takes effect only if children_type is set. Use 'null' value to specify empty tags. Use '__Snot' value to specify that the following value should be excluded"
            type: array
            items {type: string}
        }
    }
    "2.27": ${get_all_ex."2.25"} {
        request.properties {
            filters {
                type: object
                additionalProperties: ${_definitions.field_filter}
            }
            children_tags_filter: ${_definitions.field_filter}
        }
    }
}
update {
    "2.1" {
        description: "Update project information"
        request {
            type: object
            required: [ project ]
            properties {
                project {
                    description: "Project id"
                    type: string
                }
                name {
                    description: "Project name. Unique within the company."
                    type: string
                }
                description {
                    description: "Project description"
                    type: string
                }
                tags {
                    description: "User-defined tags list"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "System tags list. This field is reserved for system use, please don't use it."
                    type: array
                    items { type: string }
                }
                default_output_destination {
                    description: "The default output destination URL for new tasks under this project"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of projects updated (0 or 1)"
                    type: integer
                    enum: [ 0, 1 ]
                }
                fields {
                    description: "Updated fields names and values"
                    type: object
                    additionalProperties: true
                }
            }
        }
    }
}
move {
    "2.13" {
        description: "Moves a project and all of its subprojects under the different location"
        request {
            type: object
            required: [project]
            properties {
                project {
                    description: "Project id"
                    type: string
                }
                new_location {
                    description: "The name location for the project"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                moved {
                    description: "The number of projects moved"
                    type: integer
                }
            }
        }
    }
}
merge {
    "2.13" {
        description: "Moves all the source project's contents to the destination project and remove the source project"
        request {
            type: object
            required: [project]
            properties {
                project {
                    description: "Project id"
                    type: string
                }
                destination_project {
                    description: "The ID of the destination project"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                moved_entities {
                    description: "The number of tasks and models moved from the merged project into the destination"
                    type: integer
                }
                moved_projects {
                    description: "The number of child projects moved from the merged project into the destination"
                    type: integer
                }
            }
        }
    }
}
validate_delete {
    "2.14" {
        description: "Validates that the project existis and can be deleted"
        request {
            type: object
            required: [ project ]
            properties {
                project {
                    description: "Project ID"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                tasks {
                    description: "The total number of tasks under the project and all its children"
                    type: integer
                }
                non_archived_tasks {
                    description: "The total number of non-archived tasks under the project and all its children"
                    type: integer
                }
                models {
                    description: "The total number of models under the project and all its children"
                    type: integer
                }
                non_archived_models {
                    description: "The total number of non-archived models under the project and all its children"
                    type: integer
                }
            }
        }
    }
    "2.26": ${validate_delete."2.14"} {
        response.properties {
            reports {
                description: "The total number of reports under the project and all its children"
                type: integer
            }
            non_archived_reports {
                description: "The total number of non-archived reports under the project and all its children"
                type: integer
            }
            pipelines {
                description: "The total number of pipelines with active controllers under the project and all its children"
                type: integer
            }
            datasets {
                description: "The total number of non-empty datasets under the project and all its children"
                type: integer
            }
        }
    }
}
delete {
    "2.1" {
        description: "Deletes a project"
        request {
            type: object
            required: [ project ]
            properties {
                project {
                    description: "Project ID"
                    type: string
                }
                force {
                    description: """If not true, fails if project has tasks.
                    If true, and project has tasks, they will be unassigned"""
                    type: boolean
                    default: false
                }
            }
        }
        response {
            type: object
            properties {
                deleted {
                    description: "Number of projects deleted (0 or 1)"
                    type: integer
                }
                disassociated_tasks {
                    description: "Number of tasks disassociated from the deleted project"
                    type: integer
                }
            }
        }
    }
    "2.13": ${delete."2.1"} {
        request {
            properties {
                delete_contents {
                    description: "If set to 'true' then the project tasks and models will be deleted. Otherwise their project property will be unassigned. Default value is 'false'"
                    type: boolean
                }
            }
        }
        response {
            properties {
                urls {
                    description: "The urls of the files that were uploaded by the project tasks and models. Returned if the 'delete_contents' was set to 'true'"
                    "$ref": "#/definitions/urls"
                }
                deleted_models {
                    description: "Number of models deleted"
                    type: integer
                }
                deleted_tasks {
                    description: "Number of tasks deleted"
                    type: integer
                }
            }
        }
    }
    "2.26": ${delete."2.13"} {
        request.properties.delete_external_artifacts {
            description: "If set to 'true' then BE will try to delete the extenal artifacts associated with the project tasks and models from the fileserver (if configured to do so)"
            type: boolean
            default: true
        }
    }
}
get_unique_metric_variants {
    "2.1" {
        description: """Get all metric/variant pairs reported for tasks in a specific project.
        If no project is specified, metrics/variant paris reported for all tasks will be returned.
        If the project does not exist, an empty list will be returned."""
        request {
            type: object
            properties {
                project {
                    description: "Project ID"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                metrics {
                    description: "A list of metric variants reported for tasks in this project"
                    type: array
                    items { "$ref": "#/definitions/metric_variant_result" }
                }
            }
        }
    }
    "2.13": ${get_unique_metric_variants."2.1"} {
        request {
            properties {
                include_subprojects {
                    description: "If set to 'true' and the project field is set then the result includes metrics/variants from the subproject tasks"
                    type: boolean
                    default: true
                }
            }
        }
    }
    "2.25": ${get_unique_metric_variants."2.13"} {
        request.properties.model_metrics {
            description: If set to true then bring unique metric and variant names from the project models otherwise from the project tasks
            type: boolean
            default: false
        }
    }
    "2.28": ${get_unique_metric_variants."2.25"} {
        request.properties.ids {
            description: IDs of the tasks or models to get metrics from
            type: array
            items {type: string}
        }
    }
}
get_hyperparam_values {
    "2.13" {
        description: """Get a list of distinct values for the chosen hyperparameter"""
        request {
            type: object
            required: [section, name]
            properties {
                projects {
                    description: "Project IDs"
                    type: array
                    items {type: string}
                }
                section {
                    description: "Hyperparameter section name"
                    type: string
                }
                name {
                    description: "Hyperparameter name"
                    type: string
                }
                allow_public {
                    description: "If set to 'true' then collect values from both company and public tasks otherwise company tasks only. The default is 'true'"
                    type: boolean
                }
                include_subprojects {
                    description: "If set to 'true' and the project field is set then the result includes hyper parameters values from the subproject tasks"
                    type: boolean
                    default: true
                }
            }
        }
        response {
            type: object
            properties {
                total {
                    description: "Total number of distinct parameter values"
                    type: integer
                }
                values {
                    description: "The list of the unique values for the parameter"
                    type: array
                    items {type: string}
                }
            }
        }
    }
    "2.26": ${get_hyperparam_values."2.13"} {
        request.properties {
            page {
                description: "Page number"
                default: 0
                type: integer
            }
            page_size {
                description: "Page size"
                default: 500
                type: integer
            }
        }
    }
    "2.27": ${get_hyperparam_values."2.26"} {
        request.properties.pattern {
            type: string
            description: The search pattern regex
        }
    }
}
get_hyper_parameters {
    "2.9" {
        description: """Get a list of all hyper parameter sections and names used in tasks within the given project."""
        request {
            type: object
            required: [project]
            properties {
                project {
                    description: "Project ID"
                    type: string
                }
                page {
                    description: "Page number"
                    default: 0
                    type: integer
                }
                page_size {
                    description: "Page size"
                    default: 500
                    type: integer
                }
            }
        }
        response {
            type: object
            properties {
                parameters {
                    description: "A list of parameter sections and names"
                    type: array
                    items {type: object}
                }
                remaining {
                    description: "Remaining results"
                    type: integer
                }
                total {
                    description: "Total number of results"
                    type: integer
                }
            }
        }
    }
    "2.13": ${get_hyper_parameters."2.9"} {
        request {
            properties {
                include_subprojects {
                    description: "If set to 'true' and the project field is set then the result includes hyper parameters from the subproject tasks"
                    type: boolean
                    default: true
                }
            }
        }
    }
}
get_model_metadata_values {
    "2.17" {
        description: """Get a list of distinct values for the chosen model metadata key"""
        request {
            type: object
            required: [key]
            properties {
                projects {
                    description: "Project IDs"
                    type: array
                    items {type: string}
                }
                key {
                    description: "Metadata key"
                    type: string
                }
                allow_public {
                    description: "If set to 'true' then collect values from both company and public models otherwise company modeels only. The default is 'true'"
                    type: boolean
                }
                include_subprojects {
                    description: "If set to 'true' and the project field is set then the result includes metadata values from the subproject models"
                    type: boolean
                    default: true
                }
            }
        }
        response {
            type: object
            properties {
                total {
                    description: "Total number of distinct values"
                    type: integer
                }
                values {
                    description: "The list of the unique values"
                    type: array
                    items {type: string}
                }
            }
        }
    }
    "2.26": ${get_model_metadata_values."2.17"} {
        request.properties {
            page {
                description: "Page number"
                default: 0
                type: integer
            }
            page_size {
                description: "Page size"
                default: 500
                type: integer
            }
        }
    }
}
get_model_metadata_keys {
    "2.17" {
        description: """Get a list of all metadata keys used in models within the given project."""
        request {
            type: object
            required: [project]
            properties {
                project {
                    description: "Project ID"
                    type: string
                }
                include_subprojects {
                    description: "If set to 'true' and the project field is set then the result includes metadate keys from the subproject models"
                    type: boolean
                    default: true
                }

                page {
                    description: "Page number"
                    default: 0
                    type: integer
                }
                page_size {
                    description: "Page size"
                    default: 500
                    type: integer
                }
            }
        }
        response {
            type: object
            properties {
                keys {
                    description: "A list of model keys"
                    type: array
                    items {type: string}
                }
                remaining {
                    description: "Remaining results"
                    type: integer
                }
                total {
                    description: "Total number of results"
                    type: integer
                }
            }
        }
    }
}
get_project_tags {
    "2.17" {
        description: "Get user and system tags used for the specified projects and their children"
        request = ${_definitions.tags_request}
        response = ${_definitions.tags_response}
    }
}
get_task_tags {
    "2.8" {
        description: "Get user and system tags used for the tasks under the specified projects"
        request = ${_definitions.tags_request}
        response = ${_definitions.tags_response}
    }
}
get_model_tags {
    "2.8" {
        description: "Get user and system tags used for the models under the specified projects"
        request = ${_definitions.tags_request}
        response = ${_definitions.tags_response}
    }
}

make_public {
    "2.9" {
        description: """Convert company projects to public"""
        request {
            type: object
            properties {
                ids {
                    description: "Ids of the projects to convert"
                    type: array
                    items { type: string}
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of projects updated"
                    type: integer
                }
            }
        }
    }
}

make_private {
    "2.9" {
        description: """Convert public projects to private"""
        request {
            type: object
            properties {
                ids {
                    description: "Ids of the projects to convert. Only the projects originated by the company can be converted"
                    type: array
                    items { type: string}
                }
            }
        }
        response {
            type: object
            properties {
                updated {
                    description: "Number of projects updated"
                    type: integer
                }
            }
        }
    }
}
get_task_parents {
    "2.12" {
        description: "Get unique parent tasks for the tasks in the specified projects"
        request {
            type: object
            properties {
                projects {
                    description: "The list of projects which task parents are retieved. If not passed or empty then all the projects are searched"
                    type: array
                    items { type: string }
                }
                tasks_state {
                    description: "Return parents for tasks in the specified state. If Null is provided, parents for all task states will be returned."
                    type: string
                    enum: [ active, archived ]
                    default: active
                }
            }
        }
        response {
            type: object
            properties {
                parents {
                    description: "The list of unique task parents sorted by their names"
                    type: array
                    items {
                        type: object
                        properties {
                            id {
                                description: "The ID of the parent task"
                                type: string
                            }
                            name {
                                description: "The name of the parent task"
                                type: string
                            }
                            project {
                                type: object
                                properties {
                                    id {
                                        description: "The ID of the parent task project"
                                        type: string
                                    }
                                    name {
                                        description: "The name of the parent task project"
                                        type: string
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    "2.13": ${get_task_parents."2.12"} {
        request {
            properties {
                include_subprojects {
                    description: "If set to 'true' and the projects field is not empty then the result includes tasks parents from the subproject tasks"
                    type: boolean
                    default: true
                }
            }
        }
    }
    "2.25": ${get_task_parents."2.13"} {
        request.properties.task_name {
            description: Task name pattern for the returned parent tasks
            type: string
        }
    }
}
get_user_names {
    "2.26" {
        description: "Get names and ids of the users who created child entitites under the passed projects"
        request {
            type: object
            properties {
                projects {
                    description: "The list of projects. If not passed or empty then all the projects are searched"
                    type: array
                    items { type: string }
                }
                include_subprojects {
                    description: "If set to 'true' and the projects field is not empty then the result includes user name from the subprojects children"
                    type: boolean
                    default: true
                }
                entity {
                    description: The type of the child entity to look for
                    type: string
                    enum: [task, model]
                    default: task
                }
            }
        }
        response {
            type: object
            properties {
                users {
                    description: "The list of users sorted by their names"
                    type: array
                    items {
                        type: object
                        properties {
                            id {
                                description: "The ID of the user"
                                type: string
                            }
                            name {
                                description: "The name of the user"
                                type: string
                            }
                        }
                    }
                }
            }
        }
    }
}
get_all_template {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_history_data {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_live_data {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_mock_data {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_satellite {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_ground_station {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_addon {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_addon_error {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_run_error {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_max_access_time {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_satellite_type {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_ground_station_type{
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_constraint{
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_evolution{
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_evaluation {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
statistics{
    "1.0" {
        description: "statistics count"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                statistics {
                    description: "statistics count"
                    type: object
                }  
            }
        }
    }
}
get_algo {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_algo_version {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_algo_params {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}

start {
    "2.9" {
        description: """Start projects tasks."""
        request {
            type: object
            required: [project]
            properties {
                project {
                    description: "Project ID"
                    type: string
                }
            }
        }
        response {
            type: object
        }
    }
}

stop {
    "2.9" {
        description: """Stop projects tasks."""
        request {
            type: object
            required: [project]
            properties {
                project {
                    description: "Project ID"
                    type: string
                }
            }
        }
        response {
            type: object
        }
    }
}

get_run {
    "2.1" {
        description: ""
        request {
            type: object
            required: [ project ]
            properties {
                project {
                    description: "Project id"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                project {
                    description: "Project info"
                    "$ref": "#/definitions/project"
                }
            }
        }
    }
}
get_all_task {
    "2.1" {
        description: "Get all the company's tasks and all public tasks"
        request {
            type: object
            properties {
                id {
                    description: "List of IDs to filter by"
                    type: array
                    items { type: string }
                }
                name {
                    description: "Get only tasks whose name matches this pattern (python regular expression syntax)"
                    type: string
                }
                user {
                    description: "List of user IDs used to filter results by the task's creating user"
                    type: array
                    items { type: string }
                }
                project {
                    type: array
                    items { type: string }
                }
                page {
                    description: "Page number, returns a specific page out of the resulting list of tasks"
                    type: integer
                    minimum: 0
                }
                page_size {
                    description: "Page size, specifies the number of results returned in each page (last page may contain fewer results)"
                    type: integer
                    minimum: 1
                }
                order_by {
                    description: """List of field names to order by. When search_text is used,
                    '@text_score' can be used as a field representing the text score of returned documents.
                    Use '-' prefix to specify descending order. Optional, recommended when using page.
                    If the first order field is a hyper parameter or metric then string values are ordered
                    according to numeric ordering rules where applicable"""
                    type: array
                    items { type: string }
                }
                type {
                    description: "List of task types. One or more of: 'import', 'annotation', 'training' or 'testing' (case insensitive)"
                    type: array
                    items { type: string }
                }
                tags {
                    description: "List of task user-defined tags. Use '-' prefix to exclude tags"
                    type: array
                    items { type: string }
                }
                system_tags {
                    description: "List of task system tags. Use '-' prefix to exclude system tags"
                    type: array
                    items { type: string }
                }
                status {
                    description: "List of task status."
                    type: array
                    items { "$ref": "#/definitions/task_status_enum" }
                }
                project {
                    description: "List of project IDs"
                    type: array
                    items { type: string }
                }
                only_fields {
                    description: "List of task field names (nesting is supported using '.', e.g. execution.model_labels). If provided, this list defines the query's projection (only these fields will be returned for each result entry)"
                    type: array
                    items { type: string }
                }
                parent {
                    description: "Parent ID"
                    type: string
                }
                status_changed {
                    description: "List of status changed constraint strings (utcformat, epoch) with an optional prefix modifier (>, >=, <, <=)"
                    type: array
                    items {
                        type: string
                        pattern: "^(>=|>|<=|<)?.*$"
                    }
                }
                search_text {
                    description: "Free text search query"
                    type: string
                }
                _all_ {
                    description: "Multi-field pattern condition (all fields match pattern)"
                    "$ref": "#/definitions/multi_field_pattern_data"
                }
                _any_ {
                    description: "Multi-field pattern condition (any field matches pattern)"
                    "$ref": "#/definitions/multi_field_pattern_data"
                }
            }
        }
        response {
            type: object
            properties {
                tasks {
                    description: "List of tasks"
                    type: array
                    items { "$ref": "#/definitions/task" }
                }
            }
        }
    }
}
get_task_log {
    "2.9" {
        description: "Get 'log' events for this task"
        request {
            type: object
            required: [
                task
            ]
            properties {
                task {
                    type: string
                    description: "Task ID"
                }
                batch_size {
                    type: integer
                    description: "The amount of log events to return"
                }
                navigate_earlier {
                    type: boolean
                    description: "If set then log events are retreived from the latest to the earliest ones (in timestamp descending order, unless order='asc'). Otherwise from the earliest to the latest ones (in timestamp ascending order, unless order='desc'). The default is True"
                }
                from_timestamp {
                    type: number
                    description: "Epoch time in UTC ms to use as the navigation start. Optional. If not provided, reference timestamp is determined by the 'navigate_earlier' parameter (if true, reference timestamp is the last timestamp and if false, reference timestamp is the first timestamp)"
                }
                order {
                    type: string
                    description: "If set, changes the order in which log events are returned based on the value of 'navigate_earlier'"
                    enum: [asc, desc]
                }
            }
        }
        response {
            type: object
            properties {
                events {
                    type: array
                    items { type: object }
                    description: "Log items list"
                }
                returned {
                    type: integer
                    description: "Number of log events returned"
                }
                total {
                    type: number
                    description: "Total number of log events available for this query. In case there are more than 10000 events it is set to 10000"
                }
            }
        }
    }
}
save_project {
    "2.1" {
        description: ""
        request {
            type: object
            required: [ project ]
            properties {
                project {
                    description: "Project id"
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                algo {
                    description: "algo id"
                    type: string
                }
            }
        }
    }
}

start {
    "2.9" {
        description: """Start projects tasks."""
        request {
            type: object
            required: [project]
            properties {
                project {
                    description: "Project ID"
                    type: string
                }
            }
        }
        response {
            type: object
        }
    }
}

stop {
    "2.9" {
        description: """Stop projects tasks."""
        request {
            type: object
            required: [project]
            properties {
                project {
                    description: "Project ID"
                    type: string
                }
            }
        }
        response {
            type: object
        }
    }
}

get_algorithms {
    "2.9" {
        description: """ projects get_algorithms."""
        request {
            type: object
            required: [project]
            properties {
                project {
                    description: "Project ID"
                    type: string
                }
            }
        }
        response {
            type: object
        }
    }
}

get_param_list {
    "2.9" {
        description: """ projects get_param_list."""
        request {
            type: object
            required: [project]
            properties {
                project {
                    description: "Project ID"
                    type: string
                }
            }
        }
        response {
            type: object
        }
    }
}
get_all_algo {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_all_algo_release {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                id {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
get_dataset_by_project {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                project {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
update_best_algo_release {
    internal: false
    "2.1" {
        description: Gets mock history data
        request {
            type: object
            properties {
                project {
                    description: mock ID
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: mock history data
                    type: array
                }
            }
        }
    }
}
task_statistics{
    "1.0" {
        description: "statistics count"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                statistics {
                    description: "statistics count"
                    type: object
                }  
            }
        }
    }
}
algo_statistics{
    "1.0" {
        description: "statistics count"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                statistics {
                    description: "statistics count"
                    type: object
                }  
            }
        }
    }
}
algo_count_statistics{
    "1.0" {
        description: "statistics count"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                statistics {
                    description: "statistics count"
                    type: object
                }  
            }
        }
    }
}
train_histogram{
    "1.0" {
        description: "train histogram"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                statistics {
                    description: "train histogram"
                    type: object
                }  
            }
        }
    }
}
get_failed_projects{
    "2.1" {
        description: get failed projects
        request {
            type: object
            properties {
                time {
                    description: time
                    type: string
                }
            }
        }
        response {
            type: object
            properties {
                list {
                    description: failed projects list
                    type: array
                }
            }
        }
    }
}

get_param_comb_types{
    "2.1" {
        description: get parameter combination types
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                list {
                    description: parameter combination types
                    type: array
                }
            }
        }
    }
}
save_to_template{
    "2.1" {
        description: get parameter combination types
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                list {
                    description: parameter combination types
                    type: array
                }
            }
        }
    }
}

status_statistics{
    "1.0" {
        description: "statistics count"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {
                statistics {
                    description: "statistics count"
                    type: object
                }  
            }
        }
    }
}

live_data_update{
    "1.0" {
        description: "live data update project"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {  
            }
        }
    }
}

execute_times{
    "1.0" {
        description: "project execute times"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {  
            }
        }
    }
}

tasks_detail{
    "1.0" {
        description: "project tasks detail"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {  
            }
        }
    }
}

get_best_task{
    "1.0" {
        description: "project get best task"
        request {
            type: object
            properties {
            }
        }
        response {
            type: object
            properties {  
            }
        }
    }
}