// some definitions
definitions {
    // description of a reference
    reference {
        type: object
        additionalProperties: false
        required: [ "$ref" ]
        properties {
            "$ref" { type: string }
        }
    }
    // description of an "additionalProperties" section
    additional_properties {
        oneOf: [
            { type: object, additionalProperties: true },
            { type: boolean }
        ]
    }
    // each endpoint is a mapping of versions to actions
    endpoint {
        type: object
        additionalProperties: false
        properties {

            // whether endpoint is internal
            internal { type: boolean }
            // whether endpoint requires authorization
            authorize { type: boolean }
            // list of roles allowed to access endpoint
            allow_roles {
                type: array
                items { type: string }
            }
        }
        patternProperties {
            "^\d\.\d+$" { "$ref": "#/definitions/action" }
        }
    }
    // an action describes request and response
    action {
        type: object
        additionalProperties: false
        // must have response
        required: [ response, description ]
        // must have either request or batch_request
        oneOf: [
            { required: [ request ] }
            { required: [ batch_request ] }
        ]
        properties {
            method { const: post }
            description { type: string }
            request {
                oneOf: [
                    { "$ref": "#/definitions/reference" }
                    { "$ref": "#/definitions/request" }
                    { "$ref": "#/definitions/multi_request" }
                ]
            }
            response {
                type: object
                oneOf: [
                    { "$ref": "#/definitions/response" }
                    // { "$ref": "#/definitions/reference" }
                    {
                        type: object
                        properties {
                            type { const: string }
                        }
                        additionalProperties: false
                    }
                ]
            }
            batch_request {
                type: object
                additionalProperties: false
                // required: [ action, version, description ]
                required: [ action, version ]
                properties {
                    action { type: string }
                    version { type: number }
                    // description { type: string }
                }
            }
        }
    }
    // describes request to server
    request {
        type: object
        additionalProperties: false
        required: [
            type
        ]
        properties {
            // says it's an object
            type { const: object }
            // required fields
            required {
                type: array
                items { type: string }
            }
            // request fields,
            // an object that can have anything
            properties {
                type: object
                additionalProperties {
                    type: object
                    required: [ description ]
                    properties {
                        description { type: string }
                    }
                    additionalProperties: true
                }
            }
            dependencies { type: object }
            // can have an "additionalProperties" section
            additionalProperties { "$ref": "#/definitions/additional_properties" }
        }
    }
    multi_request {
        type: object
        required: [ type ]
        additionalProperties: false
        oneOf: [
            {
                required: [ anyOf ]
            }
            {
                required: [ oneOf ]
            }
        ]
        properties {
            type { const: object }
            anyOf {
                type: array
                items { "$ref": "#/definitions/reference" }
            }
            oneOf {
                type: array
                items { "$ref": "#/definitions/reference" }
            }
        }
    }
    response {
        type: object
        additionalProperties: false
        required: [
            type
        ]
        properties {
            // says it's an object
            type { const: object }
            // nothing is required
            // can have anything
            properties {
                type: object
                additionalProperties: true
            }
            // can have an "additionalProperties" section
            additionalProperties { "$ref": "#/definitions/additional_properties" }
        }
    }
}

// schema starts here!
type: object
required: [ _description ]
properties {
    _description { type: string }
    // definitions for generator
    _definitions {
        type: object
        additionalProperties {
            required: [ type ]
            properties {
                type { type: string }
            }
            // can have anything
            additionalProperties: true
        }
    }
    _references = ${properties._definitions}
    // default values for actions
    // can have anything
    _default {
        additionalProperties: true
    }
}
// describing each endpoint
additionalProperties {
    type: object
    // can be:
    oneOf: [
        // a reference
        { "$ref": "#/definitions/reference" }
        // or a mapping from versions to actions
        { "$ref": "#/definitions/endpoint" }
    ]
}
