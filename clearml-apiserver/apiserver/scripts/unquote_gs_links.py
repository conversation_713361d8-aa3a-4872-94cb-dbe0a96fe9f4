import argparse
import json
import urllib.parse
from argparse import Argument<PERSON><PERSON>er
from bson import json_util
from typing import Sequence

from pymongo import MongoClient
from pymongo.database import Database, Collection


processed_models = set()


def _process_task_models(task: dict, models: Collection, dry_run: bool, backup: bool):
    task_models = task.get("models")

    if not task_models:
        return

    model_ids = {
        model_entry["model"]
        for field in ("input", "output")
        if task_models.get(field)
        for model_entry in task_models.get(field)
    }
    model_ids.difference_update(processed_models)
    if not model_ids:
        return

    processed_models.update(model_ids)
    for model in models.find({"_id": {"$in": list(model_ids)}}):
        uri = model.get("uri")
        if not (uri and uri.startswith("gs://")):
            continue
        fixed = urllib.parse.unquote(uri)
        if uri == fixed:
            continue
        _id = model["_id"]
        print(f"model {_id}\t{uri}  ---->  {fixed}")
        if backup:
            with open(f"model_{_id}", "w") as fp:
                json.dump(model, fp, default=json_util.default)
        if not dry_run:
            models.update_one({"_id": _id}, {"$set": {"uri": fixed}})
            if backup:
                with open(f"model_{_id}_modified", "w") as fp:
                    updated = models.find_one({"_id": _id})
                    json.dump(updated, fp, default=json_util.default)


def fix_models(mongo: MongoClient, task_ids: Sequence[str], dry_run: bool, backup=True):
    if dry_run:
        print("Running in dry run mode")
    else:
        print("Fixing urls")

    db: Database = mongo.get_database("backend")
    tasks: Collection = db.get_collection("task")
    models: Collection = db.get_collection("model")
    for task in tasks.find({"_id": {"$in": task_ids}}):
        task_id = task["_id"]
        print(f"Processing task {task_id}")
        _process_task_models(task, models, dry_run=dry_run, backup=backup)

        artifacts = task.get("execution", {}).get("artifacts", {})
        if not artifacts:
            continue

        updates = {}
        for key, value in artifacts.items():
            uri = value.get("uri")
            if not (uri and uri.startswith("gs://")):
                continue
            fixed = urllib.parse.unquote(uri)
            if uri == fixed:
                continue
            path = f"execution.artifacts.{key}.uri"
            print(f"{path}\t{uri}  ---->  {fixed}")
            updates[path] = fixed

        if not updates:
            continue

        if backup:
            with open(f"task_{task_id}", "w") as fp:
                json.dump(task, fp, default=json_util.default)
        if not dry_run:
            tasks.update_one({"_id": task_id}, {"$set": updates})
            if backup:
                with open(f"task_{task_id}_modified", "w") as fp:
                    updated = tasks.find_one({"_id": task_id})
                    json.dump(updated, fp, default=json_util.default)


def main():
    parser = ArgumentParser(description=__doc__)
    parser.add_argument(
        "--mongo-host",
        "-mh",
        help="Mongo db host address",
        type=str,
        default="mongo:27017",
    )
    parser.add_argument(
        "--mongo-user",
        "-mu",
        help="Mongo db user",
        type=str,
        default=None,
    )
    parser.add_argument(
        "--mongo-password",
        "-mp",
        help="Mongo db password",
        type=str,
        default=None,
    )
    parser.add_argument(
        "--task-ids",
        "-t",
        help="Ids of the tasks to fix models for",
        nargs="+",
        type=str,
    )
    parser.add_argument(
        "--ids-file",
        "-i",
        type=argparse.FileType("r"),
        default=False,
        help="File with task ids",
    )
    parser.add_argument(
        "--fix-data",
        "-f",
        action="store_true",
        default=False,
        help="When set then data is change. Otherwise runs in dry run mode",
    )
    parser.add_argument(
        "--no-backup",
        "-nb",
        action="store_true",
        default=False,
        help="When set no data backup is done",
    )
    args = parser.parse_args()

    mongo = MongoClient(args.mongo_host)
    ids = set()
    if args.task_ids:
        ids.update(args.task_ids)
    if args.ids_file:
        ids.update(line.strip() for line in args.ids_file.readlines())
    if not ids:
        parser.error("No task ids provided")

    fix_models(
        mongo, task_ids=list(ids), dry_run=not args.fix_data, backup=not args.no_backup
    )


if __name__ == "__main__":
    main()
