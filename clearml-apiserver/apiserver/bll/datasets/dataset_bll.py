import operator
from collections import defaultdict
from datetime import datetime, timedelta
from functools import reduce
from itertools import groupby, chain
from operator import itemgetter
from typing import (
    Sequence,
    Optional,
    Type,
    Tuple,
    Dict,
    Set,
    TypeVar,
    Callable,
    Mapping,
    Any,
    Union,
)

from mongoengine import Q, Document

from apiserver import database
from apiserver.apierrors import errors
from apiserver.apimodels.projects import ProjectChildrenType
from apiserver.config_repo import config
from apiserver.database.model import EntityVisibility, AttributedDocument, User
from apiserver.database.model.base import GetMixin
from apiserver.database.model.model import Model
from apiserver.database.model.dataset import (
    Dataset, DatasetType, Is_publish,
    Datasets, DatasetsKind, DatasetsVersion, VersionFiles
)
from apiserver.database.model.task.task import Task, TaskStatus, external_task_types
from apiserver.database.utils import get_options, get_company_or_none_constraint
from apiserver.utilities.dicts import nested_get
from apiserver.database.model.project import Project
from apiserver.database.errors import translate_errors_context
from apiserver.utilities.page_query import PageQuery
from mongoengine import Q
from pymongo import DESCENDING, ASCENDING


class DatasetBLL:


    @classmethod
    def create(
        cls,
        user: str,
        name: str,
        company: str,
        dataset_type: str,
        is_public: str,
        description: str = "",
    ) -> str:
        try:
            DatasetType(dataset_type)
        except ValueError:
            raise errors.bad_request.InvalidDatasetType()
        try:
            Is_publish(is_public)
        except ValueError:
            raise errors.bad_request.InvalidDatasetPublish()
        now = datetime.utcnow()
        dataset = Dataset(
            id = database.utils.id(),
            user = user,
            name = name,
            company=company,
            description = description,
            created = now,
            last_update = now,
            dataset_type = dataset_type,
            is_public = is_public,
        )

        saved = dataset.save()
        # print(saved)
        return dataset.id

    @classmethod
    def get_id_by_name(
        cls,
        dataset_name: str,
    ) -> str:

        if not dataset_name:
            raise errors.bad_request.ValidationError("dataset name required")
        dataset = Dataset.objects(name=dataset_name).only("id").first()
        print(dataset.id)
        if dataset:
            return dataset.id
        else:
            raise errors.bad_request.ValidationError("dataset does not exist")

    @classmethod
    def update(
        cls,
        company: str,
        dataset_id: str,
        **fileds
    ):
        dataset = Dataset.get_for_writing(company=company,id=dataset_id)
        if not dataset:
            raise errors.bad_request.InvalidDatasetId(id=dataset_id)
        update_fields = {}
        if "description" in fileds:
            update_fields["description"] = fileds.pop("description")
        if "is_public" in fileds:
            update_fields["is_public"] = fileds.pop("is_public")
        update_fields["last_update"] = datetime.utcnow()
        update_fields.update(fileds)
        updated = dataset.update(upsert=False, **update_fields)
        return updated

    @classmethod
    def delete(cls,dataset_id:str):
        with translate_errors_context("deleting dataset"):
            res = Dataset.objects(id=dataset_id).delete()
            if not res:
                raise errors.bad_request.InvalidDatasetId(id=dataset_id)
            return res


class DatasetsBLL:

    @classmethod
    def create(cls,
               id: str, name: str, user_id: str, description: str, kind: str, size: str, base_id: int,
               algorithm_id: int, algorithm_name: str
               ) -> str:
        """创建一条文件上传记录"""
        dateset = Datasets(
            id=id, name=name, user_id=user_id, description=description, kind=kind, size=size,
            base_id=base_id, algorithm_id=algorithm_id, algorithm_name=algorithm_name
        )
        dateset.save()
        return dateset.id

    @classmethod
    def list(cls, **kwargs):

        conditions = []
        query = {}
        # 更新查询条件
        if kwargs.get('name'):
            conditions.append(Q(name__icontains=kwargs.get('name')))
        if kwargs.get('kind'):
            conditions.append(Q(kind=kwargs.get('kind')))
        if kwargs.get('algorithm_id'):
            conditions.append(Q(algorithm_id=kwargs.get('algorithm_id')))
        if kwargs.get('user_id'):
            conditions.append(Q(user_id=kwargs.get('user_id')))
        if conditions:
            query = reduce(operator.and_, conditions)

        # 更新排序，默认排序为 created
        sort_key = kwargs.get("sort_key", "created")
        # 默认排序为降序
        sort_direction = kwargs.get("sort_direction", DESCENDING)
        if sort_direction < 0:
            order_by_key = f'-{sort_key}'
        else:
            order_by_key = f'+{sort_key}'

        # 设置显示的字段
        fields = [
            Datasets.id.name, Datasets.name.name, Datasets.kind.name, Datasets.description.name, Datasets.algorithm_name.name, Datasets.size.name,
            "version_num"
        ]

        return PageQuery.query(Datasets, query, fields, order_by_key, **kwargs)

    @classmethod
    def get_by_id(cls, id: str):
        fields = [Datasets.name.name, Datasets.kind.name,  Datasets.algorithm_name.name, Datasets.updated.name]
        return PageQuery.result(Datasets.objects(id=id).first(), fields)


class DatasetsVersionBLL:

    @classmethod
    def create(cls,
               id: str, version: str, dataset_id: int
               ) -> str:
        """创建一条文件上传记录"""
        dateset = DatasetsVersion(
            id=id, version=version, dataset_id=dataset_id
        )
        dateset.save()
        return dateset.id

    @classmethod
    def list(cls, dataset_id: str):
        data = DatasetsVersion.objects(dataset_id=dataset_id).all()
        fields = [DatasetsVersion.id.name, DatasetsVersion.version.name]
        results = PageQuery.results(data, fields)
        return sorted(results, key=lambda v: tuple(map(int, v[DatasetsVersion.version.name].split('.'))), reverse=True)


class VersionFilesBLL:

    @classmethod
    def create(cls,
               id: str, version_id: str, filename: int, filesize: str
               ) -> str:
        """创建一条文件上传记录"""
        version_file = VersionFiles(
            id=id, version_id=version_id, filename=filename, filesize=filesize
        )
        version_file.save()
        return version_file.id

    @classmethod
    def list(cls, version_id, **kwargs):
        """获取版本对应的文件"""
        query = Q(version_id=version_id)
        fields = [VersionFiles.filename.name, VersionFiles.filesize.name]
        order_by_key = f'-{VersionFiles.created.name}'
        return PageQuery.query(VersionFiles, query, fields, order_by_key, **kwargs)