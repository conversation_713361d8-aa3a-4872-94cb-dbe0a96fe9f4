from datetime import datetime

from apiserver import database
from apiserver.apierrors import errors
from apiserver.database.model.algorithm import (
    Algorithm,
    AlgorithmType,
    Is_publish,
    ModelFormat,
)


class AlgorithmBLL:

    @classmethod
    def create(
        cls,
        user: str,
        name: str,
        company: str,
        algorithm_type: str,
        is_public: str,
        is_inference: bool,
        model_format: str,
        image: str,
        description: str = "",
    ) -> str:
        try:
            AlgorithmType(algorithm_type)
        except ValueError:
            raise errors.bad_request.InvalidDatasetType()
        try:
            ModelFormat(model_format)
        except ValueError:
            raise errors.bad_request.InvalidDatasetType()
        try:
            Is_publish(is_public)
        except ValueError:
            raise errors.bad_request.InvalidDatasetPublish()
        now = datetime.utcnow()
        algorithm = Algorithm(
            id=database.utils.id(),
            user=user,
            name=name,
            company=company,
            description=description,
            created=now,
            model_format=model_format,
            last_update=now,
            algorithm_type=algorithm_type,
            is_public=is_public,
            is_inference=is_inference,
            image=image,
        )
        algorithm.save()
        return algorithm.id

    @classmethod
    def update(cls, company: str, algorithm_id: str, **fileds):
        algo = Algorithm.get_for_writing(company=company, id=algorithm_id)
        if not algo:
            raise errors.bad_request.InvalidDatasetId(id=algorithm_id)
        update_fields = {}
        if "description" in fileds:
            update_fields["description"] = fileds.pop("description")
        if "is_public" in fileds:
            update_fields["is_public"] = fileds.pop("is_public")
        update_fields["last_update"] = datetime.utcnow()
        print(update_fields)
        update_fields.update(fileds)
        updated = algo.update(upsert=False, **update_fields)
        return updated

    @classmethod
    def delete(cls, algorithm_id: str):
        res = Algorithm.objects(id=algorithm_id).delete()
        if not res:
            raise errors.bad_request.InvalidDatasetId(id=algorithm_id)
        return res

    @classmethod
    def update_mongo(cls, company: str, algorithm_id: str, size: str, version_num: int):
        algo = Algorithm.get_for_writing(company=company, id=algorithm_id)
        update_fields = {}
        update_fields["size"] = size
        update_fields["versions_num"] = version_num
        update_fields["last_update"] = datetime.utcnow()
        algo.update(upsert=False, **update_fields)
