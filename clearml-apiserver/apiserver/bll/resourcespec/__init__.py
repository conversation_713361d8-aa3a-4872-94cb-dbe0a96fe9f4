from apiserver.database.model.resourcespec import ResourceSpec
from apiserver import database
from typing import Sequence, Optional, Tuple, Union
from apiserver.database.errors import translate_errors_context

class ResourceSpecBLL(object):
    @classmethod
    def create(
        self,
        company_id: str,
        user: str,
        name: str,
        cpu: int,
        mem: int,
    ) -> ResourceSpec:
        with translate_errors_context():
            resource_spec = ResourceSpec(
                id = database.utils.id(),
                company=company_id,
                user=user,
                name=name,
                cpu=cpu,
                mem=mem,
            )
            resource_spec.save()
            return resource_spec.id
    @classmethod
    def get_by_id(self, resource_id: str, company_id: str) -> ResourceSpec:
        with translate_errors_context():
            query = dict(id=resource_id, company=company_id)
            resource_spec = ResourceSpec.objects(**query).first()
            return resource_spec
    
    @classmethod
    def get_by_name(self, name: str, company_id: str) -> ResourceSpec:
        with translate_errors_context():
            query = dict(name=name, company=company_id)
            resource_spec = ResourceSpec.objects(**query).first()
            return resource_spec
    
    @classmethod
    def get_all(self,company_id: str) -> Sequence[dict]:
         with translate_errors_context():
            return ResourceSpec.get_many(
                company=company_id,
                query={}
            )