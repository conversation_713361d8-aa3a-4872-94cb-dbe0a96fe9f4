
from apiserver.database.model.ide import Ide, IdeStatus, Is_publish
from apiserver.database.errors import translate_errors_context
from apiserver import database
from apiserver.config_repo import config
from apiserver.apierrors import errors
from datetime import datetime
from typing import Sequence, Optional, Tuple, Union

log = config.logger(__file__)

class IdeBLL(object):
    @classmethod
    def create(
        self,
        company_id: str,
        user: str,
        name: str,
        description: str,
        cpu: int,
        mem: int, 
        gpu: int,
        algos,
        image: str,
        is_public: str,
        resourceId:int,
        dataset_path: str
    ) -> Ide:
         try:
            Is_publish(is_public)
         except ValueError:
            raise errors.bad_request.InvalidIdePublish(is_public=is_public)
         with translate_errors_context():
              now = datetime.utcnow()
              ide = Ide(
                    id = database.utils.id(),
                    company=company_id,
                    user=user,
                    name = name,
                    cpu = cpu,
                    mem = mem,
                    gpu=gpu,
                    algorithms = algos,
                    created = now,
                    image = image,
                    description = description,
                    last_update = datetime.utcnow(),
                    is_public = is_public,
                    resourceId = resourceId,
                    dataset_path = dataset_path
              )
              return ide.save()

    @classmethod
    def get_by_id(self, ide_id: str, company_id: str) -> Ide:
         with translate_errors_context():
            query = dict(id=ide_id, company=company_id)
            ide = Ide.objects(**query).first()
            if not ide:
                raise errors.bad_request.IdeNotExist(**query)
            return ide

    @classmethod
    def get_by_name(self, name: str, company_id: str) -> Ide:
        with translate_errors_context():
            query = dict(name = name, company=company_id)
        ide = Ide.objects(**query).first()
        return ide

    @classmethod
    def get_by_algo(self, algo: str, company_id: str) -> Ide:
        with translate_errors_context():
            query = dict(algorithms=[algo], company=company_id)
        ide = Ide.objects(**query).first()
        return ide
         
    @classmethod
    def update_status(self, ide_id: str, company_id: str, status: str):
        updated = Ide.objects(id=ide_id, company=company_id).update(
            status=status,
        )
        return updated
    
    @classmethod
    def update(self, company: str, ide_id: str, **fields):
        with translate_errors_context():
            ide = Ide.get_for_writing(company=company,id=ide_id)
            update_fields = {}
            if "description" in fields:
                update_fields["description"] = fields.pop("description")
            if "is_public" in fields:
                update_fields["is_public"] = fields.pop("is_public")
            update_fields["last_update"] = datetime.utcnow()
            update_fields.update(fields)
            updated = ide.update(upsert=False, **update_fields)    
            Ide.safe_update(company_id=company, id=ide_id,partial_update_dict=fields)
        return updated
    
    @staticmethod
    def get_by_status(
        self,
        status: str,
        company_id: str
    ) -> Sequence[dict]:
        with translate_errors_context():
            query = dict(status=status)
            return Ide.get_many(
                company=company_id,
                query=query,
            )
        
    @classmethod
    def stop(
        self,
        ids: Sequence[str],
        company_id: str,
    )->int:
        update_dicts = {'status': 'stopping'}
        for id in ids:
            ide = self.get_by_id(id, company_id=company_id)
            if ide.status not in [IdeStatus.available, IdeStatus.running, IdeStatus.starting]:
                print(f'Ide {id} not running')
                continue
            Ide.safe_update(id=id, company_id=company_id, partial_update_dict=update_dicts)
        return len(ids)
    
    @classmethod
    def start(
        self,
        id: str,
        company_id: str
    ):
        ide = self.get_by_id(id,company_id=company_id)
        if ide.status in [IdeStatus.available, IdeStatus.running, IdeStatus.starting, IdeStatus.deleting]:
            # TODO raise exception
            pass
        else:
            Ide.safe_update(id=ide.id, company_id=ide.company, partial_update_dict={'status':'tostart'})
    
    @classmethod
    def delete(
        self,
        ids: Sequence[str],
        company_id: str):

        print(ids)
        succeed_count = len(ids)
        for id in ids:
            ide = self.get_by_id(id, company_id=company_id)
            try:
                if ide.status in [IdeStatus.unknown, IdeStatus.failed, IdeStatus.stopped] :
                    ide.delete()
                else:
                    # ide.status = IdeStatus.deleting
                    Ide.safe_update(id=ide.id, company_id=ide.company, partial_update_dict={'status':'deleting'})
            except Exception as ex:
                print(f'delete ide {id} failed')
                succeed_count-=1
        return succeed_count
    
    @classmethod
    def get_timeout(
        company_id: str,
    ):
        #到达自动停止时间的IDE列表
        now  = datetime.utcnow()
        kwargs = {}
        kwargs['stop_time'] = { '$lt': now}
        kwargs['status'] = {'in', [IdeStatus.running, IdeStatus.starting, IdeStatus.available]}
        return Ide.objects(**kwargs)
