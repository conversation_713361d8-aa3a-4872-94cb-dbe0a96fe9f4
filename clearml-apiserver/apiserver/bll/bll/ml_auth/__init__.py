from apiserver.config_repo import config
from apiserver.database.data_access.ml_auth.login_record_da import LoginRecordDA
from apiserver.database.data_access.ml_auth.user_da import AuthUserDA
from apiserver.database.data_access.ml_auth.session_da import SessionDA


log = config.logger(__file__)


class MLAuthBLL:

    @staticmethod
    def obj_save(login_record):
        return login_record.save()

    # 用户
    @staticmethod
    def list_user(**kwargs):
        return AuthUserDA.list(**kwargs)

    @staticmethod
    def create_user(data=None):
        return AuthUserDA.add(data=data)

    @staticmethod
    def delete_user(**kwargs):
        AuthUserDA.delete(**kwargs)

    @staticmethod
    def update_user(**kwargs):
        return AuthUserDA.update(**kwargs)

    @staticmethod
    def user_by_id(**kwargs):
        return AuthUserDA.get_by_id(**kwargs)

    @staticmethod
    def user_detail_by_id(**kwargs):
        return AuthUserDA.user_detail_by_id(**kwargs)

    @staticmethod
    def user_by_name(**kwargs):
        return AuthUserDA.user_by_name(**kwargs)

    # 会话
    @staticmethod
    def create_session(**kwargs):
        return SessionDA.add(**kwargs)

    @staticmethod
    def update_session_by_sid(**kwargs):
        return SessionDA.update_by_sid(**kwargs)

    @staticmethod
    def delete_session_by_user_id(**kwargs):
        return SessionDA.delete_by_user_id(**kwargs)

    @staticmethod
    def list_session_by_user_id(**kwargs):
        return SessionDA.list_by_user_id(**kwargs)

    @staticmethod
    def session_by_user_id(**kwargs):
        return SessionDA.get_by_user_id(**kwargs)

    @staticmethod
    def session_by_sid(**kwargs):
        return SessionDA.get_by_sid(**kwargs)

    # 登录记录
    @staticmethod
    def login_record_by_sid(**kwargs):
        return LoginRecordDA.get_by_sid(**kwargs)

    @staticmethod
    def login_record_count_by_sid(**kwargs):
        return LoginRecordDA.get_count_by_sid(**kwargs)

    @staticmethod
    def create_login_record(**kwargs):
        return LoginRecordDA.add(**kwargs)

    @staticmethod
    def delete_login_record(**kwargs):
        return LoginRecordDA.delete_by_name(**kwargs)
