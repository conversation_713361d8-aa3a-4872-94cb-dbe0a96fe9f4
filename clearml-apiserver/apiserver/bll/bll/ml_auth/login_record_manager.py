from datetime import datetime, timedelta
from apiserver.config_repo import config
from apiserver.utilities.random import random_ascii
from apiserver.bll.bll.ml_auth import MLAuthBLL


log = config.logger(__file__)


class LoginRecordManager:

    @staticmethod
    def get_login_record(sid=None):
        return MLAuthBLL.login_record_by_sid(sid=sid)

    @staticmethod
    def delete_login_record(name=None):
        return MLAuthBLL.delete_login_record(name=name)

    @staticmethod
    def save_login_record(name=None, sid=None, captcha=None, captcha_2fa=None):
        """
        更新或新建登录记录,若指定sid参数,则尝试更新
        保存登录sessionId,验证码
        """
        image_captcha_age = config.get("apiserver.auth.login.image_captcha_age")
        captcha_2fa_age = config.get("apiserver.auth.login.captcha_2fa_age")

        login_record = None
        if sid:
            login_record = MLAuthBLL.login_record_by_sid(sid=sid)

        if not login_record:
            if not sid:
                while True:
                    sid = random_ascii(128)
                    c = MLAuthBLL.login_record_count_by_sid(sid=sid)
                    if not c:
                        break
            login_record = MLAuthBLL.create_login_record(name=name, session_id=sid)

        if captcha is not None:
            login_record.captcha = captcha
            login_record.expired = datetime.utcnow() + timedelta(seconds=image_captcha_age)
        elif captcha_2fa is not None:
            login_record.captcha_2fa = captcha_2fa
            login_record.expired = datetime.utcnow() + timedelta(seconds=captcha_2fa_age)
        MLAuthBLL.obj_save(login_record)
        return login_record
