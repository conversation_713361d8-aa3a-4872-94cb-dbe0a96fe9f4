from apiserver.config_repo import config
from apiserver.bll.bll.utils.import_string_utils import import_string
from apiserver.apierrors.errors.ml_bad_request import MLError


log = config.logger(__file__)


class CommonGetAuth:

    @staticmethod
    def get_backend(call, login_backends, kw=None):
        """获取backend, 登录类型cookie处理"""

        auth_type_value = call.auth_type_value
        str_split = auth_type_value.split("/")
        if len(str_split) == 2:
            auth_type, auth_backend = str_split
        else:
            auth_type = str_split[0]
            auth_backend = None
        auth_module = config.get("apiserver.auth.auth_module")
        auth_backend_module_str = auth_module.get(auth_type, {}).get("backendModule", {}).get(auth_backend)
        if not auth_backend_module_str:
            call.result.clear_cookie(config.get("apiserver.auth.auth.auth_type"))
            raise MLError(replacement_msg='登录验证功能不存在')
        login_backend = login_backends.get(auth_type_value, None)
        if not login_backend:
            if kw:
                login_backend = import_string(auth_backend_module_str)(**kw)
            else:
                login_backend = import_string(auth_backend_module_str)()
            login_backends[auth_type_value] = login_backend
        return login_backend

    @staticmethod
    def get_login_bll(call, login_dict):
        """获取登录类型, 登录类型cookie处理"""
        auth_module = config.get("apiserver.auth.auth_module")
        auth_type_name = config.get("apiserver.auth.auth.auth_type")
        default_auth_type = config.get("apiserver.auth.default_auth_type")

        # 从header获取登录类型
        header_auth_type = call.headers.get(auth_type_name, None)
        # 从cookie获取登录类型
        cookie_auth_type = call.get_cookie(auth_type_name)

        if header_auth_type:
            auth_type_value = header_auth_type
        elif cookie_auth_type:
            auth_type_value = cookie_auth_type
        else:
            auth_type_value = default_auth_type

        str_split = auth_type_value.split("/")
        if len(str_split) == 2:
            auth_type, auth_backend = str_split
        else:
            auth_type = str_split[0]
            auth_backend = auth_module.get(auth_type, {}).get("backendModule")
            if auth_backend:
                auth_type_value = "{}/{}".format(auth_type, auth_backend)
        # log.info('[{}] auth_type={}'.format(call.path, auth_type_value))
        if auth_type_value != default_auth_type:
            call.result.set_cookie(auth_type_name, auth_type_value)
        # 添加登录类型
        call.auth_type_value = auth_type_value
        auth_type_module_str = auth_module.get(auth_type, {}).get("module")
        if not auth_type_module_str:
            call.clear_cookie(auth_type_name)
            raise MLError(replacement_msg='登录验证类型{}不存在'.format(auth_type))
        # log.info('auth login auth_type_module_str={}'.format(auth_type_module_str))
        login_service = login_dict.get(auth_type, None)
        if not login_service:
            login_service = import_string(auth_type_module_str)
            login_dict[auth_type] = login_service
        return login_service

