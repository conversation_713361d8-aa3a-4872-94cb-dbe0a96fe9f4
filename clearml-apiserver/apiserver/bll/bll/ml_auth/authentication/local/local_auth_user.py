import datetime
from apiserver.config_repo import config
from apiserver.service_repo.auth.auth import Identity
from apiserver.apierrors.errors.ml_bad_request import MLError

from apiserver.bll.bll.ml_auth import MLAuthBLL
from apiserver.bll.bll.ml_auth.session_manager import SessionManager


log = config.logger(__file__)


class CustomIdentity:
    def __init__(self, identity=None):
        self.identity = identity

    @property
    def identity(self):
        return self._identity

    @identity.setter
    def identity(self, value):
        if isinstance(value, dict):
            value = Identity(**value)
        else:
            assert isinstance(value, Identity)
        self._identity = value


class LocalAuthUser:

    @staticmethod
    def auth_user(**kwargs):
        call = kwargs.get("call", None)
        endpoint = kwargs.get("endpoint", None)
        if endpoint.authorize is not None and not endpoint.authorize:
            return

        authorization_key = config.get("apiserver.auth.login.authorization_key", "Authorization")
        auth = call.req.cookies.get(authorization_key)
        if not auth:
            call.result.clear_all_cookies(call)
            raise MLError(code=401, sub_code=401, replacement_msg="验证信息不存在")

        skip_session = kwargs.get("skip_session", False)

        user = None
        session = SessionManager.session_by_sid(sid=auth)
        if session:
            user = MLAuthBLL.user_by_id(obj_id=session.user_id)

        if not user:
            call.result.clear_all_cookies(call)
            raise MLError(code=401, sub_code=401, replacement_msg="用户账号不存在")

        expired = session.expired
        # sso_session_id = session.sso_session_id
        if SessionManager.is_valid(expired):
            time_now = datetime.datetime.utcnow()
            try:
                if not skip_session:
                    if not user.last_active or (time_now - user.last_active).seconds > 30:
                        SessionManager.refresh_expired(auth)
                        # if sso_session_id:
                        #     self._iam_adapter.refresh_session(session_id=sid)
            except Exception as e:
                log.exception('refresh user session failed: session_id={}, e={}'.format(auth, e))
            identity = Identity(
                user=user.id,
                company=user.company,
                role=user.role,
                user_name=user.name,
                company_name=user.company
            )
            custom_identity = CustomIdentity(identity=identity)
            call.auth = custom_identity
            log.info('auth user local success user_name={}, role={}'.format(user.name, user.role))
        else:
            # if sso_session_id:
            #     self._iam_adapter.delete_session(session_id=s.sso_session_id)
            call.result.clear_all_cookies(call)
            raise MLError(code=401, sub_code=401, replacement_msg="登录超时, 请重新登录")
