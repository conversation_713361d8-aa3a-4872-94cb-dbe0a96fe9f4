from apiserver.config_repo import config
from apiserver.bll.bll.ml_auth.session_manager import SessionManager


log = config.logger(__file__)


class LocalLogout:

    @staticmethod
    def logout(**kwargs):
        call = kwargs.get('call', None)
        # 是否保存登录, 默认为 all不保持登录
        authorization_key = config.get("apiserver.auth.login.authorization_key", "Authorization")
        clear_all_session = config.get("apiserver.auth.login.clear_all_session", "all")
        if clear_all_session == "all":
            SessionManager.clear_session(user_id=call.identity.user)
        auth_type = config.get("apiserver.auth.auth.auth_type")
        auth_status = config.get("apiserver.auth.auth.auth_status")
        token_name = config.get("apiserver.auth.token.token_name")

        if call.get_cookie(authorization_key) is not None:
            call.result.clear_cookie(authorization_key)
        if call.get_cookie(auth_type) is not None:
            call.result.clear_cookie(auth_type)
        if call.get_cookie(token_name) is not None:
            call.result.clear_cookie(token_name)
        if call.get_cookie(auth_status) is not None:
            call.result.clear_cookie(auth_status)
        log.info('auth logout local success')
