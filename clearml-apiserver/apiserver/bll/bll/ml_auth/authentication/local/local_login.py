import math
import datetime
from apiserver.config_repo import config
from apiserver.apierrors.errors.ml_bad_request import M<PERSON>rror
from apiserver.utilities.format_time import FormatTime
from apiserver.database.model.auth import User
from apiserver.bll.bll.ml_auth.verify_code import VerifyCode
from apiserver.bll.bll.utils.hash_utils import HashUtils
from apiserver.bll.bll.ml_auth.two_auth import TwoFactorAuth
from apiserver.bll.bll.ml_auth.session_manager import SessionManager
from apiserver.bll.bll.utils.login_password_utils import PasswordUtils
from apiserver.bll.bll.ml_auth.login_record_manager import LoginRecordManager


log = config.logger(__file__)


class LocalLogin:

    @staticmethod
    def login(**kwargs):

        login_record_manager = LoginRecordManager
        two_factor_auth = TwoFactorAuth(notify_type="email")

        call = kwargs.get('call', None)

        # 默认登录设置 local 类型
        auth_type_name = config.get("apiserver.auth.auth.auth_type")
        if not call.get_cookie(auth_type_name):
            call.result.set_cookie(auth_type_name, 'local')
        token_name = config.get("apiserver.auth.token.token_name")
        if call.get_cookie(token_name):
            call.result.clear_cookie(token_name)

        result = {}
        # 登录记录
        request_body = call.data

        # 注:邮箱、手机号也可能作为登录名
        login_username = request_body.get("username", None)
        password = request_body.get("password", None)
        time_now = datetime.datetime.utcnow()

        # 图片验证码失败次数限制
        login_fail_limit_for_captcha_image = config.get("apiserver.auth.login.login_fail_limit_for_captcha_image", 3)
        # 登录失败次数限制
        login_fail_limit = config.get("apiserver.auth.login.login_fail_limit", 5)
        # 登录失败账号冻结的时间
        login_fail_freeze_seconds = config.get("apiserver.auth.login.login_fail_freeze_seconds", 600)

        sid_key = config.get("apiserver.auth.login.sid_key", "sid")
        authorization_key = config.get("apiserver.auth.login.authorization_key", "Authorization")

        # 登录记录通过sid检索
        session_id = call.get_cookie(sid_key)

        login_record = None
        captcha_required = False
        captcha_2fa_required = False
        basic_required = True
        if session_id:
            login_record = login_record_manager.get_login_record(sid=session_id)

        user = User.objects(name=login_username).first()
        if user:
            login_username = user.name  # 若存在真实用户,则登录名取真实用户名

        if login_record and login_username == login_record.name:
            # 重试登录(有登录记录,说明用户登录失败了,正在重试中)
            if user and login_record.captcha_2fa is not None:
                captcha_2fa_required = True
                basic_required = False  # 双因子认证时不需要再进行密码验证
            elif login_record.captcha is not None:
                captcha_required = True
        else:
            # 首次登录,创建登录记录
            login_record = login_record_manager.save_login_record(name=login_username, sid=session_id)
            session_id = login_record.session_id
        # self_obj.current_user = user
        # 验证码校验
        # if captcha_required:
        #     # 需要验证
        #     if request_body.__contains__("captcha"):
        #         captcha = request_body['captcha']
        #         if login_record.expired and login_record.expired <= time_now:
        #             result['captcha_image_url'] = (
        #                 VerifyCode.refresh_verify_code(login_record_manager, session_id)
        #             )
        #             # 会话session_id更新过期时间到cookie中,下同
        #             call.result.set_cookie(sid_key, session_id)
        #             call.result.data = result
        #             raise MLError(replacement_msg="验证码已失效，请重新输入")
        #         elif not captcha or captcha.lower() != login_record.captcha.lower():
        #             result['captcha_image_url'] = (
        #                 VerifyCode.refresh_verify_code(login_record_manager, session_id)
        #             )
        #             call.result.set_cookie(sid_key, session_id)
        #             call.result.data = result
        #             raise MLError(replacement_msg="验证码错误，请重新输入")
        #         else:
        #             log.info("User {} image captcha auth success".format(login_username))
        #     else:
        #         result['captcha_image_url'] = (
        #             VerifyCode.refresh_verify_code(login_record_manager, session_id)
        #         )
        #         call.result.set_cookie(sid_key, session_id)
        #         call.result.data = result
        #         raise MLError(replacement_msg="请输入验证码")

        # 双因子验证码校验
        if captcha_2fa_required:
            # 需要2FA验证码验证,允许重试一定次数
            if request_body.__contains__("captcha2fa"):
                captcha_2fa = request_body['captcha2fa']
                if not login_record.captcha_2fa:
                    result['captcha2faRequired'] = True
                    result["showEmail"] = two_factor_auth.get_show_email(user.email)
                    call.result.data = result
                    raise MLError(replacement_msg="请先发送双因子验证码")
                elif login_record.expired and login_record.expired <= time_now:
                    result['captcha2faRequired'] = True
                    call.result.data = result
                    raise MLError(replacement_msg="双因子验证码已失效，请重新发送验证码")
                elif not captcha_2fa or captcha_2fa.lower() != login_record.captcha_2fa.lower():
                    captcha_2fa_failed_count = login_record.failed_count + 1
                    if captcha_2fa_failed_count >= login_fail_limit:
                        # 冻结账号
                        locked_expired = time_now + datetime.timedelta(seconds=login_fail_freeze_seconds)
                        user.update(is_locked=True, locked_expired=locked_expired)
                        message = '登录重试次数过多, 账户被临时冻结，请{}分钟后再试'.format(math.ceil(login_fail_freeze_seconds / 60))
                        captcha_2fa_failed_count = 0
                    else:
                        message = '双因子验证码错误'
                        result['captcha2faRequired'] = True
                    login_record.update(failed_count=captcha_2fa_failed_count)
                    call.result.data = result
                    raise MLError(replacement_msg=message)
                else:
                    log.info("User {} 2FA auth success".format(login_username))
            else:
                result['code'] = 0
                result['message'] = '请输入双因子验证码'
                result['captcha2faRequired'] = True
                result["showEmail"] = two_factor_auth.get_show_email(user.email)
                call.result.data = result
                raise MLError(replacement_msg=result['message'])

        if basic_required:
            if user:
                if user.is_locked:
                    if user.locked_expired:
                        if user.locked_expired > time_now:
                            # raise MLError(replacement_msg='账户被锁定至{}'.format(
                            #     FormatTime.utc(user.locked_expired)))
                            pass
                    else:
                        raise MLError(replacement_msg="用户被锁")
                elif not user.is_active:
                    raise MLError(replacement_msg="用户不可用")

            password = PasswordUtils.decrypt_password_parameter(password)
            if not password:
                raise MLError(replacement_msg="用户名或密码错误")
            
            log.info("User {} login password=({}) hash pasword=({}) data password=({})".format(login_username, password, HashUtils.hash_string(password),user.password))
            if not user or not HashUtils.check_string(password, user.password):
                if not login_record.failed_count:
                    login_record.failed_count = 0
                login_fail_count = login_record.failed_count + 1

                if user:
                    if login_fail_count >= login_fail_limit:
                        # 冻结账号
                        locked_expired = time_now + datetime.timedelta(seconds=login_fail_freeze_seconds)
                        user.update(is_locked=True, locked_expired=locked_expired)
                        login_record.update(failed_count=0)
                        raise MLError(replacement_msg='登录重试次数过多, 账户被临时冻结，请{}分钟后再试'.format(
                            math.ceil(login_fail_freeze_seconds / 60)))

                # 登录出错超出一定次数,则需要附加验证码进行验证
                if login_fail_count >= login_fail_limit_for_captcha_image:
                    result['captcha_image_url'] = (
                        VerifyCode. refresh_verify_code(login_record_manager, session_id)
                    )

                login_record.update(failed_count=login_fail_count)
                call.result.set_cookie(sid_key, session_id)
                call.result.data = result
                raise MLError(replacement_msg="用户名或密码错误")

            # 基本认证通过之后,判断用户是否需要双因子认证
            if two_factor_auth.user_2fa_enabled(user.id):
                if two_factor_auth.check_notifier():
                    # 通知用户,send email...
                    user_email = user.email
                    if not user_email:
                        raise MLError(replacement_msg="用户未指定邮箱")

                    # 保存登录记录
                    login_record_manager.save_login_record(sid=session_id)
                    result['captcha2faRequired'] = True
                    call.result.set_cookie(sid_key, session_id)
                    call.result.data = result
                    raise MLError(replacement_msg="您所登录的账号开启了双因子登录验证")
                else:
                    # 2FA验证码发送服务无法工作时,允许初始超级管理员可以登录操作
                    if not user.is_superuser:
                        raise MLError(replacement_msg="已开启双因子认证,但{}服务异常".format(two_factor_auth.notify_type))
                    log.info("2FA enabled but email server error, The superuser need to login")

        # 创建session
        session = SessionManager.create_session(user.id, from_ip=login_username)
        # self_obj.require_setting("secret_key", "secure key")
        # secret = self_obj.application.settings["secret_key"]
        # sid = create_signed_value(secret, "Sid", session.sid).decode()
        expired = session.expired

        # self_obj.db.commit()
        call.result.clear_cookie(sid_key)
        call.result.set_cookie(authorization_key, session.sid)

        # 登录成功后删除该用户登录记录
        login_record_manager.delete_login_record(name=login_username)

        # 更新登录时间
        now = datetime.datetime.utcnow()
        user.update(is_locked=None, locked_expired=None, last_login=now, last_active=now)

        # 若首次登录,仍视为登录成功,但需要修改密码
        # if user.is_reseted:
        #     raise MLError(sub_code=1001, replacement_msg="首次登陆请修改登录密码")

        result = {
            "expired": FormatTime.utc(expired),
            User.id.name: user.id,
            User.role.name: user.role,
            User.name.name: user.name,
            User.realname.name: user.name,
            User.sex.name: user.sex.cn_name,
            User.email.name: user.email,
            User.phone.name: user.phone if user.phone else "",
            User.is_reseted.name: user.is_reseted,
        }
        call.result.data = result
        return result
