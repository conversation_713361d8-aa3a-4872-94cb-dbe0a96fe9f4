from apiserver.config_repo import config
from apiserver.apierrors.errors.ml_bad_request import MLError

log = config.logger(__file__)


class LocalAuthRole:

    @staticmethod
    def auth_role(**kwargs):
        call = kwargs.get('call', None)
        endpoint = kwargs.get('endpoint', None)
        if endpoint.authorize and not endpoint.allows(call.identity.role):
            raise MLError(code=403, sub_code=403, replacement_msg="需要管理员权限")
        log.info('auth role local success endpoint authorize={}, allow_roles={}'.format(
            endpoint.authorize, endpoint.allow_roles))
