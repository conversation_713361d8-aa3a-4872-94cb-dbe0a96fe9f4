from apiserver.config_repo import config

from apiserver.bll.bll.ml_auth.authentication.local.local_login import LocalLogin
from apiserver.bll.bll.ml_auth.authentication.local.local_logout import LocalLogout
from apiserver.bll.bll.ml_auth.authentication.local.local_auth_user import LocalAuthUser
from apiserver.bll.bll.ml_auth.authentication.local.local_auth_role import LocalAuthRole


log = config.logger(__file__)


class LocalLoginBLL:

    @staticmethod
    def login(**kwargs):
        result = LocalLogin.login(**kwargs)
        return result

    @staticmethod
    def logout(**kwargs):
        result = LocalLogout.logout(**kwargs)
        return result

    @staticmethod
    def callback(**kwargs):
        log.info("This callback request should not be received in local auth type")

    @staticmethod
    def auth_user(**kwargs):
        return LocalAuthUser.auth_user(**kwargs)

    @staticmethod
    def auth_role(**kwargs):
        return LocalAuthRole.auth_role(**kwargs)
