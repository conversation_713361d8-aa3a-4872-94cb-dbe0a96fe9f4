from apiserver.config_repo import config
from apiserver.bll.bll.ml_auth.authentication.common.common_get_auth import CommonGetAuth


log = config.logger(__file__)


class LoginBLL(object):

    login_dict = dict()

    @staticmethod
    def login(**kwargs):
        return LoginBLL.get_login_bll(
            kwargs.get('call', None)
        ).login(**kwargs)

    @staticmethod
    def logout(**kwargs):
        return LoginBLL.get_login_bll(
            kwargs.get('call', None)
        ).logout(**kwargs)

    @staticmethod
    def callback(**kwargs):
        return LoginBLL.get_login_bll(
            kwargs.get('call', None)
        ).callback(**kwargs)

    @staticmethod
    def auth_user(**kwargs):
        return LoginBLL.get_login_bll(
            kwargs.get('call', None)
        ).auth_user(**kwargs)

    @staticmethod
    def auth_role(**kwargs):
        return LoginBLL.get_login_bll(
            kwargs.get('call', None)
        ).auth_role(**kwargs)

    @staticmethod
    def get_login_bll(call):
        return CommonGetAuth.get_login_bll(
            call,
            LoginBLL.login_dict
        )
