import os
import random
import uuid
import base64
from io import BytesIO
from captcha.image import ImageCaptch<PERSON>
from apiserver.apierrors.errors.ml_bad_request import M<PERSON>rror
from apiserver.config_repo import config
from apiserver.bll.bll.adapter.s3 import get_s3_adapter
from apiserver.bll.bll.ml_auth.login_record_manager import LoginRecordManager


log = config.logger(__file__)


class VerifyCode:

    @staticmethod
    def save_image_captcha_2_s3(captcha_chars: str, file_name: str):
        image_captcha_obj = ImageCaptcha()
        image_file_obj = BytesIO()
        image_captcha_obj.write(captcha_chars, image_file_obj)

        # 图片上传至S3
        _s3_adapter = get_s3_adapter()
        file_size = image_file_obj.tell()
        image_file_obj.seek(0)
        tmp_bucket_name = config.get("apiserver.s3.tmp_bucket_name")
        tmp_object_name = config.get("apiserver.s3.tmp_object_name")
        object_name = "{}verify_code/{}".format(tmp_object_name, file_name)
        _s3_adapter.put_object(tmp_bucket_name, object_name,
                               data=image_file_obj,
                               length=file_size,
                               content_type="image/png")

    @staticmethod
    def save_image_captcha_2_local(captcha_chars: str, file_name: str):
        verify_code_path = config.get("apiserver.auth.login.verify_code_path")
        if not os.path.exists(verify_code_path):
            os.makedirs(verify_code_path, exist_ok=True)
        ImageCaptcha().write(captcha_chars, verify_code_path + file_name)

    @staticmethod
    def refresh_verify_code(lr_manager, sid):
        verify_code_save_type = config.get("apiserver.auth.login.verify_code_save_type", "local")
        func_dict = {
            "s3": VerifyCode.save_image_captcha_2_s3,
            "local": VerifyCode.save_image_captcha_2_local,
        }
        save_func = func_dict.get(verify_code_save_type)
        letter_cases = 'abcdefghjkmnpqrstuwxy'
        init_chars = ''.join((letter_cases, letter_cases.upper(), ''.join(map(str, range(3, 9)))))

        def get_chars():
            return random.sample(init_chars, 4)

        code = ''.join(get_chars())
        lr_manager.save_login_record(sid=sid, captcha=code)

        file_name = uuid.uuid4().hex + ".png"
        save_func(code, file_name)

        return file_name

    @staticmethod
    def get_verify_code_2_s3(call=None):
        pass

    @staticmethod
    def get_verify_code_2_local(call=None):
        verify_code_path = config.get("apiserver.auth.login.verify_code_path")
        file = call.data.get("captcha_image_url")
        if not file:
            raise MLError(replacement_msg="captcha_image_url参数不存在")
        verify_code_path = "{}/{}".format(verify_code_path, file)
        log.info("ml auth login verify_code_path={}".format(verify_code_path))
        if not os.path.isfile(verify_code_path):
            raise MLError(replacement_msg="验证码不存在, 请刷新")
        with open(verify_code_path, "rb") as image_file:
            encoded_image = base64.b64encode(image_file.read()).decode('utf-8')
        os.remove(verify_code_path)
        call.result.data = {"image_base64": encoded_image}

    @staticmethod
    def get_verify_code(call=None):
        verify_code_save_type = config.get("apiserver.auth.login.verify_code_save_type", "local")
        func_dict = {
            "s3": VerifyCode.get_verify_code_2_s3,
            "local": VerifyCode.get_verify_code_2_local,
        }
        get_func = func_dict.get(verify_code_save_type)
        get_func(call=call)

    @staticmethod
    def refresh_captcha_image(call=None):
        sid_key = config.get("apiserver.auth.login.sid_key", "sid")
        session_id = call.get_cookie(sid_key)
        if not session_id:
            raise MLError(replacement_msg="太长时间没有登录,请重新刷新进入登录页面")

        login_record = LoginRecordManager.get_login_record(sid=session_id)
        if not login_record:
            raise MLError(replacement_msg="太长时间没有登录,请重新刷新进入登录页面")

        verify_code_filename = VerifyCode.refresh_verify_code(LoginRecordManager, session_id)
        call.result.set_cookie(sid_key, session_id)
        call.result.data = {"captcha_image_url": verify_code_filename}
