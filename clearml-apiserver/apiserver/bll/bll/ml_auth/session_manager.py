from datetime import datetime, timedelta
from apiserver.config_repo import config
from apiserver.utilities.random import random_ascii
from apiserver.database.model.auth import Session
from apiserver.bll.bll.adapter.iam.iam_adapter import IamAdapter
from apiserver.bll.bll.ml_auth import MLAuthBLL


class SessionManager:

    @staticmethod
    def create_session(user_id, **kwargs):
        """创建新的会话凭证"""

        # 得到新 sid
        while True:
            sid = random_ascii(128)
            c = Session.objects(sid=sid).first()
            if not c:
                break

        allow_user_login_more = config.get("apiserver.auth.login.allow_user_login_more", False)
        default_auth_type = config.get("apiserver.auth.default_auth_type", False)
        if not allow_user_login_more:
            _iam_adapter = IamAdapter.get_instance() if default_auth_type == "sso/cas" else None
            old_sessions = MLAuthBLL.list_session_by_user_id(user_id=user_id)
            for old_session in old_sessions:
                # 删除旧的 session
                if _iam_adapter:
                    _iam_adapter.delete_session(old_session.sso_session_id)
                old_sessions.delete()

        kwargs["expired"] = SessionManager.after_seconds()
        session = MLAuthBLL.create_session(sid=sid, user_id=user_id, sso_ticket=sid, **kwargs)
        return session

    @staticmethod
    def clear_session(user_id):
        """清除 User 会话凭证"""
        MLAuthBLL.delete_session_by_user_id(user_id=user_id)

    @staticmethod
    def after_seconds():
        session_cookie_age = config.get("apiserver.auth.login.session_cookie_age")
        now = datetime.utcnow()
        return now + timedelta(seconds=session_cookie_age)

    @staticmethod
    def refresh_expired(sid):
        """重新激活失效时间"""
        expired = SessionManager.after_seconds()
        MLAuthBLL.update_session_by_sid(sid=sid, fields=dict(expired=expired))

    @staticmethod
    def is_valid(expired):
        return datetime.utcnow() < expired

    @staticmethod
    def session_by_sid(sid=None):
        return MLAuthBLL.session_by_sid(sid=sid)
