from apiserver.config_repo import config
from apiserver.apimodels.auth import CreateUserResponse
from apiserver.bll.bll.ml_auth import MLAuth<PERSON>L
from apiserver.bll.bll.utils.hash_utils import HashUtils
from apiserver.bll.bll.ml_auth.auth_check import <PERSON><PERSON><PERSON><PERSON><PERSON>, AuthCommonCheck
from apiserver.database.model.auth import User, Role
from apiserver.config.info import get_default_company
from apiserver.utilities.cubestudio import CubeStudioCli


log = config.logger(__file__)


class AuthUserBLL:

    @staticmethod
    def create_user(**kwargs):
        call = kwargs.get("call", None)
        log.info('auth service user create call.data={}'.format(call.data))
        AuthCheck.create_user_check(call)

        # 使用默认密码
        password = HashUtils.hash_string(config.get("apiserver.auth.password.default"))
        data = {
            User.name.name: call.data.get("name", None),
            User.realname.name: call.data.get("realname", None),
            User.password.name: password,
            User.sex.name: call.data.get("sex", None),
            User.email.name: call.data.get("email", None),
            User.phone.name: call.data.get("phone", None),
            User.role.name: call.data.get("role", None),
            User.is_active.name: call.data.get("is_active", False)
        }
        user_id = MLAuthBLL.create_user(data=data)
        call.result.data_model = CreateUserResponse(id=user_id)

    @staticmethod
    def user_list(**kwargs):
        call = kwargs.get("call", None)
        log.info('auth service list user call.data={}'.format(call.data))
        result = MLAuthBLL.list_user(call=call)
        call.result.data = result

    @staticmethod
    def update_user_password(**kwargs):
        call = kwargs.get("call", None)
        log.info('auth service user update call.data={}'.format(call.data))
        AuthCheck.update_user_password_check(call)

        request_body = call.data
        password = request_body.get("password", None)

        fields = {}
        if password:
            fields["password"] = HashUtils.hash_string(password)
        
        # 重置密码被重置的状态
        fields[User.is_reseted.name] = False
        log.info('auth service user update fields={}'.format(fields))
        MLAuthBLL.update_user(call=call, fields=fields, obj_id=call.identity.user)
    
    @staticmethod
    def update_user(**kwargs):
        call = kwargs.get("call", None)
        log.info('auth service user update call.data={}'.format(call.data))
        data = AuthCheck.update_user_check(call)
        
        # 管理员可以改所有用户，用户只能改自己的
        user_id = call.data.get("user", None)
        if not user_id or call.identity.role in [Role.user, Role.guest, Role.annotator]:
            user_id = call.identity.user
        log.info('auth service user update fields={}'.format(data))
        MLAuthBLL.update_user(call=call, fields=data, obj_id=user_id)
    
    @staticmethod
    def reset_user(**kwargs):
        call = kwargs.get("call", None)
        req = call.data_model
        fields = {}
        # 重置为默认密码
        if req.reset_password is not None and req.reset_password == True:
            fields[User.password.name] = HashUtils.hash_string(config.get("apiserver.auth.password.default"))
            fields[User.is_reseted.name] = True
        
        # 重置用户的状态
        if req.reset_status is not None:
            fields[User.is_active.name] = req.reset_status
        
        user_id = req.user
        AuthCommonCheck.check_id(user_id)
        # 重置密码被重置的状态
        if fields:
            log.info('auth service user update fields={}'.format(fields))
            MLAuthBLL.update_user(call=call, fields=fields, obj_id=user_id)

    @staticmethod
    def user_by_id(**kwargs):
        """
        {
            "user": "9865639f0422450c8d4ae5f79cb83559"
        }
        """
        call = kwargs.get("call", None)
        log.info('auth service user by id call.data={}'.format(call.data))
        # AuthCheck.get_user_check(call)
        
        request_body = call.data
        user_id = request_body.get("user", None)
        if not user_id or call.identity.role in [Role.user, Role.guest, Role.annotator]:
            user_id = call.identity.user
        AuthCommonCheck.check_id(obj_id=user_id)
        result = MLAuthBLL.user_detail_by_id(obj_id=user_id)
        call.result.data = result

    @staticmethod
    def delete_user(**kwargs):
        """
        {
            "user": "9865639f0422450c8d4ae5f79cb83559"
        }
        """
        call = kwargs.get("call", None)
        log.info('auth service user delete call.data={}'.format(call.data))
        AuthCheck.delete_user_check(call)

        request_body = call.data
        user_id = request_body.get("user", None)
        result = MLAuthBLL.delete_user(call=call, obj_id=user_id)
        call.result.data = result

    @staticmethod
    def get_other_session(**kwargs):
        call = kwargs.get("call", None)
        user_id = call.identity.user
        data = call.data
        force = data.get("force", None)
        result = MLAuthBLL.user_detail_by_id(obj_id=user_id, hiden_password=False, get_cookies=True)
        user_name = result["name"]
        # password = result["password"]
        # 暂固定密码,目前无法解密密码
        resp = dict()
        password = config.get("apiserver.auth.third.third_user_default_password", "admin1234")

        email = result["email"]
        resp["user"] = {}
        resp["user"]["access_token"] = {}
        if force == "true":
            system_name_list = ["cubestudio"]
        else:
            system_name_list = []

            cubestudio = result.get("cubestudio")
            if not cubestudio:
                system_name_list.append("cubestudio")
            else:
                resp["user"]["access_token"]["cubestudio"] = cubestudio

        for system_name in system_name_list:  # ["local_steamer", "fastgpt", "labelstudio", "gitlab"]
            try:
                # 自动创建其它系统的用户
                AuthUserBLL.create_or_update_other_system_user(system_name, user_name, password=password,
                                                               email=email, user_id=user_id)
                # 自动获取其它其它的访问token
                access_token = AuthUserBLL.get_other_system_access_token(system_name, user_name,
                                                                         password=password, user_id=user_id)
                resp["user"]["access_token"][system_name] = access_token
                log.info("users get current user system_name={}, access_token={}".format(system_name, access_token))
            except Exception as e:
                log.exception("users get current user error={}".format(e))
                resp["user"]["access_token"][system_name] = "users get current user error={}".format(e)

        log.info(f'result .....  {result}')
        resp["user"]["created"] = result["created"]
        resp["user"]["role"] = result.get("role", None)
        resp["user"]["email"] = result["email"]
        resp["user"]["user_type"] = result.get("user_type", None)
        resp["user"]["username"] = result["name"]
        resp["user"]["company"] = result.get("company", None)
        call.result.data = resp

        for tokens in resp["user"]["access_token"]["cubestudio"].split(";"):
            if tokens:
                token_kvs = tokens.split("=")
                if len(token_kvs) == 2:
                    if token_kvs[0] != "myapp_username":
                        log.info(f'Set cookie {token_kvs[0]} = {token_kvs[1]}')
                        call.result.set_cookie(token_kvs[0], token_kvs[1])


    @staticmethod
    def create_or_update_other_system_user(system_name, user_name, **kwargs):
        """
        支持的其它系统
        "steamer", "fastgpt", "labelstudio"
        """
        if system_name == "cubestudio":
            cubestudio_cli = CubeStudioCli()
            user = cubestudio_cli.get_user(user_name)
            if not user:
                cubestudio_cli.create_user(user_name, password=kwargs.get("password"), email=kwargs.get("email"))
            return user

    @staticmethod
    def get_other_system_access_token(system_name, user_name, **kwargs):
        """
        支持的其它系统
        "steamer", "fastgpt", "labelstudio"
        """
        user_id = kwargs.get("user_id")
        if system_name == "cubestudio":
            try:
                cubestudio_cli = CubeStudioCli()
                log.info(f'get user cookie {user_name} {kwargs.get("password")}')
                cs_cookie = cubestudio_cli.get_user_cookie(user_name, password=kwargs.get("password"))
                if not cs_cookie:
                    cs_cookie = None
                fields = {"cubestudio_cookie": cs_cookie}
                MLAuthBLL.update_user(fields=fields, obj_id=user_id)
            except Exception as e:
                fields = {"cubestudio_cookie": None}
                MLAuthBLL.update_user(fields=fields, obj_id=user_id)
                raise e
            return cs_cookie