import random
import string
import socket
from apiserver.config_repo import config
from apiserver.bll.bll.adapter.email.email_adapter import EmailAdapter
from apiserver.bll.bll.utils.email_sms_password_utils import PasswordAESUtils


log = config.logger(__file__)


class TwoFactorAuth:

    def __init__(self, notify_type="email"):
        self.notify_type = notify_type

    def user_2fa_enabled(self, user_inner_id):
        """检查用户是否开启双因子认证"""
        return config.get("apiserver.auth.login.enable_2fa", False)

    def check_notifier(self):
        if self.notify_type == "email":
            try:
                _email_client = EmailAdapter(**self.get_email_server_config())
            except socket.timeout:
                log.exception("Connent to the mail server timed out")
                return False
            return _email_client.check_login()
        return False

    def generate_captcha(self, length=6, content_type="digits") -> str:
        # 生成随机验证码
        if content_type == "digits":
            return ''.join(random.sample(string.digits, length))

    def send_captcha(self, captcha, **kwargs):
        if self.notify_type == "email":
            return self.send_captcha_by_email(captcha, kwargs["email"])

    def send_captcha_by_email(self, captcha: str, email: str):
        _email_client = EmailAdapter(**self.get_email_server_config())
        captcha_2fa_age = int(config.get("apiserver.auth.login.captcha_2fa_age") / 60)
        msg = _email_client.prepare_plain_message(
            email,
            "【容器云平台】验证码",
            "【容器云平台】你好，你正在登录，你的验证码{}，有效时间{}分钟".format(captcha, captcha_2fa_age))
        _email_client.send(email, msg)

    def get_email_server_config(self):
        smtp_server = config.get("apiserver.auth.email.smtp_server", False)
        smtp_port = config.get("apiserver.auth.email.smtp_port", 0)
        smtp_username = config.get("apiserver.auth.email.smtp_username", "")
        smtp_password = config.get("apiserver.auth.email.smtp_password", "")
        smtp_password = PasswordAESUtils.password_decrypt(smtp_password)
        smtp_ssl = config.get("apiserver.auth.email.smtp_ssl", 1) == 1
        return dict(host=smtp_server, port=smtp_port, user=smtp_username, password=smtp_password, enable_ssl=smtp_ssl)

    @classmethod
    def get_show_email(cls, email: str):
        """
        对外展示用,隐藏了部分字符的邮箱地址
        """

        at_symbol_pos = email.find("@")
        email_name_len = len(email[:at_symbol_pos])
        assert email_name_len, "User email name format error"
        not_hide_len = 5
        if email_name_len <= not_hide_len:
            hide_start_pos = 1
        else:
            hide_start_pos = not_hide_len

        return email[:hide_start_pos] + "****" + email[at_symbol_pos:]
