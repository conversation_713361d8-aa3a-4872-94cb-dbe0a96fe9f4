import re
from apiserver.apierrors.errors.ml_bad_request import <PERSON><PERSON><PERSON><PERSON>
from apiserver.database.model.auth import Role, UserSex
from apiserver.bll.bll.ml_auth import MLAuthBLL


class AuthCheck:

    @staticmethod
    def create_user_check(call):
        request_body = call.data
        name = request_body.get("name", None)
        AuthCommonCheck.check_name(name)

        # 检查姓名
        realname = request_body.get("realname", None)
        if realname:
            AuthCommonCheck.check_realname(realname)

        role = request_body.get("role", None)
        if call.identity.role in [Role.user, Role.annotator, Role.guest]:
            raise MLError(replacement_msg="普通用户禁止创建用户")

        roles = Role.get_company_roles()
        if role not in roles:
            raise MLError(replacement_msg='用户角色应为: {}'.format(",".join(roles)))
        
        # 检查性别
        sex = request_body.get("sex", None)
        if sex:
            if not UserSex.is_member(sex):
                raise MLError(replacement_msg="性别只能为：男、女、不填")

        # 检查电话
        phone = request_body.get("phone", None)
        if phone:
            AuthCommonCheck.check_phone(phone)
    
        # 检查邮箱
        email = request_body.get("email", None)
        if email:
            AuthCommonCheck.check_email(email)


    @staticmethod
    def update_user_password_check(call):
        request_body = call.data
        password = request_body.get("password", None)
        if password:
            AuthCommonCheck.check_password(password=password)

    @staticmethod
    def update_user_check(call):
        request_body = call.data
        # 更新字段
        fields = {}
        # 检查用户ID是否存在
        user_id = request_body.get("user", None)
        if user_id:
            AuthCommonCheck.check_id(obj_id=user_id)
            # fields["user"] = user_id
        
        # 普通用户只能修改自己的信息
        if call.identity.role in [Role.user, Role.guest, Role.annotator] and call.identity.user!= user_id:
            raise MLError(replacement_msg="普通用户禁止修改其他用户信息")
        
        # 检查姓名
        realname = request_body.get("realname", None)
        if realname:
            AuthCommonCheck.check_realname(realname)
            fields["realname"] = realname
    
        # 检查性别
        sex = request_body.get("sex", None)
        if sex:
            if not UserSex.is_member(sex):
                raise MLError(replacement_msg="性别只能为：男、女、不填")
            fields["sex"] = sex
    
        # 检查电话
        phone = request_body.get("phone", None)
        if phone:
            AuthCommonCheck.check_phone(phone)
            fields["phone"] = phone
    
        # 检查邮箱
        email = request_body.get("email", None)
        if email:
            AuthCommonCheck.check_email(email)
            fields["email"] = email

        return fields
        

    @staticmethod
    def get_user_check(call):
        request_body = call.data
        user_id = request_body.get("user", None)
        AuthCommonCheck.check_id(obj_id=user_id)
        if call.identity.user != user_id and call.identity.role == Role.user:
            raise MLError(replacement_msg="用户没有权限查看其他用户详情")

    @staticmethod
    def delete_user_check(call):
        request_body = call.data
        user_id = request_body.get("user", None)
        AuthCommonCheck.check_id(obj_id=user_id)
        if call.identity.role == Role.user:
            raise MLError(replacement_msg="普通用户禁止删除用户")
        if user_id == call.identity.user:
            raise MLError(replacement_msg="不能删除自己{}".format(user_id))


class AuthCommonCheck:

    @staticmethod
    def check_id(obj_id=None):
        result = MLAuthBLL.user_by_id(obj_id=obj_id)
        if not result:
            raise MLError(replacement_msg="用户不存在")

    @staticmethod
    def check_name(name):
        if not name:
            raise MLError(replacement_msg="名称不能为空")
        regex = re.compile(r'^[a-z][a-z0-9-]{1,19}$')
        match = regex.match(name or '')
        if not match:
            raise MLError(replacement_msg="用户名小写字母开头，允许小写字母、数字、“-”；2~20个字符")
        obj = MLAuthBLL.user_by_name(name=name)
        if obj:
            raise MLError(replacement_msg="{}已存在".format(name))

    @staticmethod
    def check_password(password=None):
        """
        检查密码复杂程度
        密码至少要包含数字、字母、特殊字符两种组合方式8位以上
        """
        if not password:
            raise MLError(replacement_msg="创建用户密码不能为空")
        if len(password) < 6 or len(password) > 20:
            raise MLError(replacement_msg="密码必须为6~20位数字和字符的组合,至少要包含数字、字母、特殊字符两种组合方式")
        pattern = re.compile('[A-Za-z]+')
        check_contain_word = pattern.findall(password)
        pattern = re.compile('[0-9]+')
        check_contain_number = pattern.findall(password)
        pattern = re.compile('([^a-z0-9A-Z])+')
        check_contain_symbol = pattern.findall(password)
        if (check_contain_number and check_contain_symbol) or (check_contain_number and check_contain_word) or (check_contain_symbol and check_contain_word):
            return True
        else:
            raise MLError(replacement_msg="密码必须为6~20位数字和字符的组合,至少要包含数字、字母、特殊字符两种组合方式")
        
    
    @staticmethod
    def check_realname(realname):
        if not realname:
            raise MLError(replacement_msg="姓名不能为空")
        # 匹配基本汉字
        regex = re.compile(r"^[\u3400-\u4DBF\u4E00-\u9FFF]+$")
        if regex.match(realname or "") is None:
            raise MLError(replacement_msg="姓名必须为汉字")
        
    @staticmethod
    def check_sex(realname):
        if not UserSex.is_member(realname):
            raise MLError(replacement_msg="性别只能为：男、女、不填")

    @staticmethod
    def check_phone(phone):
        regex = re.compile(r"^1[3-9]\d{9}$")
        if regex.match(phone or "") is None:
            raise MLError(replacement_msg="手机号格式不正确")

    @staticmethod
    def check_email(email):
        regex = re.compile(r'^[a-zA-Z0-9.!#$%&\'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$')
        if regex.match(email or "") is None:
            raise MLError(replacement_msg="邮箱号格式不正确")
