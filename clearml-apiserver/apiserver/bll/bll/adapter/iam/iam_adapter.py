import logging
from apiserver.config_repo import config
from apiserver.bll.bll.adapter.iam.iam_backends import IAM_TYPE_DICT


logger = logging.getLogger(__name__)


class IamAdapter(object):
    GLOBAL_INSTANCE = None

    @staticmethod
    def get_instance():
        if not IamAdapter.GLOBAL_INSTANCE:
            IamAdapter.GLOBAL_INSTANCE = IamAdapter(internal_create=True)
        return IamAdapter.GLOBAL_INSTANCE

    def __init__(self, internal_create=False):
        if not internal_create:
            raise ValueError('please get instance by invoke "%s.get_instance()"' % IamAdapter.__name__)
        self.td_iam_backend = IAM_TYPE_DICT[config.get("apiserver.auth.iam.iam_type")](
            url=config.get("apiserver.auth.iam.iam_url"),
            timeout=config.get("apiserver.auth.iam.iam_http_timeout"),
            app_code=config.get("apiserver.auth.iam.app_code")
        )

    def refresh_session(self, session_id, **kwargs):
        """刷新session"""
        response = self.td_iam_backend.refresh_session(session_id, **kwargs)
        # logger.info("refresh IAM session: {}".format(response.text))
        return response.json()["data"]

    def delete_session(self, session_id, **kwargs):
        """删除session,用户强制用户下线"""
        response = self.td_iam_backend.delete_session(session_id, **kwargs)
        return response.json()["data"]

    def get_user(self, iam_user_id, **kwargs):
        """获取用户信息"""
        response = self.td_iam_backend.get_user(iam_user_id, **kwargs)
        logger.debug("get IAM user info: {}".format(response.text))
        return response.json()["data"]

    def list_user(self, **kwargs):
        """分页查询用户列表"""
        response = self.td_iam_backend.list_user(**kwargs)
        logger.debug("list IAM user : {}".format(response.text))
        return response.json()["data"]

    def is_admin(self, iam_user_id, **kwargs):
        response = self.td_iam_backend.get_app_by_code(config.get("apiserver.auth.iam.app_code"), **kwargs)
        logger.debug("get IAM app: {}".format(response.text))
        admin_user_list = response.json()["data"]["administrators"]

        for admin_user in admin_user_list:
            if iam_user_id == admin_user["userId"]:
                break
        else:
            return False
        
        return True

    def create_user_extra_prop(self, iam_user_id, **kwargs):
        """创建用户的额外属性"""
        response = self.td_iam_backend.create_user_extra_prop(iam_user_id, **kwargs)
        logger.debug("create IAM user extra prop: {}".format(response.text))
        return response.json()["data"]

    def update_user_extra_prop(self, iam_user_id, **kwargs):
        """更新用户的额外属性"""
        response = self.td_iam_backend.update_user_extra_prop(iam_user_id, **kwargs)
        logger.debug("update IAM user extra prop: {}".format(response.text))
        return response.json()["data"]
