import logging
import requests

from apiserver.bll.bll.adapter.iam.iam_abstract import IAMAbstract


logger = logging.getLogger(__name__)


class TDIAMBackend(IAMAbstract):

    def __init__(self, url, timeout=15, verify=None, **kwargs):
        self.server_url = url
        self.app_code =  kwargs["app_code"]
        self.headers = {
            "Accept": "application/json",
            "content-type": "application/json",
            "X-Subject-App": self.app_code
        }
        self.verify = verify
        self.timeout = (timeout, 2 * timeout)

        self.app_id = None

    @property
    def api_version(self):
        return "v2"

    @classmethod
    def add_response_encoding(cls, response, encoding="UTF-8"):
        response.encoding = encoding

    def refresh_session(self, session_id, x_auth_session="CROSS_ALL_SESSION"):
        """刷新session"""
        url = self.server_url + "/iam/session/{}/refresh".format(session_id)
        headers = {"X-Auth-Session": x_auth_session}
        headers.update(self.headers)
        response = requests.put(url, headers=headers, verify=self.verify, timeout=self.timeout)
        self.add_response_encoding(response)
        response.raise_for_status()
        return response
    
    def delete_session(self, session_id, x_auth_session="CROSS_ALL_SESSION"):
        """删除session"""
        url = self.server_url + "/iam/session/{}".format(session_id)
        headers = {"X-Auth-Session": x_auth_session}
        headers.update(self.headers)
        response = requests.delete(url, headers=headers, verify=self.verify, timeout=self.timeout)
        self.add_response_encoding(response)
        response.raise_for_status()
        return response

    def get_user(self, iam_user_id, x_auth_session="CROSS_ALL_SESSION"):
        """获取用户信息"""
        url = self.server_url + "/iam/user/{}".format(iam_user_id)
        headers = {"X-Auth-Session": x_auth_session}
        headers.update(self.headers)
        response = requests.get(url, headers=headers, verify=self.verify, timeout=self.timeout)
        self.add_response_encoding(response)
        response.raise_for_status()
        return response

    def list_user(self, x_auth_session="CROSS_ALL_SESSION", **kwargs):
        """获取用户信息"""
        url = self.server_url + "/iam/user"
        headers = {"X-Auth-Session": x_auth_session}
        headers.update(self.headers)
        response = requests.get(url, headers=headers, params=kwargs, verify=self.verify, timeout=self.timeout)
        self.add_response_encoding(response)
        response.raise_for_status()
        return response

    def get_app_by_code(self, app_code, x_auth_session="CROSS_ALL_SESSION"):
        """获取IAM注册的应用(本平台)信息"""
        url = self.server_url + "/iam/app/{}/by_code/details".format(app_code)
        headers = {"X-Auth-Session": x_auth_session}
        headers.update(self.headers)
        response = requests.get(url, headers=headers, verify=self.verify, timeout=self.timeout)
        self.add_response_encoding(response)
        response.raise_for_status()
        return response

    def create_user_extra_prop(self, iam_user_id, x_auth_session="CROSS_ALL_SESSION", **kwargs):
        url = self.server_url + "/iam/user/{}/extra_prop".format(iam_user_id)
        headers = {"X-Auth-Session": x_auth_session}
        headers.update(self.headers)

        if not self.app_id:
            self.app_id = self.get_app_by_code(self.app_code).json()["data"]["id"]
        kwargs["appId"] = self.app_id
        response = requests.post(url, headers=headers, json=kwargs, verify=self.verify, timeout=self.timeout)
        self.add_response_encoding(response)
        response.raise_for_status()
        return response

    def update_user_extra_prop(self, iam_user_id, x_auth_session="CROSS_ALL_SESSION", **kwargs):
        if not self.app_id:
            self.app_id = self.get_app_by_code(self.app_code).json()["data"]["id"]

        url = self.server_url + "/iam/user/{}/app/{}/extra_prop".format(iam_user_id, self.app_id)
        headers = {"X-Auth-Session": x_auth_session}
        headers.update(self.headers)
        response = requests.put(url, headers=headers, json=kwargs, verify=self.verify, timeout=self.timeout)
        self.add_response_encoding(response)
        response.raise_for_status()
        return response
