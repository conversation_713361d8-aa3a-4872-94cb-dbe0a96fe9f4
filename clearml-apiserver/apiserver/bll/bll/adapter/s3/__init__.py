__all__ = [
            "MinioAdapter",
            "get_s3_adapter",
        ]

from apiserver.config_repo import config
from apiserver.bll.bll.adapter.s3.minio_adapter import MinioAdapter


def get_minio_adapter():
    return MinioAdapter.get_instance(scheme=config.get("apiserver.s3.scheme"),
                                     host=config.get("apiserver.s3.host"),
                                     port=config.get("apiserver.s3.port"),
                                     access_key=config.get("apiserver.s3.access_key"),
                                     secret_key=config.get("apiserver.s3.secret_key"),
                                     region=config.get("apiserver.s3.region"),)


def get_s3_adapter(storage_type=config.get("apiserver.s3.storage_type")):
    """根据类型适配对应的Registry镜像仓库操作方法类"""
    storage_type = storage_type.lower()
    if storage_type == "minio":
        return get_minio_adapter()
    else:
        return  None
