import threading
import minio

from datetime import datetime, timedelta
from minio.deleteobjects import DeleteObject
from minio.retention import Retention
from minio.commonconfig import CopySource, GOVERNANCE


class MinioAdapter(object):
    _instance_lock = threading.Lock()

    @classmethod
    def get_instance(cls, *args, **kwargs):
        if not hasattr(cls, "_instance"):
            with cls._instance_lock:
                if not hasattr(cls, "_instance"):
                    cls._instance = cls(*args, **kwargs)
        return cls._instance

    def __init__(self, scheme="http", host="127.0.0.1", port=9000, access_key=None, secret_key=None, region=None):
        self.minio_client = minio.Minio(
            "{}:{}".format(host, port),
            access_key=access_key,
            secret_key=secret_key,
            secure=True if scheme =="https" else False,
            region=region
        )

    def bucket_exists(self, bucket_name):
        """
        判断存储桶(bucket)是否存在
        """

        try:
            return self.minio_client.bucket_exists(bucket_name)
        except minio.error.S3Error as e:
            if e.code == "InvalidBucketName":
                return False
            raise e
    
    def put_object(self, bucket_name, object_name, data, length=-1, content_type="application/octet-stream", part_size=10*1024*1024, retain_days:int=0):
        """
        上传文件
        """
        retention = None
        if retain_days:
            date = datetime.utcnow() + timedelta(days=retain_days)
            retention = Retention(GOVERNANCE, date)

        result = self.minio_client.put_object(bucket_name, object_name, data, length=length, content_type=content_type, part_size=length+part_size, retention=retention)
        return result.object_name
    
    def copy_object(self, src_bucket_name, src_object_name, bucket_name, object_name, retain_days:int=0):
        retention = None
        if retain_days:
            date = datetime.utcnow() + timedelta(days=retain_days)
            retention = Retention(GOVERNANCE, date)

        copy_source = CopySource(src_bucket_name, src_object_name)
        result = self.minio_client.copy_object(bucket_name, object_name, copy_source, retention=retention)

        return result.object_name

    def set_object_retention(self, bucket_name, object_name, retain_days:int):
        assert retain_days, "retain_days must be given"
        date = datetime.utcnow() + timedelta(days=retain_days)
        retention = Retention(GOVERNANCE, date)

        self.minio_client.set_object_retention(bucket_name, object_name, retention)

    def list_object(self, bucket_name, object_name):
        """
        文件列表
        """

        return self.minio_client.list_objects(bucket_name, prefix=object_name, recursive=False)

    def stat_object(self, bucket_name, object_name):
        """
        获取文件的元数据
        注：返回内容为 urllib3.response.HTTPResponse
        """

        return self.minio_client.stat_object(bucket_name, object_name)

    def object_exists(self, bucket_name, object_name):
        try:
            self.minio_client.stat_object(bucket_name, object_name)
        except minio.error.S3Error as e:
            if e.code == "NoSuchKey":
                return False
            raise e
        return True

    def get_object(self, bucket_name, object_name):
        """
        获取文件的数据
        注：返回内容为 urllib3.response.HTTPResponse
        """

        return self.minio_client.get_object(bucket_name, object_name)
    
    def presigned_get_object(self, bucket_name, object_name, expires=timedelta(days=1))->str:
        """
        为文件生成一个 URL 下载链接用于 HTTP GET 下载, 有效期默认为1天
        注：返回内容为 str, 即文件的临时下载链接
        """

        return self.minio_client.presigned_get_object(bucket_name, object_name, expires=expires)

    def fget_object(self, bucket_name, object_name, file_path):
        """
        获取文件的数据(下载到文件)
        参数 file_path 的值建议为文件绝对路径
        注：返回内容为对象信息
        """

        return self.minio_client.fget_object(bucket_name, object_name, file_path)
    
    def fput_object(self, bucket_name, object_name, file_path, content_type="application/octet-stream"):
        """
        上传文件到对象存储(指定本地文件的路径)
        参数 file_path 的值建议为文件绝对路径
        注：返回内容为对象信息
        """

        return self.minio_client.fput_object(bucket_name, object_name, file_path, content_type=content_type)
    
    def remove_object(self, bucket_name, object_name, ttl_seconds=0):
        """
        删除文件
        参数 ttl_seconds 指定后,仅删除存在时间达到该值的文件
        """

        objects_generator = self.minio_client.list_objects(bucket_name, object_name, recursive=True)

        if ttl_seconds:
            delete_object_list = []
            time_now = datetime.utcnow()
            for file_object in objects_generator:
                update_time = file_object.last_modified.replace(tzinfo=None)
                if (time_now - update_time).total_seconds() > ttl_seconds:
                    delete_object_list.append(DeleteObject(file_object.object_name))
        else:
            # 使用 python 的 map() 方法生成一组待删除的文件对象
            delete_object_list = map(
                lambda x: DeleteObject(x.object_name),
                objects_generator,
            )
        
        if not delete_object_list:
            return

        err_res = self.minio_client.remove_objects(bucket_name, delete_object_list)
        err_msg_list = []
        for err in list(err_res):
            err_msg_list.append(err.message)
        
        if len(err_msg_list):
            err_msg = "、".join(err_msg_list)
            raise ValueError("Delete object failed:{}".format(err_msg))
