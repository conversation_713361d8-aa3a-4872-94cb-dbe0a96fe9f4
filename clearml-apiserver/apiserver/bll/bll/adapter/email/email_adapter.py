import logging
import smtplib
from email.mime.text import MIMEText
from email.header import Header
from email.utils import formataddr


class EmailAdapter(object):
    def __init__(self, host="localhost", user="", password="", enable_ssl=True, **kwargs):
        """
        param host: str 设置服务器
        param port: int SMTP 端口号
        """
        self.logger = logging.getLogger(__name__)
        self.user = user # 用户邮箱地址
        self.password = password   #口令
        port = kwargs.get("port", 0)
        if enable_ssl:
            self.smtp = smtplib.SMTP_SSL(host, port, timeout=3)
        else:
            self.smtp = smtplib.SMTP(host, port, timeout=3)

    def check_login(self):
        """注:去掉邮箱域名后缀(如 @163.com)后的用户名,仍可登录。但不能在发件人中去掉"""
        try:
            self.smtp.login(self.user, self.password)
        except:
            self.logger.exception("Login the mail server failed")
            return False
        return True

    def send(self, receiver_addrs, message):
        """Except: smtplib.SMTPException 无法发送邮件"""

        self.smtp.login(self.user, self.password)
        if isinstance(message, MIMEText):
            message = message.as_string()
        self.smtp.sendmail(self.user, receiver_addrs, message)

    def prepare_plain_message(self, receiver, subject, msg, msg_type="plain", **kwargs):
        message = MIMEText(msg, msg_type, 'utf-8')
        message['From'] = formataddr([kwargs.get("sender_name", "梯度科技"), self.user])    # 发送者
        message['To'] = formataddr([kwargs.get("receiver_name"), receiver])    # 接收者
        message['Subject'] = Header(subject, 'utf-8')
        return message
