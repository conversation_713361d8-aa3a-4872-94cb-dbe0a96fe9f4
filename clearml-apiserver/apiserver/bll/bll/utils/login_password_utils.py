from apiserver.config_repo import config
from apiserver.bll.bll.utils.base64_utils import Base64Utils


log = config.logger(__file__)


class PasswordUtils(object):

    @staticmethod
    def decrypt_password_parameter(crypt_password):
        if not crypt_password:
            return crypt_password
        if not isinstance(crypt_password, str):
            raise TypeError('password parameter must be a str')
        if not crypt_password.startswith('a1'):  # 目前只支持a1版本
            return crypt_password
        try:
            password_alpha = crypt_password[2:]
            password_alpha = password_alpha[::-1]
            password_alpha = Base64Utils.base64_decode(password_alpha)
            password_alpha = password_alpha.replace('%3D', '=')
            password_alpha = Base64Utils.base64_decode(password_alpha)
            return password_alpha
        except:
            log.warning('Decrypt password parameter failed: crypt_password={}'.format(crypt_password))
            return crypt_password
