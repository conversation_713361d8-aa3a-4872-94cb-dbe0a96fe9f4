import time
from apiserver.config_repo import config
from apiserver.utilities.aes import CryptoUtils

log = config.logger(__file__)

# aes 密钥
CREDENTIALS_CONFIG_AES_KEY_V1 = 'C8F945769C947CB06BFE94CCF0CCEA5A'


class PasswordAESUtils(object):

    @staticmethod
    def password_encrypt(msg):
        """v1 aes 加密"""
        if msg is None:
            msg = ""
        version = "1"
        time_stamp = str(int(time.time()))
        msg = CryptoUtils.aes_encrypt("{}-----{}-----{}".format(msg, time_stamp, version), CREDENTIALS_CONFIG_AES_KEY_V1)
        return "{}:{}|{}:{}|{}:{}".format(len(version), version, len(time_stamp), time_stamp, len(msg), msg)

    @staticmethod
    def password_decrypt(msg):
        """v1 aes 解密"""
        if msg is None:
            return ""
        try:
            msg_list = msg.split("|")
            if len(msg_list) == 3 and msg_list[0].split(":")[1] == "1":
                decrypt_msg = CryptoUtils.aes_decrypt(msg_list[2].split(":")[1], CREDENTIALS_CONFIG_AES_KEY_V1)
                decrypt_msg_list = decrypt_msg.split("-----")
                msg = decrypt_msg_list[0] if decrypt_msg_list[1] == msg_list[1].split(":")[1] and decrypt_msg_list[2] == "1" else ""
            return msg
        except Exception as e:
            log.exception('解密失败:{}, {}'.format(msg, e))
            return msg

