import bcrypt


class HashUtils(object):

    CHARSET = 'utf-8'

    @staticmethod
    def hash_string(string):
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(string.encode(HashUtils.CHARSET), salt).decode(HashUtils.CHARSET)
        return hashed

    @staticmethod
    def check_string(string, hashed):
        if bcrypt.checkpw(string.encode(HashUtils.CHARSET), hashed.encode(HashUtils.CHARSET)):
            return True
        return False
