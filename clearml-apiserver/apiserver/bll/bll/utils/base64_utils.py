import base64


class Base64Utils(object):

    @staticmethod
    def base64_encode(string, charset='utf-8'):
        if not string:
            return string
        return base64.standard_b64encode(string.encode(charset)).decode(charset)

    @staticmethod
    def base64_decode(string, charset='utf-8'):
        if not string:
            return string
        return base64.standard_b64decode(string.encode(charset)).decode(charset)
