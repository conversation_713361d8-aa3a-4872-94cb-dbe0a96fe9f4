from datetime import datetime

from apiserver import database
from apiserver.apierrors import errors
from apiserver.database.model.algo import Algo, AlgoRelease, AlgoRun
from apiserver.database.errors import translate_errors_context
from typing import List
# from apiserver.utilities.faas import *


class AlgoBLL:

    @classmethod
    def create(
        cls,
        user: str,
        name: str,
        company: str,
    ) -> str:
        now = datetime.utcnow()
        algo = Algo(
            id=database.utils.id(),
            user=user,
            name=name,
            company=company,
            created=now,
            last_update=now,
        )
        algo.save()
        return algo.id

    @classmethod
    def delete(cls, company: str, algo_id: str):
        with translate_errors_context():
            res = Algo.objects(id=algo_id).delete()
            release_list = AlgoRelease.get_many(company, query_dict={"algo": algo_id})
            for release in release_list:
                release_id = release["id"]
                release_version = release["version"]
                # delete_algorithm_svc(algo_id, release_version)
                AlgoReleaseBLL.delete(release_id)
            return res

    @classmethod
    def update(cls, company: str, algo_id: str, **fileds):
        algo = Algo.get_for_writing(company=company, id=algo_id)
        if algo:
            update_fields = {}
            if "description" in fileds:
                update_fields["description"] = fileds.pop("description")
            update_fields["last_update"] = datetime.utcnow()
            update_fields.update(fileds)
            updated = algo.update(upsert=False, **update_fields)
            return updated
        return 0

    @classmethod
    def get_by_id(self, algo_id: str, company_id: str) -> Algo:
         with translate_errors_context():
            query = dict(id=algo_id, company=company_id)
            algo = Algo.objects(**query).first()
            if not algo:
                return {}
                # raise errors.bad_request.InvalidQueueId(**query)
            return algo

class AlgoReleaseBLL:

    @classmethod
    def create(
        cls,
        user: str,
        version: str,
        company: str,
        algo: str,
        # exec_config: dict,
        publish_status: str,
        args: dict = None,
        hyperparams: dict = None,
    ) -> str:
        now = datetime.utcnow()
        algoRelease = AlgoRelease(
            id=database.utils.id(),
            algo=algo,
            user=user,
            version=version,
            company=company,
            created=now,
            last_update=now,
            args=args,
            # exec_config=exec_config,
            publish_status=publish_status,
        )
        algoRelease.save()
        result = {
            "algo_id": algoRelease.algo,
            "version": algoRelease.version,
            "release_id": algoRelease.id,
        }
        return result

    @classmethod
    def get_by_version(self, algo: str, version: str, company_id: str) -> AlgoRelease:
        with translate_errors_context():
            query = dict(algo=algo, version=version, company=company_id)
            release = AlgoRelease.objects(**query).first()
            if not release:
                raise errors.bad_request.InvalidQueueId(**query)
            return release

    @classmethod
    def update(self, company: str, release_id, fields):
        with translate_errors_context():
            release = AlgoRelease.get_for_writing(company=company, id=release_id)

            updated = release.update(upsert=False, **fields)
            # AlgoRelease.update(company_id=company, id=release_id, partial_update_dict=fields)
        return updated
    
    
    @classmethod
    def set_baseline(self, company: str, release_id, status):
        print(release_id, status)
        algo = AlgoRelease.get_for_writing(company=company, id=release_id)
        update_fields = {}
        update_fields["is_baseline"] = status
        res = algo.update(upsert=False, **update_fields)
        return res

    def set_best(company: str, release_id, status):
        algo = AlgoRelease.get_for_writing(company=company, id=release_id)
        update_fields = {}
        update_fields["best"] = status
        res = algo.update(upsert=False, **update_fields)
        return res

    def set_from_project(company: str, release_id):
        algo = AlgoRelease.get_for_writing(company=company, id=release_id)
        update_fields = {}
        update_fields["from_project"] = True
        res = algo.update(upsert=False, **update_fields)
        return res    

    @classmethod
    def delete(cls,release_id:str):
        with translate_errors_context():
            res = AlgoRelease.objects(id=release_id).delete()
            return res
    
    @classmethod
    def get_all(cls):
        with translate_errors_context():
            return AlgoRelease.objects()


class AlgoRunBLL:

    @classmethod
    def create(
        cls,
        user: str,
        company: str,
        algo: str,
        algo_release: str,
        algo_metrics: dict,
    ) -> List[str]:
        now = datetime.utcnow()

        algoRelease = AlgoRun(
            id=database.utils.id(),
            algo=algo,
            user=user,
            algo_release=algo_release,
            company=company,
            dataset=algo_metrics.get("dataset_id"),
            complete_ratio=float(algo_metrics.get("complete_rate"))*100,
            complete_duration=float(algo_metrics.get("complete_time")),
            scheme_score = float(algo_metrics.get("scheme_score")),
            created=now,
        )
        algoRelease.save()
        return "save success"

    @classmethod
    def update(cls,
            company: str,
            id: str,
            algo_metrics: List[dict],
        ):
        algo_run = AlgoRun.get_for_writing(company=company, id=id)

        fields = {'complete_ratio':float(algo_metrics.get("complete_rate"))*100, 'complete_duration':float(algo_metrics.get("complete_time")), 'scheme_score':float(algo_metrics.get("scheme_score"))}
        update_fields = {}
        update_fields.update(fields)
        updated = algo_run.update(upsert=False, **update_fields)
        return updated


    @classmethod
    def get(
        cls,
        company: str,
        algo: str,
        algo_release: str,
        dataset: str,   
    ):
        query_dict = {"algo":algo, "algo_release":algo_release, "dataset":dataset}
        algoruns = AlgoRun.get_many(company=company,query_dict=query_dict)
        if len(algoruns)>0:
            return algoruns[0]
        return None
    
