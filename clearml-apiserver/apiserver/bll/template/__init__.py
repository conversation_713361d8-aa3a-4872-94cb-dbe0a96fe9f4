
from apiserver.database.model.template import Template, Type
from apiserver.database.model.project import Project
from apiserver.database.errors import translate_errors_context
from apiserver import database
from apiserver.apierrors import errors
from datetime import datetime
import json

class TemplateBLL(object):
    @classmethod
    def create(
        self,
        company: str,
        configs: dict,
        name: str,
        type: str = None,
    ) -> Template:
        try:
            Type(type)
        except ValueError:
            raise errors.bad_request.DataValidationError()
        with translate_errors_context():
            now = datetime.utcnow()
            template = Template(
                    id = database.utils.id(),
                    configs = configs,
                    company=company,
                    created = now,
                    type = type,
                    name = name,
                    last_update = datetime.utcnow(),
              )
            return template.save()

    @classmethod
    def update(cls, company: str, project: str):
        project = Project.get_for_writing(company=company, id=project)
        project_dict = project.to_proper_dict()
        update_fields = {
            'last_update':datetime.utcnow()
        }
        template_list = Template.get_many(company=company, query_dict={"type":project.type.name})
        template_id = template_list[0]['id']
        template = Template.get_for_writing(company=company, id=template_id)
        update_fields["configs"] = project_dict.get('configs')
        updated = template.update(upsert=False, **update_fields)
        return  updated    