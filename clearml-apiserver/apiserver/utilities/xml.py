import requests
import json
from apiserver.config_repo import config

log = config.logger(__file__)

def generate_dataset_xml(request_body):
    # TODO: 需要加上另外一个生成xml的接口
    # external_url = config.get("apiserver.external.external_url")
    # log.info(f'Generate xml file body content {request_body}')
    # res = request_external_url(external_url, request_body, "POST")

    res = {'code':200, 'data': [{'inputPath':"/data/", "outPath":"/data/algorithmXml/data/output/"}]}
    return res

def request_external_url(external_url, request_body=None, method='GET'): 
    try:
        if method == 'GET':
            response = requests.get(external_url, params=request_body)
        elif method == 'POST':
            response = requests.post(external_url, json=request_body) 
        response.raise_for_status()
        res = response.json()
        return res

    except requests.RequestException as e:
        log.warn(f'generate xml error while request {external_url} err: {e}')
        raise errors.bad_request.RequestExternalUrlError()