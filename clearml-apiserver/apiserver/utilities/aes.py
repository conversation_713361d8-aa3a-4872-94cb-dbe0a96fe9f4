import base64

from Crypto.Cipher import AES
from Crypto.Cipher.AES import MODE_CBC


class ZeroBytePadding(object):
    def pad(self, message_bytes):
        pad_length = AES.block_size - (len(message_bytes) % AES.block_size)
        return message_bytes + bytes(pad_length)

    def unpad(self, message_bytes):
        padding_end = -1
        for i in reversed(range(len(message_bytes))):
            if message_bytes[i] == 0:
                padding_end = i
            else:
                break
        if padding_end > 0:
            return message_bytes[0:padding_end]
        else:
            return message_bytes


BYTE_PADDING_BY_MODE = {
    'zero': ZeroBytePadding(),
}


class CryptoUtils(object):
    @staticmethod
    def aes_encrypt(message, key, mode=MODE_CBC, iv='default-iv-12345', encoding='utf-8', output_mode='hex',
                    pad_mode='zero'):
        if isinstance(key, str):
            key = key.encode()
        if isinstance(iv, str):
            iv = iv.encode()

        message_bytes = message.encode(encoding)

        padding = BYTE_PADDING_BY_MODE[pad_mode]
        message_bytes = padding.pad(message_bytes)

        aes = AES.new(key, mode, iv)
        cipher_text = aes.encrypt(message_bytes)

        if output_mode == 'hex':
            cipher_text = cipher_text.hex()
        elif output_mode == 'base64':
            cipher_text = base64.standard_b64encode(cipher_text).decode('utf-8')
        else:
            raise ValueError('unsupport output model: %s' % output_mode)

        return cipher_text

    @staticmethod
    def aes_decrypt(cipher_text, key, mode=MODE_CBC, iv='default-iv-12345', encoding='utf-8', output_mode='hex',
                    pad_mode='zero'):
        if output_mode == 'hex':
            cipher_bytes = bytes.fromhex(cipher_text)
        elif output_mode == 'base64':
            cipher_bytes = base64.standard_b64decode(cipher_text.encode('utf-8'))
        else:
            raise ValueError('unsupport output model: %s' % output_mode)

        if isinstance(key, str):
            key = key.encode()
        if isinstance(iv, str):
            iv = iv.encode()

        aes = AES.new(key, mode, iv)
        message_bytes = aes.decrypt(cipher_bytes)

        padding = BYTE_PADDING_BY_MODE[pad_mode]
        message_bytes = padding.unpad(message_bytes)
        message = str(message_bytes, encoding=encoding)
        return message
