from apiserver.config_repo import config
from apiserver.config_repo import config

log = config.logger(__file__)

def get_resource_from_config():
    resource_list = []
    cpu_resource = config.get("apiserver.resource.cpu", {})
    for i in cpu_resource:
        resource_list.append({
            "type":"cpu",
            "cores":i.get("cores"),
            "count":i.get("count")
        })
    memory_resource = config.get("apiserver.resource.memory", {})
    for i in memory_resource:
        resource_list.append({
            "type":"memory",
            "size":i
        })
    gpu_resource = config.get("apiserver.resource.gpu", {})
    for i in gpu_resource:
        log.info(f'name: {i.get("name")}, memory: {i.get("memory")}, count: {i.get("count")}')  
        resource_list.append({
            "type":"gpu",
            "name":i.get("name"),
            "memory":i.get("memory"),
            "count":i.get("count")
        })
    return resource_list