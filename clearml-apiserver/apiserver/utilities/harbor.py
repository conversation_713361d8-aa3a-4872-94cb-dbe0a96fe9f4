import requests
from apiserver.config_repo import config

class HarborCli():
    def __init__(self):

        config.get("apiserver.git.address")
        self.harbor_url = config.get("apiserver.harbor.url")
        self.project = config.get("apiserver.harbor.project")
        self.username = config.get("apiserver.harbor.username")
        self.password = config.get("apiserver.harbor.password")
        self.harbor_domain = config.get("apiserver.harbor.domain",default="hub.tiduyun.com:5000")

    # 获取镜像名列表:  name:version
    def get_images(self, project=None):
        ret_images = []
        images_url = f'{self.harbor_url}/api/v2.0/projects/{self.project if project is None else project}/repositories'
        response = requests.get(images_url, auth=(self.username, self.password), verify=False)
        if not response:
            print('Get image in {project} failed')
            return []
        images = response.json()
        for image in images:  
            tags = harbor_cli.get_tags(image['name'], project=project)
            if not tags:
                print(f"No tags found in {image['name']}")
                continue
            for tag in tags:
                image_url = f'{self.harbor_domain}/{image["name"]}:{tag}'

            ret_images.append(image_url)
        return ret_images
    
    # 获取镜像名列表： name
    def get_image_names(self, project=None):
        ret_image_names = []

        project = self.project if project is None else project

        images_url = f'{self.harbor_url}/api/v2.0/projects/{project}/repositories'
        response = requests.get(images_url, auth=(self.username, self.password), verify=False)
        images = response.json()
        for image in images:  
            ret_image_names.append(image['name'].replace(f"{project}/", ''))
        return ret_image_names

    #获取镜像tag列表
    def get_tags(self,repo_name, project=None):
        ret_tags = []

        project = self.project if project is None else project
        if project in repo_name:
            repo_name = repo_name.replace(project, '')
        tags_url = f'{self.harbor_url}/api/v2.0/projects/{project}/repositories/{repo_name}/artifacts'
        response = requests.get(tags_url, auth=(self.username, self.password), verify=False)
        if not response.ok:
            return None
        artifacts = response.json()
        for artifact in artifacts:
            tags = artifact['tags']
            if tags is not None:
                for tag in tags:
                    ret_tags.append(tag['name'])
        return ret_tags
    
    # 获取harbor仓库域名
    def get_harbor_domain(self):
        return self.harbor_domain
    
    def get_harbor_project(self):
        return self.project
    
harbor_cli = HarborCli()