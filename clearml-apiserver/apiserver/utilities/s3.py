from apiserver.config_repo import config
from boto3.session import Session
import boto3
import re

class s3_config:
    url = config.get("apiserver.s3.url")
    access_key = config.get("apiserver.s3.access_key")
    secret_key = config.get("apiserver.s3.secret_key")
    bucket = config.get("apiserver.s3.bucket")
    pvc = config.get("apiserver.s3.algorithm_pvc")
    session = Session(access_key, secret_key)
    s3_client = session.client("s3", endpoint_url=url)
    # s3_client = boto3.client('s3',aws_secret_access_key = secret_key,aws_access_key_id = access_key,endpoint_url = url)


s3_client = s3_config.s3_client

def list_file_in_s3(s3_path):
    s3_prefix = f"{s3_config.pvc}/{str(s3_path)}"
    try:
        res = s3_client.list_objects(Bucket=s3_config.bucket, Prefix=s3_prefix)
    except Exception as e:
        print(e)
    result = []
    contents = res.get("Contents",[])
    if contents:
        for info in contents:
            name = info["Key"]
            result.append(name)
        return result
    else:
        return []
    

def rename_files_in_s3(s3_path, algo_id, version, file_paths):
    for old_key in file_paths:
        new_key_path = old_key.replace(s3_path, f"{algo_id}/{version}")
        s3_client.copy_object(
            Bucket=s3_config.bucket,
            CopySource={"Bucket": s3_config.bucket, "Key": old_key},
            Key=new_key_path,
        )


def delete_files_in_s3(file_paths):
    for old_key in file_paths:
        s3_client.delete_object(Bucket=s3_config.bucket, Key=old_key)

def delete_file(file_name):
        s3_client.delete_object(Bucket=s3_config.bucket, Key=file_name)


def check_release(version):
    pattern = r"^[0-9]\d*\.[0-9]\d*\.[0-9]\d*$"
    if re.match(pattern, version):
        return True
    else:
        return False