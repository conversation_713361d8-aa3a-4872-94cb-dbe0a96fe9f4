# import docker
# import requests
# import json
# from apiserver.config_repo import config

# log = config.logger(__file__)

# template_dockerfile = "/home/<USER>/Dockerfile-algorithm"
# image_labels = {"watchdog":"v1"}

# faas_gateway_url = config.get("apiserver.faas.url")
# faas_user = config.get("apiserver.faas.user")
# faas_password = config.get("apiserver.faas.password")
# faas_namespace = config.get("apiserver.faas.namespace", default="tiduai")

# # 算法服务接口
# # 算法服务名构成： svc-{id}-{version}  其中version为1-0-1模式

# # 构建算法服务镜像并推送
# # base_image: 基础镜像
# # cmd: 算法运行指令
# def image_build(base_image, cmd):
#     try:
#         image_name = base_image.rsplit(':', 1)[0]
#         image_tag = base_image.rsplit(':', 1)[1]
#         auth_config = {
#                 'username': config.get('apiserver.harbor.username'),
#                 'password': config.get('apiserver.harbor.password'),
#             }
#         print(f'Going to build image:  {image_name}:{image_tag}')

#         client = docker.DockerClient(base_url='unix://var/run/docker.sock')
#         client.images.pull(repository=image_name, tag=image_tag, auth_config=auth_config)
#         image = client.images.get(f'{image_name}:{image_tag}')
#         if image.labels.get('watchdog') :
#             print(f'Image {image_name}:{image_tag} already build ...')
#             return True

#         buildargs = { "RUN_CMD": cmd, "BASE_IMAGE":base_image }
#         img = client.images.build(dockerfile=template_dockerfile,
#                             path="/home/",
#                             buildargs=buildargs,
#                             tag=base_image,
#                             labels=image_labels)
#         client.images.push(repository=image_name, tag=image_tag, auth_config=auth_config)
#     except Exception as ex:
#         print("Exception while build :",ex)
#         return False
#     return True

# # 部署算法服务
# def deploy_algorithm(id:str, version:str, image:str, cmd:str, algo_name:str):
#     try:

#         # 将算法版本、IDE以环境变量的形式注入of-watchdog，后续回调可以带上对应的信息
#         envs = {"ALGO_NAME":algo_name,"ALGO_ID":id, "ALGO_VERSION":version, "POST_METRICS_URL":config.get("apiserver.faas.callback_url")+"/algo.update_running_metrics"}
#         log.info(f"deploy algorithm envs {envs}")
#         # version = version.replace('.','-')
#         service = f'svc-{id}-{version.replace(".","-")}'
#         url = f"http://{faas_user}:{faas_password}@{faas_gateway_url}/system/functions?usage=1"

#         payload = json.dumps({
#             "service": service,
#             "image": f"{image}",
#             "envProcess": f"{cmd}",
#             "annotations": {},
#             "algopath": f"{id}/{version}",
#             "envVars":envs
#         })
#         headers = {
#             'Content-Type': 'application/json'
#         }
#         response = requests.request("POST", url, headers=headers, data=payload)
#         log.info(f'[algorithm deploy] response {response}')
#         if not response.ok:
#             return False
#     except Exception as ex:
#         print("Exception while deploying :",ex)
#         return False
#     return True

# # 获取算法服务详情
# def get_algorithm_svc(id:str, version:str):
#     try:
#         version = version.replace('.','-')
#         service = f"svc-{id}-{version}"
#         url = f"http://{faas_user}:{faas_password}@{faas_gateway_url}/system/function/{service}?namespace={faas_namespace}"
#         payload={}
#         headers = {}
#         print(url)
#         response = requests.request("GET", url, headers=headers, data=payload)
#         if not response.ok:
#             return {}
#     except Exception as ex:
#         print("Exception while get algorithm service : ", ex)
#         return {}
    
#     data = json.loads(response.text)
#     data['url'] = f"http://{faas_gateway_url}/function/{service}"
#     print(data)
#     return data

# # 删除算法服务
# def delete_algorithm_svc(id:str, version:str):
#     try:
#         url = f"http://{faas_user}:{faas_password}@{faas_gateway_url}/system/functions?namespace={faas_namespace}"

#         payload = json.dumps({
#         "functionName": f"svc-{id}-{version.replace('.','-')}"
#         })
#         headers = {
#         'Content-Type': 'application/json'
#         }

#         response = requests.request("DELETE", url, headers=headers, data=payload)
#         print(response.text)
#         if not response.ok:
#             return False
#     except Exception as ex:
#         print("Exception while delete  algorithm service : ", ex)
#         return False
#     return True

# # 测试算法服务是否可用
# # dataset_url:  数据集下载路径（可以改成数组）
# # callback_url: 回调地址，用于算法运行结束结果回调
# def test_algorithm_svc(id:str, version:str, 
#                         dataset_urls=None,                  # 数据集下载url，暂时不用 
#                         callback_url: str=None,             # 回调接口  update_metrics
#                         dataset_id=None,                    # 验证数据集ID
#                         ):
#     try:
#         print(callback_url)
#         print(dataset_urls)
#         svc = get_algorithm_svc(id, version)

#         # TODO:加上需求分解算法的逻辑，两种的数据集区分开来
#         input_path = f"/data/standard/planning/{dataset_id}/"
#         output_path = f"/data/standard/planning/outputs/{id}-{version}-{dataset_id}/"

#         headers={"X-Callback-Url":callback_url, "Content-Type":"application/json", "Baseline-Test":"true","Dataset-Id":f'{dataset_id}'}
#         params = f"?cmd_args=--input_path {input_path} --output_path {output_path}"
#         data=json.dumps({"dataset_files":dataset_urls})

#         response = requests.request("POST", svc['url']+params, headers=headers, data=data)
#         log.info(f'----------[test_algorithm response]---------------{response}')
#         if response.status_code != 200 :
#             return False
#     except Exception as ex:
#         log.info(f'Exception while test algorithm service : {ex}')
#         return False
#     return True

# def get_algorithm_svc_health(id:str, version:str):
#     try:
#         svc = get_algorithm_svc(id, version)
#         check_url = svc['url']+'/_/health'
#         response = requests.request("GET", check_url)
#         if response.status_code != 200 :
#             return False
#     except Exception as ex:
#         print("Exception while get algorithm service health : ", ex)
#         return False
#     return True