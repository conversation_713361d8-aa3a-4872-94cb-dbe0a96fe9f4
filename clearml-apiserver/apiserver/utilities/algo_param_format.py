# 类型
from enum import Enum
import json
from apiserver.config_repo import config


log = config.logger(__file__)

class FileType(Enum):
    JSON = 'json'
    XML = 'xml'
    YAML = 'yaml'
    MD = 'md'

class BaseAlgoFormat():
    def __init__(
            self,
            type = None,
            file_name = None,
    ):
        self.file_type=type
        self.file_name=file_name

    # 生成新的配置文件
    @classmethod
    def format_file(file_path):
        pass

    # 从上传的文件解析算法配置参数
    @classmethod
    def parse_file(file_path):
        pass

class JsonAlgoFormat(BaseAlgoFormat):
    def format_file(file_path, content):
        #将json写入文件
        with open(file_path, 'w') as f:
            json.dump(content, f,  ensure_ascii=False)

    def parse_file(file_path):
        # 从file_path中读取json文件
        with open(file_path, 'r') as f:
            content = json.load(f)
        return content

class YamlAlgoFormat(BaseAlgoFormat):
    def format_file(file_path, content):
        pass

    def parse_file(file_path):
        return {}


class XmlAlgoFormat(BaseAlgoFormat):
    def format_file(file_path, content):
        pass

    def parse_file(file_path):
        return {}

class MDAlgoFormat(BaseAlgoFormat):
    def format_file(file_path, content):

        pass

    def parse_file(file_path):
        return {}

file_type_map = {
    FileType.JSON: JsonAlgoFormat,
    FileType.YAML: YamlAlgoFormat,
    FileType.XML: XmlAlgoFormat,
    FileType.MD: MDAlgoFormat
}


def format_file(file_type, file_path, content):
    log.info(f'file_type_map: {file_type_map}')
    formatter = file_type_map.get(file_type)
    if formatter:
        formatter.format_file(file_path, content)
    else:
        raise ValueError(f"Unsupported file type: {file_type}")

def parse_file(file_type, file_path):
    log.info(f'file_type_map: {file_type_map} {file_type} {file_path}')
    formatter = file_type_map.get(file_type)
    if formatter:
        return formatter.parse_file(file_path)
    else:
        raise ValueError(f"Unsupported file type: {file_type}")
