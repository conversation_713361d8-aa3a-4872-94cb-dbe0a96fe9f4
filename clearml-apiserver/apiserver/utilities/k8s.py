from kubernetes import client, config, watch
config.load_kube_config()

import docker
import os
from apiserver.bll.algo import AlgoBLL, AlgoReleaseBLL, AlgoRunBLL
from typing import Optional
from apiserver.config_repo import config
from apiserver.utilities.threads_manager import ThreadsManager
from apiserver.database.model.algo import (
    Algo,
    AlgoRelease,
    PublishStatus
)
log = config.logger(__file__)

k8sCli = client.AppsV1Api()

volume_name = config.get("apiserver.algo.pvc", "tiduai-algorithm-pvc")
namespace = config.get("apiserver.kubernetes.namespace", "tiduai")
harbor_prefix = config.get("apiserver.harbor.prefix", "hub.tiduyun.com:5000")
harbor_project = config.get("apiserver.harbor.project", "ai")
deployment_labels = {"app": "tiduai-algorithm"}

template_dockerfile = "Dockerfile-clearml"
image_labels = {"watchdog":"v1"}

template_dockerfile = "Dockerfile-clearml"
image_labels = {"watchdog":"v1"}

CONDITION_AVAILABLE = "Available"
CONDITION_PROGRESSING = "Progressing"

UPLOAD_PREFIX = "/code/upload/"
# 创建deployment
# 算法服务接口
# 算法服务名构成： svc-{id}-{version}  其中version为1-0-1模式
def create_deployment_object(image_name:str,algo_id:str, version:str, cpu:str, memory:str, company: str, cmd:str=None):
    script = os.path.join(f'/code/',"infer/endpoint.sh" )
    name = f'svc-{algo_id}-{version.replace(".", "-")}'
    container = client.V1Container(
        name=name,
        image=f'{harbor_prefix}/{harbor_project}/{image_name}',
        command=["/bin/bash", f"{script}"],
        # TODO: 增加命令行参数
        # command=cmd,
        volume_mounts=[
            client.V1VolumeMount(
                name="code-volume",
                mount_path="/code",
                sub_path=f'{algo_id}/{version}'
            )
        ],
        resources={
            "limits": {
                "cpu": cpu,
                "memory": memory+"Gi"
            }
        }
    )
    
    # if cmd is not None:
    #     os.system(f'bash {scripts}')
    template = client.V1PodTemplateSpec(
        metadata=client.V1ObjectMeta(labels={"algo_id":algo_id, "version":version, "company":company}),
        spec=client.V1PodSpec(
            containers=[container],
            volumes=[
                client.V1Volume(
                    name="code-volume",
                    persistent_volume_claim=client.V1PersistentVolumeClaimVolumeSource(claim_name="tiduai-algorithm-pvc")
                )
            ]
            ),
    )
    spec = client.V1DeploymentSpec(
        replicas=1,
        template=template,
        selector=client.V1LabelSelector(
            match_labels={"algo_id":algo_id, "version":version, "company":company}
        )
    )
    try:
        deployment = client.V1Deployment(
            api_version="apps/v1",
            kind="Deployment",
            metadata=client.V1ObjectMeta(
                name=name,
                labels={"algo_id":algo_id, "version":version, "company":company}
            ),
            spec=spec,
        )

        k8sCli.create_namespaced_deployment(
            body=deployment,
            namespace=namespace
        )
    except Exception as e:
        print("create deployment error:" , e)
    
    return deployment

def delete_deployment(algo_id:str, version:str):
    name = f'svc-{algo_id}-{version.replace(".", "-")}'

    log.info(f"delete deployment {name} in {namespace} ...")
    k8sCli.delete_namespaced_deployment(
        name=name,
        namespace=namespace,
        body=client.V1DeleteOptions(
            propagation_policy="Foreground", grace_period_seconds=5
        ),
    )
    log.info(f"delete deployment {name} in {namespace} success")

#  获取deployment运行状态
def get_deployment_status(deployment_name:str, namespace:str):
    try:
        deployment = k8sCli.read_namespaced_deployment(
            name=deployment_name,
            namespace=namespace
        )
        conditions = deployment.status.conditions
        for condition in conditions:
            if condition.type in (CONDITION_PROGRESSING,CONDITION_AVAILABLE) and condition.status == "True":
                status = "success"
            else:
                status = "failed"

        return status
    except Exception as e:
        if e.status == 404:
            status = "offline"
            return status
        log.info(f"get deployment error:", e)
        return None


# 创建镜像
def image_build(base_image):
    try:
        image_name = base_image.rsplit(':', 1)[0]
        image_tag = base_image.rsplit(':', 1)[1]
        repo_url = config.get('apiserver.harbor.url')
        repo_ip = repo_url.split('//')[1].split(':')[0]
        auth_config = {
            'username': config.get('apiserver.harbor.username'),
            'password': config.get('apiserver.harbor.password'),
        }
        log.info(f'Going to build image:  {image_name}:{image_tag}')
        client = docker.DockerClient(base_url='unix://var/run/docker.sock')
        client.images.pull(repository=image_name, tag=image_tag, auth_config=auth_config)
        image = client.images.get(f'{image_name}:{image_tag}')

        if image.labels.get('watchdog') :
            print(f'Image {image_name}:{image_tag} already build ...')
            return True
        # buildargs = {"BASE_IMAGE":base_image,"REPO_URL":repo_url,"REPO_IP":repo_ip}
        buildargs = {"BASE_IMAGE":base_image}
        img = client.images.build(dockerfile=template_dockerfile,
                            path=apiserver.dockerfile_path,
                            buildargs=buildargs,
                            tag=base_image,
                            labels=image_labels)
        client.images.push(repository=image_name, tag=image_tag, auth_config=auth_config)
    except Exception as ex:
        log.warn(f'Exception while build :{ex}')
        return False
    return True

class DeploymentMonitor:
    threads = ThreadsManager()
    @classmethod
    @threads.register("deployment_monitor",daemon=True)
    def start(cls):
        cls.deployment_monitor(namespace)
    @classmethod
    def deployment_monitor(cls,namespace: str):
        try:
            watcher = watch.Watch()
            for event in  watcher.stream(k8sCli.list_namespaced_deployment, namespace=namespace):
                current_deployment = event['object']
                current_name = current_deployment.metadata.name
                current_type = event['type'] 
                if current_type == 'DELETED':
                    log.warn(f"deployment {current_name} is deleted")
                    label = current_deployment.metadata.labels
                    algo_id,version = cls.extract_id_version(current_name)
                    cls.update_algo(algo_id,version,label)
                elif current_type == 'MODIFIED':
                    log.info(f"deployment {current_name} is modified")
                    status = get_deployment_status(current_name, namespace)
                    if status == "success":
                        log.info(f"deployment {current_name} is ready")
                    elif status == "failed":
                        log.info(f"deployment {current_name} is failed")
                elif current_type == 'ADDED' :
                    log.info(f"deployment {current_name} is added")
        except Exception as e:
            log.info(f"deployment monitor error:", e)
    
    # 更新算法状态
    @classmethod
    def update_algo(cls,algo_id: str, algo_version: str, label: dict):
        log.info(f'check does algo need update')
        company=label.get('company')
        releases = AlgoRelease.get_by_id(
            company=company,
            algo=algo_id
        )
        for release in releases :
            if (release["publish_status"] == PublishStatus.pulled_down) & (algo_version == release["version"]):
                log.info(f"update algo {algo_id} version {algo_version} status already done")
                break
            else:
                log.info(f"update algo {algo_id} version {algo_version} status to pulled_down")
                update_dict = {"publish_status":"pulled_down"}
                AlgoReleaseBLL.update(
                    company=company,
                    release_id=release["id"],
                    fields=update_dict
                )
                break

    # 还原算法id、version
    @classmethod
    def extract_id_version(cls,deployment_name: str):
        parts = deployment_name.split('-')
        algo_id = parts[1]
        version_parts = parts[2:]
        version = '.'.join(version_parts)
        return algo_id, version
    