from enum import Enum


class PageConstant(Enum):
    DEFAULT_PAGE_SIZE = 10
    DEFAULT_PAGE = 1


class PageQuery:

    @staticmethod
    def query(data_cls, query, fields, order_by_key, **kwargs):
        # 分页
        page_size = kwargs.get('page_size', PageConstant.DEFAULT_PAGE_SIZE.value)
        if not page_size or page_size < 1:
            page_size = PageConstant.DEFAULT_PAGE_SIZE.value
        page = kwargs.get('page', PageConstant.DEFAULT_PAGE.value)
        if not page or page < 1:
            page = PageConstant.DEFAULT_PAGE.value
        skip = (page - 1) * page_size

        # 进行数据查询
        data = data_cls.objects(query).order_by(order_by_key)[skip:skip + page_size]

        # 获取总数
        total = data_cls.objects(query).count()
        total_pages = (total + page_size - 1) // page_size

        return {
            "data": PageQuery.results(data, fields),
            "page": page,
            "page_size": page_size,
            "total": total,
            "total_pages": total_pages
        }

    @staticmethod
    def results(data, fields):
        # 获取查询指定字段的数据
        results = []
        for d in data:
            results.append(PageQuery.result(d, fields))
        return results

    @staticmethod
    def result(data, fields):
        # 获取查询指定字段的数据
        d = dict()
        for field in fields:
            if hasattr(data, field):
                d[field] = getattr(data, field)
                if isinstance(d[field], Enum):
                    d[field] = d[field].cn_name
        return d