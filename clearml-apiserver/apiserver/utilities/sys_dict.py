from apiserver.config_repo import config
import requests

# gateway = config.get("apiserver.external.gateway")
# dict_url = config.get("apiserver.external.dict_url")


# # 获取kd的字典内容
# def get_dict_content(dict_name:str , authorization: str):
#     url = f'{gateway}{dict_url}&dictType={dict_name}'
#     try:
#         response = requests.get(url, headers = {'Authorization':authorization})
#         data = response.json()
#         dict_content = {}
#         for row in data.get('rows'):
#             dict_content[row.get('dictLabel')] = row.get('dictValue')
#     except Exception as e:
#         print(f'get_dict_content {e}')
#         return {}
#     return dict_content

# def get_algosource_by_id(source_id: str, authorization: str):
#     source_dict = get_dict_content(dict_name='algorithm_source', authorization=authorization)

#     for source_name, source_key in source_dict.items():
#         if source_key.strip() == source_id.strip():
#             return source_name
#     return None