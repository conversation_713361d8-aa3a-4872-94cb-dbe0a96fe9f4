from itertools import product
from allpairspy import AllPairs
import numpy as np
import random

# 参数设计方式-网格组合
def get_grid_params(origin_params: []):
    result = list(product(*origin_params))
    ret = []
    for arr in result:
        ret.append([x for x in arr])
    return ret

# 参数设计方式-正交实验：
def get_orthogonal_params(origin_params: []):
    ret = []
    for i, pairs in enumerate(AllPairs(origin_params)):
        ret.append(pairs)
    return ret

# 参数设计方式-随机采样
def get_random_params(origin_params: [], count: int):
    grid_params = get_grid_params(origin_params)
    if count >= len(grid_params):
        return grid_params
    indexes = random.sample([i for i in range(0, len(grid_params))], count)
    return [grid_params[i] for i in indexes]