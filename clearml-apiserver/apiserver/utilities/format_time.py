from datetime import datetime, timedelta, timezone
from apiserver.config_repo import config


log = config.logger(__file__)


class FormatTime:

    @staticmethod
    def local(local_datetime):
        return local_datetime.strftime("%Y-%m-%d %H:%M:%S")

    @staticmethod
    def utc(utc_datetime, flag=False):
        if not utc_datetime:
            return None
        try:
            current_time = datetime.now().astimezone()
            offset = current_time.utcoffset()
            if offset == timedelta(hours=8):
                local_time = utc_datetime + offset
                result = local_time.strftime("%Y-%m-%d %H:%M:%S")
            else:
                east_eight = utc_datetime.replace(tzinfo=timezone.utc).astimezone(timezone(timedelta(hours=8)))
                result = east_eight.strftime("%Y-%m-%d %H:%M:%S")
            if flag:
                result = result.split()
        except Exception as e:
            log.exception("parse utc time failed: utc_datetime={}, error={}".format(utc_datetime, e))
            result = ''
            if flag:
                result = [0, 1]
        return result

