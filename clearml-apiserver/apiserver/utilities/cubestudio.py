import hashlib
import requests
from apiserver.config_repo import config


log = config.logger(__file__)


class CubeStudioCli():
    def __init__(self):

        self.cubestudio_url = config.get("apiserver.auth.third.cubestudio.url", "http://192.168.1.49:3000")
        self.user = config.get("apiserver.auth.third.cubestudio.user", "admin")
        self.password = config.get("apiserver.auth.third.cubestudio.password", "admin")
        self.timeout = config.get("apiserver.auth.third.cubestudio.timeout", 60)

        self.headers = {
            "Content-Type": "application/json"
        }

        self.cookie = self.get_user_cookie(self.user, self.password)

    def get_user_cookie(self, user, password, is_admin=False):
        if is_admin:
            cookie = self.cookie
        else:
            cookies = self.user_login(user, password)
            log.info(f"get cubestudio cookie response {cookies}")

            cookie = ''
            for cookie_key, cookie_value in cookies.items():
                cookie += f"{cookie_key}={cookie_value};"

        return cookie

    def user_login(self, user, password):
        user_login_url = f'{self.cubestudio_url}/login'

        payload = {
            "username": user,
            "password": password
        }

        headers = self.headers.copy()
        headers["Content-Type"] = "application/x-www-form-urlencoded; charset=UTF-8"
        response = requests.post(user_login_url, data=payload, headers=headers, timeout=self.timeout, verify=False)
        log.info(f'cubestudio login cookies {requests.utils.dict_from_cookiejar(response.cookies)}')

        return requests.utils.dict_from_cookiejar(response.cookies)

    def encrypt_password(self, password):
        encode = hashlib.sha256()
        encode.update(password.encode('utf-8'))
        return encode.hexdigest()

    def get_user(self, username):
        # 从cubestudio获取用户
        user_list_url = f'{self.cubestudio_url}/users/api/'
        headers = self.headers.copy()
        cookie = self.get_user_cookie(self.user, self.password, is_admin=True)

        headers["Cookie"] = cookie

        response = requests.get(user_list_url, headers=headers, timeout=self.timeout, verify=False)

        user_list = response.json().get("result").get("data")
        for user in user_list:
            log.info(f"user {user}")
            if user.get("username") == username:
                return user
        return None

    def list_user(self, **kwargs):
        ...

    # TODO
    def create_user(self, username, password=None, email=None):
        users_url = f'{self.cubestudio_url}/users/api/'

        headers = self.headers.copy()
        cookie = self.get_user_cookie(self.user, self.password, is_admin=True)

        headers["Cookie"] = cookie

        if not email:
            # labelstudio 创建用户,必须指定邮箱
            email = f"{username}@example.com"

        body = {
            "username":username,
            "password":password,
            "email": email,
            "roles":"1"
            }

        resp = requests.post(users_url, headers=headers, json=body, verify=False, timeout=self.timeout)
        
        res_dict = resp.json()
        res_msg = res_dict.get("message") or ""
        if res_msg.startswith("User already exists with username"):
            return res_dict
        log.info(f"cubestudio cli create user: {resp.text}")
        resp.raise_for_status()
        return res_dict