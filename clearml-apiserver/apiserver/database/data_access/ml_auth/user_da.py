from datetime import datetime
from mongoengine.queryset.visitor import Q
from apiserver.config_repo import config
from apiserver.apierrors import errors
from apiserver.config.info import get_default_company
from apiserver.utilities.format_time import FormatTime
from apiserver import database
from apiserver.database.data_access.data_auth import DataAuth
from apiserver.database.model.auth import User as AuthUser
from apiserver.database.model.user import User
from apiserver.database.model.auth import Role, UserType, UserSex
from apiserver.utilities.page_query import PageQuery
from pymongo import DESCENDING, ASCENDING
from functools import reduce
import operator


log = config.logger(__file__)


class AuthUserDA:

    @staticmethod
    def list(**kwargs):
        call = kwargs.get("call", None)
        parameters = call.data

        conditions = []
        query = {}
        # 只能查非内部账号
        conditions.append(Q(user_type__ne="internal"))
        # 更新查询条件
        if parameters.get('name'):
            conditions.append((Q(name__regex=f".*{parameters.get('name')}.*") | Q(realname__regex=f".*{parameters.get('name')}.*")))
        if parameters.get('status'):
            conditions.append(Q(is_active=parameters.get('status')))
        if conditions:
            query = reduce(operator.and_, conditions)

        # 更新排序，默认排序为 created
        sort_key = kwargs.get("sort_key", "created")
        # 默认排序为降序
        sort_direction = kwargs.get("sort_direction", DESCENDING)
        if sort_direction < 0:
            order_by_key = f'-{sort_key}'
        else:
            order_by_key = f'+{sort_key}'

 
        fields = [
            AuthUser.id.name,
            AuthUser.name.name, 
            AuthUser.realname.name, 
            AuthUser.sex.name, 
            AuthUser.phone.name,
            AuthUser.email.name, 
            AuthUser.is_active.name, 
            AuthUser.last_login.name
        ]
        
        return PageQuery.query(AuthUser, query, fields, order_by_key, **parameters)

    @staticmethod
    def get_obj_list(**kwargs):
        kwargs["return_dicts"] = False
        return AuthUser.get_many(**kwargs)

    @staticmethod
    def get_count(**kwargs):
        return AuthUser.get_count(**kwargs)

    @staticmethod
    def add(**kwargs) -> str:
        data = kwargs.get("data", {})
		# 创建用户ID
        user_id = database.utils.unique_id(User)
        
        auth_user = AuthUser(
            id=user_id,
            name=data.get("name", None),
            realname=data.get("realname", None),
            password=data.get("password", None),
            sex=data.get("sex", UserSex.PRIVARY.value),
            role=data.get("role", Role.user),
            email=data.get("email", None),
            phone=data.get("phone", None),
            user_type=data.get("user_type", UserType.user),
            created=datetime.utcnow(),
            last_update=datetime.utcnow(),
            company=data.get("company", get_default_company()),
            is_active=data.get("is_active", True)
        )

        auth_user.save()
        return user_id

    @staticmethod
    def delete(**kwargs):
        obj_id = kwargs.get("obj_id", None)
        User.objects(id=obj_id).delete()
        AuthUser.objects(id=obj_id).delete()

    @staticmethod
    def update(**kwargs):
        obj_id = kwargs.get("obj_id", None)
        fields = kwargs.get("fields", {})

        fields.update({"last_update": datetime.utcnow()})
        result = AuthUser.objects(id=obj_id).update(
            **fields, full_result=True, upsert=False
        )
        return result

    @staticmethod
    def get_by_id(**kwargs):
        obj_id = kwargs.get("obj_id", None)
        return AuthUser.objects(id=obj_id).first()

    @staticmethod
    def user_detail_by_id(**kwargs):
        obj_id = kwargs.get("obj_id", None)
        obj = AuthUser.objects(id=obj_id).first()
        return AuthUserDA.parse_detail(obj)

    @staticmethod
    def user_by_name(**kwargs):
        name = kwargs.get("name", None)
        return AuthUser.objects(name=name).first()

    @staticmethod
    def parse_list(list_objs, simple=False):
        """解析列表数据"""
        tmp_list = []
        for obj in list_objs:
            item = AuthUserDA.parse_detail(obj, simple=simple)
            if not item:
                continue
            tmp_list.append(item)
        return tmp_list

    @staticmethod
    def parse_detail(obj, simple=False):

        created = obj.created
        last_update = obj.last_update
        
        item = {
            AuthUser.id.name: obj.id,
            AuthUser.name.name: obj.name,
            AuthUser.realname.name: obj.realname,
            AuthUser.sex.name: obj.sex.cn_name,
            AuthUser.password.name: "******",
            AuthUser.phone.name: obj.phone,
            AuthUser.email.name: obj.email,
            AuthUser.is_active.name: obj.is_active,
            AuthUser.created.name: created if created else None,
            AuthUser.last_update.name: last_update if last_update else None,
            AuthUser.is_reseted.name: obj.is_reseted,
        }

        if obj.locked_expired:
            item[AuthUser.locked_expired.name] = obj.locked_expired
        return item
