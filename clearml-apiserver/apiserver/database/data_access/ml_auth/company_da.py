from apiserver.config_repo import config
from apiserver import database
from apiserver.database.model.company import Company


log = config.logger(__file__)


class CompanyDA:

    @staticmethod
    def get_by_id(obj_id):
        return Company.objects(id=obj_id)

    @staticmethod
    def add(**kwargs):
        return Company(id=database.utils.id(), **kwargs).save()

    @staticmethod
    def save(obj):
        obj.save()
