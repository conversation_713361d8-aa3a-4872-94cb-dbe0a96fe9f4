from apiserver.config_repo import config
from apiserver import database
from apiserver.database.model.auth import LoginRecord


log = config.logger(__file__)


class LoginRecordDA:

    @staticmethod
    def delete_by_name(**kwargs):
        name = kwargs.get("name", None)
        LoginRecord.objects(name=name).delete()

    @staticmethod
    def get_by_sid(**kwargs):
        sid = kwargs.get("sid", None)
        return LoginRecord.objects(session_id=sid).first()

    @staticmethod
    def get_count_by_sid(**kwargs):
        sid = kwargs.get("sid", None)
        return LoginRecord.objects(session_id=sid).count()

    @staticmethod
    def add(**kwargs):
        return LoginRecord(id=database.utils.id(), **kwargs).save()

    @staticmethod
    def save(obj):
        obj.save()
