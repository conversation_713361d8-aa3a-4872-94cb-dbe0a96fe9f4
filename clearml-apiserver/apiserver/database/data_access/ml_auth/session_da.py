from apiserver.config_repo import config
from apiserver import database
from apiserver.apierrors.errors.ml_bad_request import MLError
from apiserver.database.model.auth import Session


log = config.logger(__file__)


class SessionDA:

    @staticmethod
    def save(obj):
        obj.save()

    @staticmethod
    def add(**kwargs):
        return Session(id=database.utils.id(), **kwargs).save()

    @staticmethod
    def update_by_sid(**kwargs):
        sid = kwargs.get("sid", None)
        fields = kwargs.get("fields", {})
        result = Session.objects(sid=sid).update(
            **fields, full_result=True, upsert=False
        )
        if not result.matched_count:
            raise MLError(replacement_msg="更新失败，sid不正确")
        return result

    @staticmethod
    def delete_by_user_id(**kwargs):
        user_id = kwargs.get("user_id", None)
        return Session.objects(user_id=user_id).delete()

    @staticmethod
    def list_by_user_id(**kwargs):
        user_id = kwargs.get("user_id", None)
        return Session.objects(user_id=user_id).all()

    @staticmethod
    def get_by_user_id(**kwargs):
        user_id = kwargs.get("user_id", None)
        return Session.objects(user_id=user_id).first()

    @staticmethod
    def get_by_sid(**kwargs):
        sid = kwargs.get("sid", None)
        return Session.objects(sid=sid).first()
