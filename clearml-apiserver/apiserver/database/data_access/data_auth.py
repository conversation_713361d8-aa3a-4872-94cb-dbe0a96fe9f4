from mongoengine.queryset.visitor import Q
from apiserver.config_repo import config
from apiserver.apierrors.errors.ml_bad_request import MLError
from apiserver.database.model.auth import Role


log = config.logger(__file__)


class DataAuth:

    @staticmethod
    def auth_list(
            identity=None,
            q=None,
            key=None,
            value=None,
            enable=True,
            is_public=True
    ):
        """按用户过滤查询集, 查询资源列表时使用"""
        if enable is True and identity.role == Role.user:
            k = "user"
            v = identity.user
            if key:
                k = key
            if value:
                v = value
            _q = Q(**{k: v})
            if is_public is True:
                _q |= Q(is_public__in=["public", "preset"])
            q &= _q
        log.info("data auth list enable={}, query={}".format(enable, q))
        return q

    @staticmethod
    def auth_own(
            identity=None,
            obj_id=None,
            model=None,
            enable=True
    ):
        if enable is True and identity.role == Role.user:
            obj = model.objects(id=obj_id).first()
            if not obj:
                raise MLError(
                    replacement_msg="资源不存在"
                )
            if obj and identity.user != obj.user:
                raise MLError(
                    code=403,
                    sub_code=403,
                    replacement_msg="不是自己的资源"
                )
