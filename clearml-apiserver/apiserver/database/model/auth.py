from mongoengine import (
    <PERSON><PERSON>ield,
    EmbeddedDocument,
    EmbeddedDocumentListField,
    EmailField,
    DateTimeField,
    IntField,
    BooleanField,
    EnumField
)

from apiserver.database import Database, strict
from apiserver.database.model import DbModelMixin
from apiserver.database.model.base import AuthDocument
from apiserver.database.utils import get_options
from apiserver.utilities.enumerate import EnumMapName

class Entities(object):
    company = "company"
    task = "task"
    user = "user"
    model = "model"


class Role(object):
    system = "system"
    """ Internal system component """
    root = "root"
    """ Root admin (person) """
    admin = "admin"
    """ Company administrator """
    superuser = "superuser"
    """ Company super user """
    user = "user"
    """ Company user """
    annotator = "annotator"
    """ Annotator with limited access"""
    guest = "guest"
    """ Guest user. Read Only."""

    @classmethod
    def get_system_roles(cls) -> set:
        return {cls.system, cls.root}

    @classmethod
    def get_company_roles(cls) -> set:
        return set(get_options(cls)) - cls.get_system_roles()


class UserType(object):
    internal = "internal"
    admin = "admin"
    user = "user"
    

class UserSex(EnumMapName):
    MALE = ("male", "男")		# 男
    FEMALE = ("female", "女")	# 女
    PRIVARY = ("privary", "")   # 保密(不填)

    @classmethod
    def is_member(cls, val):
        return  val in  [cls.MALE.value, cls.FEMALE.value, cls.PRIVARY.value]


class Credentials(EmbeddedDocument):
    meta = {"strict": False}
    key = StringField(required=True)
    secret = StringField(required=True)
    label = StringField()
    last_used = DateTimeField()
    last_used_from = StringField()


class User(DbModelMixin, AuthDocument):
    meta = {"db_alias": Database.auth, "strict": strict}

    id = StringField(primary_key=True)
    # 用户名(用于账号登录)
    name = StringField(unique=True)
    # 用户真实姓名
    realname = StringField()
    # 密码
    password = StringField(required=True)
	# 性别
    sex = EnumField(UserSex, default=UserSex.PRIVARY)
    # 电话
    phone = StringField()
	# 用户类型
    user_type = StringField(required=True, choices=get_options(UserType), default=UserType.user)

    created = DateTimeField()
    """ User auth entry creation time """
    last_update = DateTimeField()

    validated = DateTimeField()
    """ Last validation (login) time """

    role = StringField(required=True, choices=get_options(Role), default=Role.user)
    """ User role """

    company = StringField(required=True)
    """ Company this user belongs to """

    credentials = EmbeddedDocumentListField(Credentials, default=list)
    """ Credentials generated for this user """

    email = StringField()
    """ Email uniquely identifying the user """

    is_reseted = BooleanField(default=True)
    is_active = BooleanField(default=True)
    is_locked = BooleanField()
    locked_expired = DateTimeField()
    last_login = DateTimeField()
    last_active = DateTimeField()

    cubestudio_cookie = StringField()

class LoginRecord(DbModelMixin, AuthDocument):
    meta = {
        "db_alias": Database.auth,
        "strict": strict
    }

    id = StringField(primary_key=True)
    name = StringField(required=True)
    session_id = StringField(required=True)
    captcha = StringField()
    captcha_2fa = StringField()
    expired = DateTimeField()
    failed_count = IntField()


class Session(DbModelMixin, AuthDocument):
    meta = {
        "db_alias": Database.auth,
        "strict": strict
    }

    id = StringField(primary_key=True)
    sso_ticket = StringField()
    sso_session_id = StringField()
    sid = StringField(unique=True)
    user_id = StringField()
    from_ip = StringField()
    expired = DateTimeField()
