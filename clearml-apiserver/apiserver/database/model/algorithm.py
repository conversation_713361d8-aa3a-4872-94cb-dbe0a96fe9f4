from mongoengine import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>Field,
    IntField,
    BooleanField,
    ValidationError,
)

from apiserver.database import Database, strict
from apiserver.database.fields import StrippedString<PERSON>ield, SafeSortedListField
from apiserver.database.model import AttributedDocument
from apiserver.database.model.base import GetMixin
from enum import Enum


class AlgorithmType(Enum):
    Image = "image"  # 图像分类
    Object = "object"  # 物体检测
    Instance = "instance"  # 实例分割
    Txt = "txt"  # 文本分类
    ShortText = "short_txt"  # 短文本相似度
    Text_entity = "txt_entity"  # 文本实体抽取
    Sequence = "sequence"  # 序列标注
    Table = "table"  # 表格数据预测
    Other = "other"  # 其他


class ModelFormat(Enum):
    SaveModle = "save_modle"
    Frozanpb = "frozan_pb"
    KerasH5 = "keras_h5"
    CaffePrototxt = "caffe_prototxt"
    ONNX = "onnx"
    BladeModle = "blade_modle"
    PMML = "pmml"
    Other = "other"
    none = ""


class Is_publish(Enum):
    Publish = "public"
    Private = "private"
    Preset = "preset"


class Algorithm(AttributedDocument):

    get_all_query_options = GetMixin.QueryParameterOptions(
        pattern_fields=("name", "user", "id", "description", "is_inference"),
        list_fields=("algorithm_type", "is_public", "size", "versions_num"),
        range_fields=("last_update",),
    )

    meta = {
        "db_alias": Database.backend,
        "strict": strict,
        "indexes": [
            "size",
            "versions_num",
            ("name", "id"),
            ("name", "description"),
            {
                "name": "%s.dataset.main_text_index" % Database.backend,
                "fields": ["$name", "$id", "$description"],
                "default_language": "english",
                "weights": {"name": 10, "id": 10, "description": 10},
            },
        ],
    }

    id = StringField(primary_key=True)
    name = StrippedStringField(
        required=True,
        min_length=3,
        sparse=True,
    )
    user = StringField()
    description = StringField()
    algorithm_type = StringField(required=True)
    is_public = StringField(required=True)
    size = StringField()
    versions_num = IntField(default=1)
    last_update = DateTimeField()
    created = DateTimeField(required=True)
    is_inference = BooleanField(required=True)
    image = StringField(required=True)
    model_format = StringField()
