
from apiserver.database.model import AttributedDocument
from apiserver.database import Database, strict
from apiserver.database.fields import Stripped<PERSON><PERSON><PERSON>ield, SafeSortedList<PERSON>ield
from mongoengine import StringField, DateTimeField, IntField, ListField, BooleanField
from apiserver.database.utils import get_options
from apiserver.database.model.base import GetMixin
from enum import Enum

class IdeStatus(object):
    tostart = "tostart"         #待启动
    starting = "starting"       #启动中
    running = "running"         #运行中，不可用
    available = "available"     #运行中，可用 
    stopping = "stopping"       #停止中
    stopped = "stopped"         #已停止
    failed = "failed"           #启动失败
    deleting = "deleting"       #删除中
    unknown = "unknown"         #未知

class Is_publish(Enum):
    Publish = "public"
    Private = "private"
    Preset  = "preset"

class Ide(AttributedDocument):

    get_all_query_options = GetMixin.QueryParameterOptions(
        pattern_fields=("name", "basename", "description", "url_vscode", "url_jupyter"),
        list_fields=("tags",  "id", "status", "user","tags","is_public"),
        range_fields=("last_update",),
        datetime_fields=("last_update"),
    )

    meta = {
        "db_alias": Database.backend,
        "strict": strict,
        "indexes": [
             "created",
             "last_update",
             "status",
             "company",
             ("company", "name"),
             ("company", "user"),
             ("company", "status"),
             ("status", "last_update"),
             {
                "name": "%s.ide.main_text_index" % Database.backend,
                "fields": ["$name", "$id", "$description"],
                "default_language": "english",
                "weights": {"name": 10, "id": 10, "description": 10},
            },
        ]
    }
    id = StringField(primary_key=True)

    name = StrippedStringField(user_set_allowed=True,required=True)
    user = StringField()
    description = StringField(user_set_allowed=True)
    created = DateTimeField(required=True)
    # tags = SafeSortedListField(StringField(required=True))
    last_update = DateTimeField()
    status = StringField(user_set_allowed=True,default=IdeStatus.tostart, choices=get_options(IdeStatus))
    url_vscode = StringField(user_set_allowed=True)                                 #只保留IP:PORT后面的内容
    url_jupyter = StringField(user_set_allowed=True)                                #只保留IP:PORT后面的内容
    algorithms = ListField(StringField(user_set_allowed=True,required=True))        #git仓库名
    cpu = IntField(user_set_allowed=True,required=True)
    mem = IntField(user_set_allowed=True,required=True)
    gpu = IntField(user_set_allowed=True,required=True)
    image = StringField(user_set_allowed=True, required=True)
    is_public = StringField(required=True)
    stop_time = DateTimeField()                                                     #自动停止时间（创建、启动的接口设置）
    resourceId = IntField(required=True)
    dataset_path = StringField(required=False)                                      #生成xml的存储路径（挂载到 /data/dataset下）