from mongoengine import (
    <PERSON>Field,
    DateTimeField,
    IntField,
    ListField,
    EnumField,
    EmbeddedDocument,
    EmbeddedDocumentField,
    FloatField,
    BooleanField,
    EmbeddedDocumentListField
)
from apiserver.database import Database, strict
from apiserver.database.fields import (
    StrippedStringField,
    SafeSortedListField,
    DictField,
    SafeMapField,
)
from apiserver.database.model import AttributedDocument
from apiserver.database.model.base import GetMixin, ProperDictMixin
from enum import Enum


class Type(Enum):
    normal = "normal"  # 普通
    qa = "qa"  # 问答
    cmp = "cmp"  # 对比

class ParamCmbType(Enum):
    GRID_SEARCH = "grid"            # 网格搜索
    ORTHOGONAL  = "orthogonal"      # 正交实验
    RANDOM_COMB = "random"          # 随机搜索

class Status(Enum):
    created = "created"  # 未开始
    pending = "pending"  # 待运行
    running = "running"  # 运行中
    finished = "finished"  # 运行成功
    failed = "failed"  # 运行失败
    stopped = "stopped"  # 已停止


class ParamsItem(EmbeddedDocument, ProperDictMixin):
    section = StringField()
    name = StringField()
    value = StringField()
    type = StringField()
    description = StringField()
    # _cls = StringField()

class DataScene(EmbeddedDocument):
    id = StringField()
    scenarioCode = StringField()
    scenarioName = StringField()
    generation = IntField(default=1)  # 训练代数，用于SS数据更新训练时，区分每一次的训练任务

class HistoryData(EmbeddedDocument):
    id = ListField()
    scene = ListField(default=list)
    disable = BooleanField(default=False)

class LiveData(EmbeddedDocument):
    # scene可以直接不处理了，SS数据作为一个默认的数据
    scene = EmbeddedDocumentListField(DataScene, default=list)
    max_access_time = IntField(default=1)
    # time = IntField()
    disable = BooleanField(default=False)
    generation = IntField(default=1)        # TODO：去掉这个字段

class Scene(EmbeddedDocument):
    dataset_id = StringField()
    dataset_name = StringField()
    xml_path = StringField()

class MockData(EmbeddedDocument):
    scene = EmbeddedDocumentListField(Scene, default=list)
    disable = BooleanField(default=False)
    create_dataset_finished = BooleanField(default=False)


class Satellite(EmbeddedDocument):
    id = ListField(default=[1, 2, 4, 6])
    disable = BooleanField(default=False)


class GroundStation(EmbeddedDocument):
    id = ListField(default=[1])
    disable = BooleanField(default=False)


class Args(EmbeddedDocument):
    Args = DictField(
        default={
            "temperature": {
                "name": "num_vehicles",
                "value": "3",
                "description": "车辆数量",
                "section": "Args",
                "type": "",
                "min": "1",
                "max": "3",
                "disable":False
            },
            "depot": {
                "name": "depot",
                "value": "2",
                "description": "出发返回点",
                "section": "Args",
                "type": "",
                "min": "0",
                "max": "1",
                "disable":False
            },
        }
    )


class Resources(EmbeddedDocument):
    satellite = EmbeddedDocumentField(Satellite, default=Satellite)
    ground_station = EmbeddedDocumentField(GroundStation, default=GroundStation)


class Algo(EmbeddedDocument):
    algo_id = StringField()
    version = StringField()
    hyperparams = EmbeddedDocumentField(Args)


class Configuration(EmbeddedDocument):
    max_qa_num = IntField(default=0)
    max_qa_time = IntField(default=0)
    # 基础算法 对比训练：原始算法    问答训练：选用算法   普通：None
    algo = EmbeddedDocumentField(Algo, default=Algo) 
    # 算法列表 对比训练：用于对比的算法列表   普通训练：所有算法列表  问答训练：None
    algos = EmbeddedDocumentListField(Algo, default=list)
    constraint_setting = ListField(default=[1])
    dx_addon = ListField(default=[1, 2])
    xz_addon = ListField(default=[2, 3])


class AlgoParameterRange(EmbeddedDocument):
    min = FloatField(default=0)
    max = FloatField(default=999999)


class Start(EmbeddedDocument):
    evolution = IntField(default=1)
    resource = StringField(default="")


class End(EmbeddedDocument):
    addon_error = IntField(default=1)
    run_error = IntField(default=1)


class TrainData(EmbeddedDocument):
    history_data = EmbeddedDocumentField(HistoryData, default=HistoryData)
    live_data = EmbeddedDocumentField(LiveData, default=LiveData)
    mock_data = EmbeddedDocumentField(MockData, default=MockData)


class EvaluationCriterion(EmbeddedDocument):
        id = IntField()
        weight = FloatField()

class config_list(EmbeddedDocument):
    start = EmbeddedDocumentField(Start, default=Start)
    train_data = EmbeddedDocumentField(TrainData, default=TrainData)
    train_resources = EmbeddedDocumentField(Resources, default=Resources)
    configuration = EmbeddedDocumentField(Configuration, default=Configuration)
    evaluation_criterion = ListField(default=[{'id':3, 'weight':40},{'id':1, 'weight':30},{'id':2, 'weight':30}])
    end = EmbeddedDocumentField(End, default=End)

class Project(AttributedDocument):

    get_all_query_options = GetMixin.QueryParameterOptions(
        pattern_fields=("name", "basename", "description"),
        list_fields=("tags", "system_tags", "id", "parent", "path", "type", "status"),
        range_fields=("last_update",),
    )

    meta = {
        "db_alias": Database.backend,
        "strict": strict,
        "indexes": [
            "parent",
            "path",
            ("company", "name"),
            ("company", "basename"),
            {
                "name": "%s.project.main_text_index" % Database.backend,
                "fields": ["$name", "$id", "$description"],
                "default_language": "english",
                "weights": {"name": 10, "id": 10, "description": 10},
            },
        ],
    }

    id = StringField(primary_key=True)
    name = StrippedStringField(
        required=True,
        unique_with=AttributedDocument.company.name,
        min_length=3,
        sparse=True,
    )
    basename = StrippedStringField(required=True)
    description = StringField()
    created = DateTimeField(required=True)
    tags = SafeSortedListField(StringField(required=True))
    system_tags = SafeSortedListField(StringField(required=True))
    default_output_destination = StrippedStringField()
    last_update = DateTimeField()
    featured = IntField(default=9999)
    logo_url = StringField()
    logo_blob = StringField(exclude_by_default=True)
    company_origin = StringField(exclude_by_default=True)
    parent = StringField(reference_field="Project")
    path = ListField(StringField(required=True), exclude_by_default=True)
    started = DateTimeField()
    finished = DateTimeField()
    type = EnumField(Type, default=Type.normal)
    status = EnumField(Status, default=Status.created)
    configs = EmbeddedDocumentField(config_list, default=config_list)
    template = StringField(default="")
    conclusion = StringField(default="")
    algo_type = StringField(default="")
    algo_category = StringField(default="")
    duration = IntField(default=24)         # SS数据的有效时段（默认24h，启动时间后的duration时间段内有效）