from mongoengine import (
    <PERSON><PERSON><PERSON>,
    DateT<PERSON><PERSON><PERSON>,
    Enum<PERSON>ield,
    EmbeddedD<PERSON>ument<PERSON>ield,
    DictField
)
from apiserver.database import Database, strict
from apiserver.database.model import AttributedDocument
from apiserver.database.model.base import GetMixin, ProperDictMixin
from enum import Enum

class Type(Enum):
    normal = "normal"  # 普通
    qa = "qa"  # 问答
    cmp = "cmp"  # 对比

class Template(AttributedDocument):

    get_all_query_options = GetMixin.QueryParameterOptions(
        list_fields=("type", "name"),
        range_fields=("last_update",),
    )

    meta = {
        "db_alias": Database.backend,
        "collection": 'template',
        "strict": strict,
    }
    id = StringField(primary_key=True)
    created = DateTimeField(required=True)
    type = EnumField(Type)
    configs = DictField()
    last_update = DateTimeField()
    name = StringField(required=True)