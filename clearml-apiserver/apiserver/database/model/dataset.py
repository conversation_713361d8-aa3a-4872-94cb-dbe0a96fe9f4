import time
from datetime import datetime
from enum import Enum

from mongoengine import <PERSON>Field, DateTimeField, IntField, BooleanField, ValidationError, EnumField, ReferenceField, <PERSON><PERSON>ield
from mongoengine import Document, Q
from pymongo import MongoClient, ASCENDING, DESCENDING

from apiserver.database import Database, strict
from apiserver.database.fields import Stripped<PERSON>tring<PERSON>ield, SafeSortedListField
from apiserver.database.model import AttributedDocument
from apiserver.database.model.base import GetMixin
from apiserver.utilities.enumerate import EnumMapName


class DatasetType(Enum):
    IMAGE = "image"
    TEXT = "txt"
    AUDIO = "audio"
    VIDEO = "video"
    Table = "table"
    Other = "other"


class Is_publish(Enum):
    Publish = "public"
    Private = "private"
    Preset  = "preset"


class Dataset(AttributedDocument):

    get_all_query_options = GetMixin.QueryParameterOptions(
        pattern_fields=("name", "user", "id", "description"),
        list_fields=("dataset_type", "is_public", "size", "versions_num"),
        range_fields=("last_update",),
    )

    meta = {
        "db_alias": Database.backend,
        "strict": strict,
        "indexes": [
            "size",
            "versions_num",
            ("name", "id"),
            ("name", "description"),
            {
                "name": "%s.dataset.main_text_index" % Database.backend,
                "fields": ["$name", "$id", "$description"],
                "default_language": "english",
                "weights": {"name": 10, "id": 10, "description": 10},
            },
        ],
    }

    id = StringField(primary_key=True)
    name = StrippedStringField(
        required=True,
        min_length=3,
        # unique_with=AttributedDocument.company.name,
        sparse=True,
    )
    user = StringField()
    description = StringField()
    dataset_type = StringField(required=True)
    is_public = StringField(required=True)
    size = StringField()
    versions_num = IntField(default=1)
    last_update = DateTimeField()
    created = DateTimeField(required=True)




class DatasetsKind(EnumMapName):
    IMAGE = ("image", "图片")
    TEXT = ("txt", "文本")
    AUDIO = ("audio", "音频")
    VIDEO = ("video", "视频")
    Table = ("table", "表格")
    Other = ("other", "其他")
    MIXTURE = ("mixture", "混合数据")


class Datasets(Document):
    meta = {
        "db_alias": Database.backend,
        "strict": strict,
        "indexes": [
            ("name"),
            ("name", "kind"),
            ("name", "kind", "algorithm_id"),
            {
                "name": "%s.dataset.main_text_index" % Database.backend,
                "fields": ["$name", "$kind", "$algorithm_id"],
                "default_language": "english",
                "weights": {"name": 10, "kind": 10, "algorithm_id": 10},
            },
        ],
    }

    id = StringField(primary_key=True)
    name = StrippedStringField(
        required=True,
        min_length=3,
        # unique_with=AttributedDocument.company.name,
        sparse=True,
    )
    description = StringField()
    user_id = IntField()

    kind = EnumField(DatasetsKind, default=DatasetsKind.MIXTURE)
    size = StringField()
    # 基线ID
    base_id = StringField(required=True)
    algorithm_id = StringField(required=True)
    algorithm_name = StringField()

    @property
    def version_num(self) -> int:
        return DatasetsVersion.objects(dataset_id=self.id).count()

    created = IntField(default=datetime.now().timestamp)
    updated = IntField(default=datetime.now().timestamp)


class DatasetsVersion(Document):

    # get_all_query_options = GetMixin.QueryParameterOptions(
    #     pattern_fields=("name", "user", "id", "description"),
    #     list_fields=("kind", "size"),
    #     range_fields=("created",),
    # )

    meta = {
        "db_alias": Database.backend,
        "strict": strict,
        "indexes": [
            ("id", "version"),
            ("version", "dataset_id"),
            {
                "name": "%s.dataset.main_text_index" % Database.backend,
                "fields": ["$id", "$version", "$dataset_id"],
                "default_language": "english",
                "weights": {"id": 10, "version": 10, "dataset_id": 10},
            },
        ],
    }

    id = StringField(primary_key=True)
    version = StrippedStringField(
        required=True,
    )
    dataset_id = StringField()
    created = IntField(default=datetime.now().timestamp)
    updated = IntField(default=datetime.now().timestamp)


class VersionFiles(Document):
    #
    # get_all_query_options = GetMixin.QueryParameterOptions(
    #     pattern_fields=("name", "user", "id", "description"),
    #     list_fields=("kind", "size"),
    #     range_fields=("created",),
    # )

    meta = {
        "db_alias": Database.backend,
        "strict": strict,
        "indexes": [
            ("id", "version_id"),
            {
                "name": "%s.dataset.main_text_index" % Database.backend,
                "fields": ["$id", "$version_id"],
                "default_language": "english",
                "weights": {"id": 10, "version_id": 10},
            },
        ],
    }

    id = StringField(primary_key=True)
    version_id = StringField(required=True)
    filename = StringField(required=True)
    filesize = StringField(required=True)
    created = IntField(default=datetime.now().timestamp)
    updated = IntField(default=datetime.now().timestamp)