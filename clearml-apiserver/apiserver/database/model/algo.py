from mongoengine import (
    <PERSON><PERSON>ield,
    <PERSON><PERSON><PERSON>Field,
    BooleanField,
    <PERSON>loatField,
    IntField,
    EnumField,
    EmbeddedDocument,
    ListField,
    Document
)
from apiserver.database.errors import translate_errors_context
from enum import Enum
from apiserver.database.fields import (SafeDictField)
from apiserver.database.model import AttributedDocument
from apiserver.database.model.base import DbModelMixin, ABSTRACT_FLAG
from apiserver.database import Database, strict
from apiserver.database.model.base import ProperDictMixin, GetMixin

class PublishStatus(Enum):
    published = "published"  #已上线
    pulled_down = "pulled_down" #已下线
    no_verified = "pulled_down" #未验证
    no_pass_baseline = "pulled_down" #未上线

class IOEntry(EmbeddedDocument):
    ...

class AlgoType(AttributedDocument):
    """ 算法类型 """

    meta = {
        "db_alias": Database.backend,
        "collection": 'algo_type',
        "strict": strict,
        'indexes': ['$name']
    }
    id = StringField(primary_key=True)
    name = StringField(required=True, unique=True)
    disabled = <PERSON>oleanField(default=False)
    created = DateTimeField(required=True)
    last_update = DateTimeField()
    type = StringField(required=True, unique=True)

class AlgoCategory(AttributedDocument):
    """ 算法类别 """

    meta = {
        "db_alias": Database.backend,
        "collection": 'algo_category',
        "strict": strict,
        'indexes': ['$name']
    }
    id = StringField(primary_key=True)
    name = StringField(required=True, unique=True)
    disabled = BooleanField(default=False)
    created = DateTimeField(required=True)
    last_update = DateTimeField()
    type = StringField(required=True, unique=True)

class AlgoSource(AttributedDocument):
    """ 算法来源 """

    meta = {
        "db_alias": Database.backend,
        "collection": 'algo_source',
        "strict": strict,
        'indexes': ['$name']
    }
    id = StringField(primary_key=True)
    name = StringField(required=True, unique=True)
    disabled = BooleanField(default=False)
    created = DateTimeField(required=True)
    last_update = DateTimeField()

class Algo(AttributedDocument):
    """ 算法 """

    meta = {
        "db_alias": Database.backend,
        "collection": 'algo',
        "strict": strict,
        'indexes': [
            "created",
            "last_update",
            {
                "name": "%s.algo.main_text_index" % Database.backend,
                "fields": ["$name"],
                "default_language": "english",
                "weights": {"name": 10, "title": 10,},
            },
        ],
    }

    get_all_query_options = GetMixin.QueryParameterOptions(
        list_fields=(
            "id",
            "user",
            "name",
            "type",
            "category",
            "source"
        ),
    )

    id = StringField(primary_key=True)
    name = StringField(required=True, unique=True)
    # type = StringField(required=True, reference_field=AlgoType)
    # category = StringField(required=True, reference_field=AlgoCategory)
    created = DateTimeField(required=True)
    last_update = DateTimeField()
    label_cmds = ListField(StringField())
    baseline_dataset = StringField()

    @classmethod
    def get_by_id(cls, algo: str, company: str):
        with translate_errors_context():
            query = dict(id=algo, company=company)
        algo = Algo.objects(**query).first()
        return algo

class AlgoRelease(AttributedDocument):
    """ 算法版本 """

    meta = {
        "db_alias": Database.backend,
        "collection": 'algo_release',
        "strict": strict,
        'indexes': [
            "algo",
            "version",
            "created",
            "last_update"
        ],
    }
    get_all_query_options = GetMixin.QueryParameterOptions(
        list_fields=(
            "id",
            "algo",
            "publish_status",
            "version"
        ),
        fields=("is_baseline", "from_project"),
    )

    id = StringField(primary_key=True)
    algo = StringField(required=True, reference_field=Algo)
    version = StringField(required=True)
    args = SafeDictField()
    exec_config = SafeDictField()
    publish_status = EnumField(PublishStatus, default=PublishStatus.no_verified)
    description = StringField(default="")
    created = DateTimeField(required=True)
    last_update = DateTimeField()
    current = BooleanField(default=False)
    @classmethod
    def get_by_id(cls, algo: str, company: str):
        with translate_errors_context():
            query = dict(algo=algo, company=company)
        algo = AlgoRelease.objects(**query)
        return algo

# class Baseline(AttributedDocument):
#     """ 算法基线. 每个记录对应一个算法类型下某个数据集的推理结果数据 """

#     meta = {
#         "db_alias": Database.backend,
#         "collection": 'baseline',
#         "strict": strict,
#         'indexes': [
#             "dataset",
#             "algo_release",
#             "created",
#             "last_update"
#         ],
#     }
#     id = StringField(primary_key=True)
#     algo_type = StringField(required=True, reference_field=AlgoType)
#     dataset = StringField(required=True)
#     complete_ratio = FloatField(required=True)
#     complete_duration = IntField(required=True)
#     algo_release = StringField(required=True, reference_field=AlgoRelease)
#     created = DateTimeField(required=True)
#     last_update = DateTimeField()

class AlgoRun(AttributedDocument):
    """ 算法版本执行记录. """

    meta = {
        "db_alias": Database.backend,
        "collection": 'algo_run',
        "strict": strict,
        'indexes': [
            "dataset",
            "algo_release",
            "created",
        ],
    }
    get_all_query_options = GetMixin.QueryParameterOptions(
        list_fields=(
            "id",
            "user",
            "algo",
            "algo_release",
            "dataset",
            "complete_ratio",
            "complete_duration",
            "scheme_score"
        ),
    )
    id = StringField(primary_key=True)
    dataset = StringField(required=True)
    algo = StringField(required=True, reference_field=Algo)
    algo_release = StringField(required=True, reference_field=AlgoRelease)
    complete_ratio = FloatField(required=True)
    complete_duration = FloatField(required=True)
    scheme_score = FloatField(required=True)
    created = DateTimeField(required=True)