from apiserver.database.model import AttributedDocument
from apiserver.database.fields import Stripped<PERSON><PERSON><PERSON>ield, SafeSorted<PERSON>ist<PERSON>ield
from mongoengine import StringField, DateTimeField, IntField, ListField, <PERSON>oleanField
from apiserver.database import Database, strict

class ResourceSpec(AttributedDocument):
    meta = {
        "db_alias": Database.backend,
        "collection": 'resource_spec',
        "strict": strict,
        'indexes': ['$name']
    }
    id = StringField(primary_key=True)
    name = StrippedStringField(user_set_allowed=True,required=True)
    cpu = IntField(user_set_allowed=True,required=True)
    mem = IntField(user_set_allowed=True,required=True)
    user = StringField()