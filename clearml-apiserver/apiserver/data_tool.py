#!/usr/bin/python3
"""
Export/import experiments and all referenced objects to/from JSON format
"""
import base64
from argparse import ArgumentParser, Namespace, FileType
from pathlib import Path
from typing import Tuple, Any, Sequence

import boto3


def _s3_combine(*parts) -> str:
    return "/".join(filter(None, parts))


def _s3_get_bucket(bucket_path: str) -> Tuple[Any, str]:
    bucket_name, _, path = bucket_path.partition("/")
    bucket = boto3.resource("s3").Bucket(bucket_name)
    return bucket, path.strip("/")


def upload_files(upload_bucket: str, filename_prefix: str, files: Sequence[str]):
    bucket, path = _s3_get_bucket(upload_bucket)

    # do not upload files if they are already there
    already_uploaded = set()
    for file in files:
        key = _s3_combine(path, Path(file).name)
        if any(existing.key == key for existing in bucket.objects.filter(Prefix=key)):
            print(f"File {bucket.name}/{key} already exists")
            already_uploaded.add(file)
    if set(files) == already_uploaded:
        print("Nothing to upload")
        return

    # backup exiting files
    key_prefix = _s3_combine(path, filename_prefix)
    back_path = _s3_combine(path, "back")
    for existing_file in bucket.objects.filter(Prefix=key_prefix):
        print(f"Backing up {existing_file.key}")
        _, _, existing_name = existing_file.key.rpartition("/")
        backup_key = _s3_combine(back_path, existing_name)
        bucket.Object(backup_key).copy_from(
            CopySource=f"{bucket.name}/{existing_file.key}"
        )
        existing_file.delete()

    # upload new files
    for file in files:
        print(f"Uploading {file}")
        key = _s3_combine(path, Path(file).name)
        bucket.upload_file(file, key)

    # delete back files
    print(f"Deleting back up files from {back_path}")
    bucket.objects.filter(Prefix=back_path).delete()


def do_download(download_bucket: str, folder: Path, new_only: bool) -> Sequence[Path]:
    bucket, path = _s3_get_bucket(download_bucket)
    downloaded = []
    for file in bucket.objects.filter(Prefix=path):
        file_key, _, file_name = file.key.rpartition("/")
        if file_key != path:  # do not copy from sub folders
            continue
        file_path = folder / file_name
        if file_path.exists():
            print(f"The file {str(file_path)} already exists")
            if not new_only:
                downloaded.append(file_path)
            continue
        print(f"Downloading file {file.key} into {str(file_path)}")
        bucket.download_file(file.key, str(file_path))
        downloaded.append(file_path)

    return downloaded


class _EncodedBase64FileContents(FileType):
    def __call__(self, string):
        file = super(_EncodedBase64FileContents, self).__call__(string)
        return base64.b64encode(file.read())


def setup_export(parser: ArgumentParser):
    parser.add_argument(
        "--experiments", "-E", nargs="+", help="IDs of experiments to be exported"
    )
    parser.add_argument(
        "--projects", "-P", nargs="+", help="IDs or names of experiments to be exported"
    )
    parser.add_argument(
        "--statuses",
        "-S",
        nargs="+",
        default=["published"],
        help="Only tasks with these statuses will be exported",
    )
    parser.add_argument(
        "--output",
        "-O",
        type=str,
        default="export.zip",
        help="Output zip file (default is %(default)s)",
    )
    parser.add_argument(
        "--files",
        "-F",
        type=str,
        default="/mnt/fileserver",
        help="Path to the root folder for storing user files",
    )
    parser.add_argument(
        "--tag-exported",
        "-T",
        action="store_true",
        default=False,
        help="If set then export tag is added to the exported entities",
    )
    parser.add_argument(
        "--no-events",
        "-Ne",
        action="store_true",
        default=False,
        help="If set then task and model events and artifacts are not exported",
    )
    parser.add_argument(
        "--export-users",
        "-Eu",
        action="store_true",
        default=False,
        help="If set then users are exported",
    )
    parser.add_argument(
        "--upload",
        "-U",
        type=str,
        default="",
        help="Name of the s3 bucket to upload data to",
    )
    metadata_group = parser.add_argument_group("Metadata")
    metadata_group.add_argument(
        "--user-id",
        "-Ui",
        help="Override target user ID to use for the entire package",
    )
    metadata_group.add_argument(
        "--user-name", "-Un", help="Override target user name to use for this package",
    )
    metadata_group.add_argument(
        "--project-name",
        "-Pn",
        help="Override target project name to use for this package",
    )
    ids_group = metadata_group.add_mutually_exclusive_group()
    ids_group.add_argument(
        "--new-ids",
        "-Ni",
        action="store_true",
        default=False,
        help="Create new ids for all the entities in the package",
    )
    ids_group.add_argument(
        "--example-ids",
        "-Ei",
        action="store_true",
        default=False,
        help="Prefix all the ids with ex- prefix",
    )
    ids_group.add_argument(
        "--private-ids",
        "-Pi",
        action="store_true",
        default=False,
        help="Unprefix all the ex- ids",
    )
    public_group = metadata_group.add_mutually_exclusive_group()
    public_group.add_argument("--public", action="store_true", help="Export for all companies", default=True)
    public_group.add_argument("--private", action="store_false", dest="public", help="Export for a specific company")

    logo = metadata_group.add_mutually_exclusive_group()
    logo.add_argument(
        "--logo-url", help="Logo URL to include in this package",
    )
    logo.add_argument(
        "--logo-file",
        dest="logo_blob",
        type=_EncodedBase64FileContents(),
        help="Logo file to embed in this package",
    )

    def do_export(p: ArgumentParser, args: Namespace):
        if not (args.experiments or args.projects):
            p.error("Must provide projects or experiments")

        metadata = {
            a.dest: getattr(args, a.dest, None)
            for a in metadata_group._group_actions
            if getattr(args, a.dest, None) is not None
        }

        from apiserver.database import db

        db.initialize()

        from apiserver.mongo.initialize import PrePopulate

        created_files = PrePopulate.export_to_zip(
            filename=args.output,
            experiments=args.experiments,
            projects=args.projects,
            artifacts_path=args.files,
            task_statuses=args.statuses,
            tag_exported_entities=args.tag_exported,
            export_events=not args.no_events,
            export_users=args.export_users,
            metadata=metadata,
        )

        if args.upload and created_files:
            upload_files(args.upload, Path(args.output).stem, created_files)

    parser.set_defaults(func=do_export)


def _get_input_files(args: Namespace, new_only=True) -> Sequence[Path]:
    """
    Get a file or list of zip files from the passed args.input
    Download files from aws if args.download is set
    """
    path = Path(args.input)
    if not args.download:
        return path.glob("*.zip") if path.is_dir() else [path]

    if not path.exists():
        path.mkdir(parents=True, exist_ok=True)
    elif not path.is_dir():
        print("Input should be a folder when download from aws requested")
        exit(1)

    return [f for f in do_download(args.download, path, new_only) if f.suffix == ".zip"]


def setup_import(parser: ArgumentParser):
    def do_import(_: ArgumentParser, args: Namespace):
        from apiserver.database import db

        db.initialize()

        from apiserver.mongo.initialize import PrePopulate

        files = _get_input_files(args)
        for file in files:
            PrePopulate.import_from_zip(
                filename=str(file),
                company_id=args.company,
                user_id=args.user,
                user_name=args.user_name,
                artifacts_path=args.files,
            )

        if args.update_featured_order:
            PrePopulate.update_featured_projects_order()

    parser.set_defaults(func=do_import)
    parser.add_argument(
        "input", type=str, help="Input zip file or folder with zip files"
    )
    parser.add_argument(
        "--company", "-C", type=str,
        help="Company id for imported data. If not provided, company will be set "
             "according to each zip file metadata, or set to public if not provided"
    )
    parser.add_argument(
        "--user", "-U", type=str, default="", help="Override user id for imported data"
    )
    parser.add_argument(
        "--user-name",
        "-N",
        type=str,
        default="",
        help="Override user name for imported data",
    )
    parser.add_argument(
        "--files",
        "-F",
        type=str,
        default="/mnt/fileserver",
        help="Path to the root folder for storing user files",
    )
    parser.add_argument(
        "--download",
        "-D",
        type=str,
        default="",
        help="Name of the s3 bucket to download data from",
    )
    parser.add_argument(
        "--skip-update-featured-order",
        "-Su",
        action="store_false",
        dest="update_featured_order",
        default=True,
        help="Skip updating featured (public) projects order",
    )


def setup_upgrade(parser: ArgumentParser):
    def do_upgrade(_: ArgumentParser, args: Namespace):
        from apiserver.database import db

        db.initialize()

        from apiserver.mongo.initialize import PrePopulate

        files = _get_input_files(args, new_only=False)
        for file in files:
            print(f"Processing file {str(file)}")
            created_files = PrePopulate.upgrade_zip(filename=str(file))
            if args.download and created_files:
                upload_files(args.download, file.stem, created_files)

    parser.set_defaults(func=do_upgrade)
    parser.add_argument(
        "input", type=str, help="Input zip file or folder with zip files"
    )
    parser.add_argument(
        "--download",
        "-D",
        type=str,
        default="",
        help="Name of the s3 bucket to download data from",
    )


def main():
    p = ArgumentParser(description=__doc__)
    sps = p.add_subparsers(title="Commands")

    setup_export(sps.add_parser("export"))
    setup_import(sps.add_parser("import"))
    setup_upgrade(sps.add_parser("upgrade"))

    args = p.parse_args()
    if not getattr(args, "func", None):
        p.print_usage()
        exit(1)
    args.func(p, args)


if __name__ == "__main__":
    main()
