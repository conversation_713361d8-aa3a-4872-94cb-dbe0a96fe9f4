from enum import Enum, auto

from jsonmodels import models, fields

from apiserver.apimodels import <PERSON><PERSON>ield, ActualEnumField, Dict<PERSON>ield
from apiserver.apimodels.organization import TagsRequest
from apiserver.database.model import EntityVisibility
from apiserver.utilities.stringenum import StringEnum


class DatasetFileRequest(models.Base):
    dataset_id = fields.StringField(required=True)  #dataset id
    version = fields.StringField(required=True)
    # file_name = fields.StringField(required=True)

class DatasetFileUploadRequest(DatasetFileRequest):
    dir_path = fields.StringField(required=True)

# class DatasetFileListRequest(models.Base):
#     dataset_id = fields.StringField(required=True)  #dataset id
#     version = fields.StringField(required=True)

class DatasetFileDeleteRequest(DatasetFileRequest):
    file_name = fields.StringField(required=True)

class BatchDatasetFileDeleteRequest(DatasetFileRequest):
    batch_file_names = fields.ListField([str])

class DatasetFileDownloadRequest(DatasetFileRequest):
    file_name = fields.StringField(required=True)

class BatchDatasetFileDownloadRequest(DatasetFileRequest):
    batch_file_names = fields.ListField([str])

class DatasetFindRequest(models.Base):
    dataset_name = fields.StringField(required=True)


class DeleteRequestForMongo(models.Base):
    dataset_id = fields.StringField(required=True)

class DatasetUpdateRequest(models.Base):
    dataset_id = fields.StringField(required=True)

class DatasetCreateVersionRequest(models.Base):
    dataset_id = fields.StringField(required=True)
    version = fields.StringField(required=True)
    description = fields.StringField()

class DatasetRequestGetVersion(models.Base):
    dataset_id = fields.StringField(required=True)

class DatasetRequestDownLoadVersion(models.Base):
    dataset_id = fields.StringField(required=True)    
    version = fields.StringField(required=True)


class DatasetsListRequests(models.Base):
    page = fields.IntField()
    page_size = fields.IntField()
    name = fields.StringField()
    kind = fields.StringField()
    algo_id = fields.StringField()


class DatasetVersionsListRequests(models.Base):
    dataset_id = fields.StringField(required=True)


class DatasetDetailRequests(models.Base):
    dataset_id = fields.StringField(required=True)
    version_id = fields.StringField()
    page = fields.IntField()
    page_size = fields.IntField()
