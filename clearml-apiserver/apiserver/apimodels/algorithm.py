from jsonmodels import models, fields
from jsonmodels.fields import <PERSON><PERSON><PERSON>, IntField, BoolField, Float<PERSON>ield
from apiserver.apimodels import <PERSON><PERSON>ield, DictField


class AlgoRequest(models.Base):
    algorithm_id = fields.StringField(required=True)


class AlgoUpdateRequest(AlgoRequest):
    is_public = StringField(required=True)
    description = StringField()


class AlgoFileRequest(models.Base):
    algorithm_id = fields.StringField(required=True)
    version = fields.StringField(required=True)


class AlgoFileUploadRequest(AlgoFileRequest):
    dir_path = fields.StringField(required=True)


class AlgoGitUrlRequest(models.Base):
    algorithm_id = fields.StringField(required=True)
    git_url = fields.StringField(required=True)
    version = fields.StringField(required=True)


class AlgoFileDeleteRequest(AlgoFileRequest):
    file_name = fields.StringField(required=True)


class AlgoFileBatchDeleteRequest(AlgoFileRequest):
    file_name = fields.ListField([str])


class AlgoCreateVersionRequest(models.Base):
    algorithm_id = fields.StringField(required=True)
    version = fields.StringField(required=True)
    description = fields.StringField()    

class AlgoReadFileRequest(AlgoFileRequest):
    file_name = fields.StringField(required=True)   