from jsonmodels.fields import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DateTimeField
from jsonmodels.models import Base
from jsonmodels.validators import <PERSON>, Enum

from apiserver.apimodels import <PERSON><PERSON>ield, EnumField
from apiserver.config_repo import config
from apiserver.database.model.auth import Role, UserType, UserSex
from apiserver.database.utils import get_options


class GetTokenRequest(Base):
    """ User requests a token """

    expiration_sec = IntField(
        validators=Max(config.get("apiserver.auth.max_expiration_sec")), nullable=True
    )
    """ Expiration time for token in seconds. """


class GetTaskTokenRequest(GetTokenRequest):
    """ User requests a task token """

    task = StringField(required=True)


class GetTokenForUserRequest(GetTokenRequest):
    """ System requests a token for a user """

    user = StringField(required=True)
    company = StringField()


class GetTaskTokenForUserRequest(GetTokenForUserRequest):
    """ System requests a token for a user, for a specific task """

    task = StringField(required=True)


class GetTokenResponse(Base):
    token = StringField(required=True)


class ValidateTokenRequest(Base):
    token = StringField(required=True)


class ValidateUserRequest(Base):
    email = StringField(required=True)


class ValidateResponse(Base):
    valid = BoolField(required=True)
    msg = StringField()
    user = StringField()
    company = StringField()


class CreateUserRequest(Base):
    name = StringField(required=True)
    realname = StringField(required=True)
    sex = EnumField(
        UserSex,
        default=UserSex.PRIVARY,
    )
    phone = StringField()
    email = StringField()
    is_active = BoolField(default=True)
    
	# 当前角色
    role = StringField(
        required=True,
		default=Role.user,
	)
    # 用户的类型
    user_type = StringField(
        required=True,
        default=UserType.user
	)


class CreateUserResponse(Base):
    id = StringField(required=True)


class Credentials(Base):
    access_key = StringField(required=True)
    secret_key = StringField(required=True)
    label = StringField()


class CredentialsResponse(Credentials):
    secret_key = StringField()
    last_used = DateTimeField(default=None)
    last_used_from = StringField()


class CreateCredentialsRequest(Base):
    label = StringField()


class CreateCredentialsResponse(Base):
    credentials = EmbeddedField(Credentials)


class GetCredentialsResponse(Base):
    credentials = ListField(CredentialsResponse)


class EditCredentialsRequest(Base):
    access_key = StringField(required=True)
    label = StringField()


class RevokeCredentialsRequest(Base):
    access_key = StringField(required=True)


class RevokeCredentialsResponse(Base):
    revoked = IntField(required=True)


class AddUserRequest(CreateUserRequest):
    company = StringField()
    secret_key = StringField()


class AddUserResponse(CreateUserResponse):
    secret = StringField()


class DeleteUserRequest(Base):
    user = StringField(required=True)
    company = StringField()


class UpdateUserPasswordRequest(Base):
    password = StringField(required=True)


class EditUserReq(Base):
    user = StringField()
    name = StringField()
    realname = StringField()
    sex = EnumField(
        UserSex,
        default=UserSex.PRIVARY,
    )
    phone = StringField()
    email = StringField()


class ResetUserRequest(Base):
    user = StringField(required=True)
    reset_password = BoolField()
    reset_status = BoolField()


