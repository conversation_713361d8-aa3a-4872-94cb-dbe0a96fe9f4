from jsonmodels import models, fields
from jsonmodels.fields import <PERSON><PERSON><PERSON>, IntField, <PERSON>ol<PERSON>ield, <PERSON>loat<PERSON>ield
from apiserver.apimodels import <PERSON><PERSON>ield, DictField

class IdeRequest(models.Base):
    ide = fields.StringField(required=True)

class DeleteRequest(IdeRequest):
    force = fields.BoolField(default=False)

class IdeGetRequest(models.Base):
    active_users = fields.ListField(str)

class IdeUpdateRequest(IdeRequest):
    status = StringField(required=True)

class IdeCreateRequest(models.Base):
    # name = StringField(required=True)
    # tags = ListField(items_types=[str])
    description = StringField()
    cpu = IntField(required=False, default=4)
    mem = IntField(required=False, default=4)
    gpu = IntField(required=False, default=0)
    algorithm = StringField(required=True)
    image = StringField(required=True, default = "")
    is_public = StringField(required=True, default = "public")
    resourceId = IntField(required=True)