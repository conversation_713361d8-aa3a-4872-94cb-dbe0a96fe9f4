from enum import Enum, auto

from jsonmodels import models, fields
from typing import Optional

from apiserver.apimodels import <PERSON><PERSON>ield, ActualEnumField, DictField
from apiserver.apimodels.organization import TagsRequest
from apiserver.database.model import EntityVisibility
from apiserver.utilities.stringenum import StringEnum


class ProjectRequest(models.Base):
    project = fields.StringField(required=True)


class MergeRequest(ProjectRequest):
    destination_project = fields.StringField()


class MoveRequest(ProjectRequest):
    new_location = fields.StringField()


class DeleteRequest(ProjectRequest):
    force = fields.BoolField(default=False)
    delete_contents = fields.BoolField(default=False)
    delete_external_artifacts = fields.BoolField(default=True)


class ProjectOrNoneRequest(models.Base):
    project = fields.StringField()
    include_subprojects = fields.BoolField(default=True)


class GetUniqueMetricsRequest(ProjectOrNoneRequest):
    model_metrics = fields.BoolField(default=False)
    ids = fields.ListField(str)


class GetParamsRequest(ProjectOrNoneRequest):
    page = fields.IntField(default=0)
    page_size = fields.IntField(default=500)


class ProjectTagsRequest(TagsRequest):
    projects = ListField(str)


class MultiProjectRequest(models.Base):
    projects = fields.ListField(items_types=[str, type(None)])
    include_subprojects = fields.BoolField(default=True)


class ProjectTaskParentsRequest(MultiProjectRequest):
    tasks_state = ActualEnumField(EntityVisibility)
    task_name = fields.StringField()


class EntityTypeEnum(StringEnum):
    task = auto()
    model = auto()


class ProjectUserNamesRequest(MultiProjectRequest):
    entity = ActualEnumField(EntityTypeEnum, default=EntityTypeEnum.task)


class MultiProjectPagedRequest(MultiProjectRequest):
    allow_public = fields.BoolField(default=True)
    page = fields.IntField(default=0)
    page_size = fields.IntField(default=500)


class ProjectHyperparamValuesRequest(MultiProjectPagedRequest):
    section = fields.StringField(required=True)
    name = fields.StringField(required=True)
    pattern = fields.StringField()


class ProjectModelMetadataValuesRequest(MultiProjectPagedRequest):
    key = fields.StringField(required=True)


class ProjectChildrenType(Enum):
    pipeline = "pipeline"
    report = "report"
    dataset = "dataset"


class ProjectsGetRequest(models.Base):
    include_dataset_stats = fields.BoolField(default=False)
    include_stats = fields.BoolField(default=False)
    include_stats_filter = DictField()
    stats_with_children = fields.BoolField(default=True)
    stats_for_state = ActualEnumField(EntityVisibility, default=EntityVisibility.active)
    non_public = fields.BoolField(default=False)  # legacy, use allow_public instead
    active_users = fields.ListField(str)
    check_own_contents = fields.BoolField(default=False)
    shallow_search = fields.BoolField(default=False)
    search_hidden = fields.BoolField(default=False)
    allow_public = fields.BoolField(default=True)
    children_type = ActualEnumField(ProjectChildrenType)
    children_tags = fields.ListField(str)
    children_tags_filter = DictField()


class TaskEventsRequestBase(models.Base):
    task: str = fields.StringField(required=True)
    batch_size: int = fields.IntField(default=500)


class LogOrderEnum(StringEnum):
    asc = auto()
    desc = auto()


class LogEventsRequest(TaskEventsRequestBase):
    batch_size: int = fields.IntField(default=5000)
    navigate_earlier: bool = fields.BoolField(default=True)
    from_timestamp: Optional[int] = fields.IntField()
    order: Optional[str] = ActualEnumField(LogOrderEnum)


class SaveProjectRequest(ProjectRequest):
    version = fields.StringField(required=True)
    description = fields.StringField()
    name = fields.StringField()