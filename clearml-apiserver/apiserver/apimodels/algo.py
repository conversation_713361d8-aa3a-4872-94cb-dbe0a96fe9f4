from jsonmodels import models, fields
from jsonmodels.fields import <PERSON><PERSON><PERSON>, IntField, BoolField, FloatField
from apiserver.apimodels import <PERSON><PERSON>ield, DictField


class AlgoFileRequest(models.Base):
    algo_id = fields.StringField(required=True)
    version = fields.StringField(required=True)


class AlgoReadFileRequest(AlgoFileRequest):
    file_name = fields.StringField(required=True)


class ENV(models.Base):
    resource = fields.IntField(required=True)
    image = fields.StringField(required=True)
    version = fields.StringField(required=True)


class PORTS(models.Base):
    type = fields.StringField(required=True)
    port = fields.IntField(required=True)
    description = fields.StringField(required=True)


class ExecConfig(models.Base):
    env = fields.EmbeddedField(ENV)
    ports = fields.ListField(PORTS)
    cmd = fields.StringField(required=True)
    area = fields.StringField(required=True)


class AlgoCreateVersionRequest(models.Base):
    algo = fields.StringField(required=True)
    version = fields.StringField(required=True)
    description = fields.StringField()
    inputs = fields.ListField(dict)
    outputs = fields.ListField(dict)
    args = fields.ListField(dict)
    publish_status = fields.StringField()
    s3_id = fields.StringField(required=True)
    exec_config = fields.EmbeddedField(ExecConfig)


class AlgoIDRequest(models.Base):
    algo_id = fields.StringField(required=True)


class AlgoVersionRequest(models.Base):
    algo_id = fields.StringField(required=True)
    version = fields.StringField(required=True)

class FileListRequest(models.Base):
    s3_id = fields.StringField(required=True)


class FileDeleteRequest(FileListRequest):
    file_name = fields.StringField(required=True)

class BaselineTestRequest(AlgoFileRequest):
    dataset_url = fields.StringField(required=False)

class ReadFileRequest(models.Base):
    file_type = fields.StringField(required=True)
    file_name = fields.StringField(required=True)    
