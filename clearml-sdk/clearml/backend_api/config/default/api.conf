{
    version: 1.5
    # default api_server: https://api.clear.ml
    api_server: ""
    # default web_server: https://app.clear.ml
    web_server: ""
    # default files_server: https://files.clear.ml
    files_server: ""

    # verify host ssl certificate, set to False only if you have a very good reason
    verify_certificate: True

    # default api.clear.ml credentials
    credentials {
        access_key: ""
        secret_key: ""
    }

    # default version assigned to requests with no specific version. this is not expected to change
    # as it keeps us backwards compatible.
    default_version: 1.5

    http {
        max_req_size = 15728640  # request size limit (smaller than that configured in api server)

        # extra retry codes on which the session will automatically use the specified retry.
        # these will be added to the default retry list which currently includes bad gateway (502),
        # service unavailable (503), bandwidth limit exceeded (509) and too many requests (429)
        extra_retry_codes: []

        retries {
            # retry values (int, 0 means fail on first retry)
            total: 240     # Total number of retries to allow. Takes precedence over other counts.
            connect: 240   # How many times to retry on connection-related errors (never reached server)
            read: 240      # How many times to retry on read errors (waiting for server)
            redirect: 240  # How many redirects to perform (HTTP response with a status code 301, 302, 303, 307 or 308)
            status: 240   # How many times to retry on bad status codes

            # backoff parameters
            # timeout between retries is min({backoff_max}, {backoff factor} * (2 ^ ({number of total retries} - 1))
            backoff_factor: 1.0
            backoff_max: 120.0
        }

        wait_on_maintenance_forever: true

        pool_maxsize: 512
        pool_connections: 512

        # Override the default http method, use "put" if working behind GCP load balancer (default: "get")
        # default_method: "get"
    }

    auth {
        # When creating a request, if token will expire in less than this value, try to refresh the token
        token_expiration_threshold_sec = 360

        # token expiration time in seconds to request from the server. if not specified,
        # server will set the default expiration
        req_token_expiration_sec: null
    }
}
