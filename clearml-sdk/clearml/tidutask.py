from typing import Union
from .task import Task
class TiduTask(Task):
    def metrics_report(
            self,
            metrics_name:str,
            metrics_value:Union[int, str, list]=None,
            metrics_value_x: list=None,
            metrics_value_y: list=None,
            title:str = "Summary",
            ):
        if not metrics_name:
            self.get_logger().report_text("[tidu_task]call metrics_repor without metrics name")

        if metrics_value:
            self.get_logger().report_single_value(
                name = metrics_name,
                value = metrics_value
            )
        elif metrics_value_x and metrics_value_y:
            for i in (0,len(metrics_value_x)-1):
                
                self.get_logger().report_scalar(
                    title=metrics_name,
                    series=metrics_name,
                    iteration = metrics_value_x[i],
                    value = metrics_value_y[i]
                )
