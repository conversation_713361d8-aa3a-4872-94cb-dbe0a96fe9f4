"""
<PERSON><PERSON><PERSON><PERSON><PERSON>der<PERSON>
====================
This submodule contains renderer objects which define renderer behavior used
within the Exporter class.  The base renderer class is :class:`Renderer`, an
abstract base class
"""

from .base import Renderer
from .vega_renderer import <PERSON><PERSON><PERSON><PERSON>, fig_to_vega
from .vincent_renderer import Vincent<PERSON><PERSON><PERSON>, fig_to_vincent
from .fake_renderer import Fake<PERSON>enderer, FullFakeRenderer

__all__ = [
    "Renderer",
    "VegaRenderer",
    "fig_to_vega",
    "VincentRenderer",
    "fig_to_vincent",
    "Fake<PERSON>enderer",
    "FullFakeRenderer"
]
