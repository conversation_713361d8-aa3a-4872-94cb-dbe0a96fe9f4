{
    version: 1
    disable_existing_loggers: 0
    loggers {
        clearml {
            level: INFO
            # force the above-mentioned level even if such a logger already exists (default true)
            # setting this to false causes clearml to preserve the log level if it was set before loading clearml
            force_level: false
        }
        boto {
            level: WARNING
        }
        "boto.perf" {
            level: WARNING
        }
        botocore {
            level: WARNING
        }
        boto3 {
            level: WARNING
        }
        google {
            level: WARNING
        }
        urllib3 {
            level: WARNING
        }
    }
}