{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {"id": "fauyMKCEJ6x4"}, "source": ["<div align=\"center\">\n", "\n", "  <a href=\"https://clear.ml\" target=\"_blank\">\n", "    <img width=\"512\", src=\"https://github.com/allegroai/clearml/raw/master/docs/clearml-logo.svg\"></a>\n", "\n", "\n", "<br>\n", "\n", "<h1>Notebook 1: Experiment Management</h1>\n", "\n", "<br>\n", "\n", "Hi there! This is the ClearML getting started notebook, meant to teach you the ropes. ClearML has a lot of modules that you can use, so in this notebook, we'll start with the most well-known one: <a href=\"https://app.clear.ml/projects\" target=\"_blank\">Experiment Management.</a>\n", "\n", "You can find out more details about the other ClearML modules and the technical specifics of each in <a href=\"https://clear.ml/docs\" target=\"_blank\">our documentation.</a>\n", "\n", "\n", "<table>\n", "<tbody>\n", "  <tr>\n", "    <td><b>Step 1: Experiment Management</b></td>\n", "    <td><a target=\"_blank\" href=\"https://colab.research.google.com/github/allegroai/clearml/blob/master/docs/tutorials/Getting_Started_1_Experiment_Management.ipynb\">\n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/>\n", "</a></td>\n", "  </tr>\n", "  <tr>\n", "    <td>Step 2: Remote Agent</td>\n", "    <td><a target=\"_blank\" href=\"https://colab.research.google.com/github/allegroai/clearml/blob/master/docs/tutorials/Getting_Started_2_Setting_Up_Agent.ipynb\">\n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/>\n", "</a></td>\n", "  </tr>\n", "  <tr>\n", "    <td>Step 3: Remote Task Execution</td>\n", "    <td><a target=\"_blank\" href=\"https://colab.research.google.com/github/allegroai/clearml/blob/master/docs/tutorials/Getting_Started_3_Remote_Execution.ipynb\">\n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/>\n", "</a></td>\n", "  </tr>\n", "</tbody>\n", "</table>\n", "\n", "</div>"]}, {"cell_type": "markdown", "metadata": {"id": "s8eUiQauOA31"}, "source": ["# 📦 Setup\n", "\n", "Since we are using a notebook here, we're importing the special `browser_login` function, it will try to help you easily log in. If it doesn't work, don't worry, it will guide you through the steps to get it done :)\n", "\n", "**If it asks you to generate new credentials, keep them handy, you'll need them again in later notebooks**\n", "\n", "When installing ClearML in a normal python environment (not a colab notebook), you'll want to use `clearml-init` instead. It, too, will guide you through the setup.\n", "\n", "What we're doing here is connecting to a ClearML server, that will store all your experiment details."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "IMZsEYw5J5JI"}, "outputs": [], "source": ["%pip install --upgrade xgboost clearml\n", "import clearml\n", "clearml.browser_login()"]}, {"cell_type": "markdown", "metadata": {"id": "TLsgNR5PhPPN"}, "source": ["# ✈️ Example: XGBoost\n", "\n", "Let's start simple, by adding the ClearML experiment tracker to an XGBoost training script.\n", "\n", "The important parts are:\n", "\n", "- Initializing ClearML. Always do this as a very first line if possible!\n", "- Manually log the parameter dict (e.g. CLI commands are captured automatically)\n", "\n", "**⚠️ NOTE: `output_uri` in `Task.init` is an important parameter. By default it is set to `False`, meaning any registered models will NOT be uploaded to ClearML, but their info will be registered. Set this to `True` to automatically upload all model files.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CSaL3XTqhYAy", "outputId": "6c870d67-d4f8-4c11-a356-0678b2fd9a41"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ClearML Task: created new task id=38ba982031b04187a59acbd7346c5010\n", "ClearML results page: https://app.clear.ml/projects/df7858a441f94c29a1230d467f131840/experiments/38ba982031b04187a59acbd7346c5010/output/log\n", "2023-03-06 16:06:09,239 - clearml.Task - INFO - Storing jupyter notebook directly as code\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "from sklearn.datasets import load_iris\n", "from clearml import Task\n", "import xgboost as xgb\n", "import numpy as np\n", "\n", "\n", "# Always initialize ClearML before anything else. Automatic hooks will track as\n", "# much as possible for you!\n", "task = Task.init(\n", "    project_name=\"Getting Started\",\n", "    task_name=\"XGBoost Training\",\n", "    output_uri=True  # IMPORTANT: setting this to True will upload the model\n", "    # If not set the local path of the model will be saved instead!\n", ")\n", "\n", "# Training data\n", "X, y = load_iris(return_X_y=True)\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=100\n", ")\n", "\n", "dtrain = xgb.DMatrix(X_train, label=y_train)\n", "dtest = xgb.DMatrix(X_test, label=y_test)\n", "\n", "# Setting the parameters\n", "params = {\n", "    'max_depth': 2,\n", "    'eta': 1,\n", "    'objective': 'reg:squarederror',\n", "    'nthread': 4,\n", "    'eval_metric': 'rmse',\n", "}\n", "# Make sure ClearML knows these parameters are our hyperparameters!\n", "task.connect(params)\n", "\n", "# Train the model\n", "bst = xgb.train(\n", "    params,\n", "    dtrain,\n", "    num_boost_round=100,\n", "    evals=[(dtrain, \"train\"), (dtest, \"test\")],\n", "    verbose_eval=0,\n", ")\n", "\n", "# Save the model, saving the model will automatically also register it to \n", "# ClearML thanks to the automagic hooks\n", "bst.save_model(\"best_model\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "P150YtRbhYww"}, "outputs": [], "source": ["# When a python script ends, the ClearML task is closed automatically. But in\n", "# a notebook (that never ends), we need to manually close the task.\n", "task.close()"]}, {"cell_type": "markdown", "metadata": {"id": "-jWVBTuXyRHd"}, "source": ["## 🔹 XGBoost WebUI\n", "\n", "\n", "After running the code above you should have a new ClearML project on your server called \"Getting Started\".\n", "\n", "Inside the task, a lot of things are tracked!\n", "\n", "\n", "\n", "### Source information (git, installed packages, uncommitted changes, ...)\n", "\n", "Naturally, running this notebook doesn't actually give us any git information. Instead ClearML saves the execution order of the cells you've executed until it detected the `Task.close()` command.\n", "\n", "![](https://i.imgur.com/DehMv2X.png)\n", "\n", "### Configuration\n", "\n", "The configuration section holds all the values we added using the `task.connect()` call before. You can also use `task.set_parameter()` for a single value or `task.connect_configuration()` to connect an external configuration file.\n", "\n", "![](https://i.imgur.com/hlHcKfm.png)\n", "\n", "### Artifacts\n", "\n", "\n", "Artifacts are very flexible and can mean every type of file storage. Mostly this is used to track and save input and output models. In this case we get the saved XGBoost model. But when running a notebook you'll also get the original notebook here as well as an HTML preview!\n", "\n", "![](https://i.imgur.com/Ow5meUZ.png)\n", "\n", "\n", "### <PERSON><PERSON><PERSON>\n", "\n", "Scalars are the performance values of your models. They can either be a value for each epoch/iteration, which will display them as a plot, or a single one, which displays them as a table. In our example above the scalars where automatically grabbed from XGBoost using our integration!\n", "\n", "![](https://i.imgur.com/QBl79GI.png)\n", "\n", "\n", "### Plots and Debug Samples\n", "\n", "We haven't generated any plots or debug samples in our XGBoost example. We'll look at these a little later in the tutorial.\n", "\n", "\n", "### Info + Console logs\n", "\n", "These sections speak for themselves: you get some additional information (like runtime or original machine hostname) as well as the original console logs.\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "pkYjS0RTtJ6s"}, "source": ["# 🚀 Example: Pytorch + Tensorboard + Matplotlib\n", "\n", "As you might have seen, our previous example was missing plots and debug samples, none were logged to ClearML! Luckily, XGBoost is not the only integration that ClearML has, it can also lift scalars, plots and debug samples from other frameworks you most likely use already.\n", "\n", "A full list of our integrations can be found [here](https://clear.ml/docs/latest/docs/integrations/libraries)."]}, {"cell_type": "markdown", "metadata": {"id": "5iwK3p-X3WIC"}, "source": ["## 🔹 Logging scalars through Tensorboard\n", "\n", "ClearML will detect this and also log scalars, images etc. to the ClearML experiment manager. So you don't even have to change your existing code!\n", "\n", "![](https://i.imgur.com/4MNZdGi.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4aeacPunj_7o", "outputId": "52a3afcf-bb5e-4be2-e345-5b2199a5358a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ClearML Task: created new task id=0bfefb8b86ba44798d9abe34ba0a6ab4\n", "ClearML results page: https://app.clear.ml/projects/df7858a441f94c29a1230d467f131840/experiments/0bfefb8b86ba44798d9abe34ba0a6ab4/output/log\n", "Epoch 1 complete\n", "Epoch 2 complete\n", "Epoch 3 complete\n", "Epoch 4 complete\n", "Epoch 5 complete\n", "Epoch 6 complete\n", "Epoch 7 complete\n", "Epoch 8 complete\n", "Epoch 9 complete\n", "Epoch 10 complete\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from clearml import Task\n", "from torch.utils.data import DataLoader\n", "from torchvision.datasets import MNIST\n", "from torchvision.transforms import ToTensor\n", "from torch.utils.tensorboard import SummaryWriter\n", "\n", "\n", "# Always initialize ClearML before anything else. Automatic hooks will track as\n", "# much as possible for you (such as in this case TensorBoard logs)!\n", "task = Task.init(project_name=\"Getting Started\", task_name=\"TB Logging\")\n", "\n", "# Set up TensorBoard logging\n", "writer = SummaryWriter()\n", "\n", "# Load MNIST dataset\n", "train_data = MNIST('data', train=True, download=True, transform=ToTensor())\n", "train_loader = DataLoader(train_data, batch_size=64, shuffle=True)\n", "\n", "# Define model\n", "model = nn.Sequential(\n", "    nn.<PERSON><PERSON>(784, 128),\n", "    nn.ReLU(),\n", "    nn.<PERSON><PERSON>(128, 10)\n", ")\n", "\n", "# Define loss and optimizer\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.SGD(model.parameters(), lr=0.01)\n", "\n", "# Train the model\n", "for epoch in range(10):\n", "    for i, (inputs, labels) in enumerate(train_loader):\n", "        # Flatten input images\n", "        inputs = inputs.view(-1, 784)\n", "        \n", "        # Zero the gradients\n", "        optimizer.zero_grad()\n", "        \n", "        # Forward pass\n", "        outputs = model(inputs)\n", "        loss = criterion(outputs, labels)\n", "        \n", "        # Backward pass and update parameters\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        # Log loss to TensorBoard\n", "        # ClearML will detect this and also log the scalar to the ClearML\n", "        # experiment manager. So you don't even have to change your existing code!\n", "        writer.add_scalar('Training loss', loss.item(), epoch * len(train_loader) + i)\n", "        \n", "    print(f'Epoch {epoch + 1} complete')\n", "    \n", "# Close TensorBoard writer\n", "writer.close()"]}, {"cell_type": "markdown", "metadata": {"id": "fsHJZGcC3afb"}, "source": ["## 🔹 Logging debug samples through matplotlib\n", "\n", "Whenever you use `plt.imshow()` ClearML will intercept the call and immediately log the image to the experiment manager. The images will become visible under the Debug Samples tab.\n", "\n", "![](https://i.imgur.com/FuHTPKP.png)\n", "\n", "You can log basically any media type (images, videos, audio, ...) as a debug sample. Check [our docs](https://clear.ml/docs/latest/docs/references/sdk/logger#report_media) for more info."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 281}, "id": "bAEB04OUuI3R", "outputId": "d9e8e484-3903-4212-9674-7490cbb192e3"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import torchvision\n", "\n", "# Helper function to show an image\n", "def matplotlib_imshow(img):\n", "    img = img.mean(dim=0)\n", "    img = img / 2 + 0.5     # unnormalize\n", "    npimg = img.numpy()\n", "    plt.title(\"MNIST Images\")\n", "    plt.imshow(npimg, cmap=\"Greys\")\n", "\n", "# get some random training images\n", "dataiter = iter(train_loader)\n", "images, labels = next(dataiter)\n", "\n", "# create grid of images\n", "img_grid = torchvision.utils.make_grid(images)\n", "\n", "# show images\n", "matplotlib_imshow(img_grid)"]}, {"cell_type": "markdown", "metadata": {"id": "pjtyBV0h5Aak"}, "source": ["## 🔹 Logging plots through Matplotlib\n", "\n", "Similar to above, mat<PERSON><PERSON><PERSON><PERSON> is automatically captured, but this time, we use `plt.show()` (implicit inside `ConfusionMatrixDisplay`) which logs the result as a plot!\n", "\n", "![](https://i.imgur.com/Q4H7RDM.png)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 313}, "id": "GvM3VYPvxZoR", "outputId": "b1d637a6-47b5-446c-9728-d6b720106a51"}, "outputs": [{"data": {"text/plain": ["<sklearn.metrics._plot.confusion_matrix.ConfusionMatrixDisplay at 0x7efe139e0b50>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay\n", "\n", "\n", "# Load MNIST test dataset\n", "test_data = MNIST('data', train=False, download=True, transform=ToTensor())\n", "test_loader = DataLoader(test_data, batch_size=64, shuffle=False)\n", "\n", "# Test the model and compute confusion matrix\n", "y_true = []\n", "y_pred = []\n", "model.eval()\n", "with torch.no_grad():\n", "    for inputs, labels in test_loader:\n", "        inputs = inputs.view(-1, 784)\n", "        outputs = model(inputs)\n", "        _, predicted = torch.max(outputs, 1)\n", "        y_true.extend(labels.numpy())\n", "        y_pred.extend(predicted.numpy())\n", "cm = confusion_matrix(y_true, y_pred)\n", "\n", "# Display confusion matrix\n", "plt.title(\"Confusion Matrix Logging\")\n", "ConfusionMatrixDisplay(cm).plot(ax=plt.gca())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6hvbrbvEp3yb"}, "outputs": [], "source": ["# When a python script ends, the ClearML task is closed automatically. But in\n", "# a notebook (that never ends), we need to manually close the task.\n", "task.close()"]}, {"cell_type": "markdown", "metadata": {"id": "nXCUDvfZ8kAs"}, "source": ["# 🚁 Example: <PERSON><PERSON><PERSON><PERSON>\n", "\n", "As a third example, let's train an sklearn example. Here, too, ClearML will automatically capture a number of outputs, which we will describe in the code comments."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 264}, "id": "I_UH3SSZ9WQ0", "outputId": "e6c6c58d-db9d-4dd0-edb2-3d556283ae55"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ClearML Task: created new task id=62fb0fc73d384181a220dfb8adb2c75a\n", "ClearML results page: https://app.clear.ml/projects/df7858a441f94c29a1230d467f131840/experiments/62fb0fc73d384181a220dfb8adb2c75a/output/log\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAP8AAADSCAYAAACFOhYiAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMywgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/NK7nSAAAACXBIWXMAAAsTAAALEwEAmpwYAABO7UlEQVR4nO2dd1iT1xfHvzdAICETAmFvFREnQ1zgRK2Ke1u1ddZR+9PaYZdW29rWtlpttbZ11K24V92jFTeguGUIyF6yV8j5/REajMhQQVDez/O8D8l97zhvyMl773nPPYcRETg4OBoevLoWgIODo27glJ+Do4HCKT8HRwOFU34OjgYKp/wcHA0UTvk5OBoonPK/ojDGRjPGjta1HByvLpzy11MYYw8YY90rOk9Em4jI/zn6Pc0YK2CMZTPGshhjVxljHzHGDJ+hD2KMuTzr2M/KyxqnocIp/ysIY0z/BbuYQURiAJYA5gAYAeAQY4y9sHAcrwyc8r8CMMbGM8bOMcZ+YoylAZhfWvZv6XlWei659G4exhhzr6pfIsolotMAAgC0A9CntD9vxth5xtgjxlgCY2wFY4xfeu5safNrjLEcxthwxpicMXaAMZbCGMsofW3zhPyRpbONKMbY6MfOvc0Yu13a7ghjzL6icV74g+TQgVP+V4e2ACIBKAF89cQ5fwC+ABoDkAIYBiCtuh0TUQyAKwA6lRaVAPgfAAU0PwrdAEwrretbWqclEYmIaBs036O1AOwB2AHIB7ACABhjxgB+BtC7dLbRHkBo6bn+AOYBGATADMA/ALZUMg5HDcIp/6tDPBEtJyIVEeU/ca4YgBiAKwBGRLeJKOFZ+wdgAgBEdJWILpSO9QDAbwD8KmpIRGlEtJOI8ogoG5ofp8frqwG4M8YERJRARDdLy6cC+KZUXhWArwG0+u/uz1G7cMr/6hBb0QkiOgnNnfYXAMmMsdWMMckz9m8NIB0AGGONS6fuiYyxLGiUUlFRQ8aYkDH2G2MsurT+WQAyxpgeEeUCGA6Noicwxg4yxlxLm9oDWFa6vHhUOj4rlYWjluGU/9Wh0u2XRPQzEXkAcINm+j+3uh0zxmwBeEAz7QaAlQDuAGhERBJopuaVGQPnAGgCoG1p/f+m7KxUtiNE1AMaA+MdAL+Xno8FMIWIZI8dAiIKqq7sHM8Pp/yvAYwxL8ZYW8aYAYBcAAXQTLWraidkjPkB2AvgEoBDpafEALIA5JTepd95omkSAKfH3ouhWec/YoyZAPjisTGUjLH+pWv/QgA5j8m2CsDHjLFmpXWljLGhlYzDUYNwyv96IIHmbpoBIBoaY9/3ldRfwRjLhka5lgLYCaAXEf2nlO8DGAUgu7TfJ41t8wGsL52uDyvtQwAgFcAFAH8/VpcHYDY0NoV0aGwB7wAAEe0G8C2AraXLhRsAelcyDkcNwrhgHhwcDRPuzs/B0UDhlJ+Do4HCKT8HRwOFU34OjgbKi24QqRYKhYIcHBxexlAcHByPcfXq1VQiMnvauZei/A4ODrhy5crLGIqDg+MxGGPRFZ3jpv0cHA0UTvk5OBoonPJzcDRQOOXn4GigcMrPwdFA4ZSfg6OBwik/B0cDhVN+Do4GCqf8HBwNFE75OTgaKJzyc3A0UDjl5+BooHDKz8HRQOGUn4OjgcIpPwdHA4VTfg6OBgqn/BwcDRRO+Tk4GigvJYwXx8shLy8PQUFBEAgE8PHxgZ6eXl2LxFGP4ZT/NWHLli2YNmMGbBwbIT8vB6r8POzaGYg2bdrUtWgc9RRO+V8D7t69i+kz38WHv26BfWM3AMCFo/vRp28/RD+IAp/Pr2MJOeoj3Jr/NWDtunXwDRimVXwA8PHvB3MbB/z999+VtORoyHDK/xqQkfEIElPzcuVShRkyMjLqQCKOVwFO+V8Devr3wKUje1CiUmnLsjLScC3oDLp27VqHknHUZ7g1/2tAQEAAVq3+Hd9OGwnf/iORn5uNY1vXYObMmbC1ta1r8TjqKZzyvwbo6+vj4P592LRpE3bv3QehUIA/Vv2Knj171rVoHPUYRkS1Poinpydx6bo4OF4+jLGrROT5tHPcmp+Do4HCKf8rwJkzZ/DmuPHoG9Afv/zyC/Lz8+taJI7XAE756zk/LV2K4aPGgKd0glP7nli7bRc6d+3G/QBwvDCc8tdjMjIyMH/+fMxbvQO9Rr6Ndj0DMHvpOqj0DLFhw4a6Fo/jFYdT/npMUFAQXJq1gpmVjbaMMQaf3oNw+MjROpSM43WAU/56jFwuR3pKEp58IvMoJQmmJiZ1JBXH6wKn/C+JK1euoG+/AFhYWsHT2wfbtm2rso2Pjw8MeMCJwA3aH4DEmCgc27YGEye8Xdsic7zmcE4+L4GQkBD49+yFAVNm45N3PkFsxF3M/fgTpKSmYsb06RW24/F4OLBvLwIGDMTx7esgNVEg+v5tfLt4MXx8fF7iFXC8jnBOPi+BQUOGQuLcAj1HvKUtiw2/gyUzxiDuYSwMDAwqbU9EuHz5MjIzM9G2bVtIJJLaFpnjNaEyJx/uzv8SCAkJwYzRM3TKbF1cAR4PiYmJVfrfM8bg7e1dmyJyNEC4Nf9LwMHBAQ/u3tApy0hJRGF+PkxNTWt1bCJCYmIicnNza3UcjlcPTvlfAh/OfR87ln+De9eugIiQmhCH1V/MxsSJEyEUCmtt3IMHD6JRE1e4ujWD0sIS49+egJycnFobj+PVglvzvyQ2bdqEeZ9+hoyMdPAYD1OmTMFXixZCX792Vl5Xr16Ff6/emLJwGdy9OyI36xE2/jAfpoY87ArcUStjctQ/Klvzc8r/ElGr1cjIyIBYLK71uHpvjhsPMrVFnzenaMuKCvIxq48Pwq6Fcvv8Gwjcrr56Ao/Hg6mp6UsJqBn14AHsGjXVKeMbCWBl54iYmJhaH5+j/sMpfz1DrVZj06ZN+PzzzxEaGvrc/bT19ETY+TM6ZY9Sk/HwQTjc3NwqaMXRkOAe9dUjbty4Ad/OXaAGg6W9E779fglatmyJC0HnwOM92+/0e+/NgqeXNwTGYvj0DEBqwkPsWLEYM6bPgFwur6Ur4HiV4O789Qj/Xr3Rvs9g/HLkCj7/IxArDl9EfHIqpkyZUnXjJ7C1tUXQuX9hmJeKn2aNxeE/fsSH/3sXXy1aWAuSc7yKcAa/esKNGzfg4eWN1aeug29opC2/euYY1i+eh5TEhDqUjuNVhTP4vQIkJSXB0MgIBnxDnXKpiQLFRcV1JBXH6wy35q8FSkpKMGfOHOzevRtyuRy//PILOnToUGkbPz8/qIqLcetyEJp5l9U9vWcrmjRuVK1xQ0JCsH//fgiFQgwbNgx2dnYvdB0cz05YWBj27NkDQ0NDDB06FI6OjuXqlJSU4PDhw7h06RLs7OwwfPhwiMXily8sEdX64eHhQQ2FvLw8kpmYktzcggLemk7tegaQgaERTZo0qcq28+fPJyOBkAZMnEkzvllBXl16kdBYRLdu3aq0nVqtpv/NnkPmllbUb9xU6jFkDEllctq4cWNNXRZHNfh03kekkIpogJsZ9XE1I5lISKtX/6ZTJycnhzq09SJXKxMa7q6gji7mpFSYUFhYWK3IBOAKVaCX3Jq/hunevTvuxcRh4V/7tWv3m5fO4ftZ45GclAipVFpp+3379uGLBV8iPSMDLd3dsXz5z7C3t6+0zdmzZzHyzXFYsOEAjMWa/h9G3MWiiUPwICqSs+6/BK5cuYK+PbpiSVclJIaaCXV8dhE+PJWAu+GRsLCwAAB88flnOLV5FWZ7mYLHGADgaMQjXCo2x6XgazUuF7fmf4lcCbmGfmPf0THaNfPuAFOlFRYsWFBl+4CAAIRcvYLoyAjs27e3SsUHgO2BgegUMFyr+ABg49wEbh5tuUSdL4nAHTvgZ2OkVXwAsBLz4WEtwYEDB7RlO7ZsRj8nY63iA0A3Rynu3ruPhISXa9TllL+GYQxQk7pcuVpd8szP6quLHuOB1E8bUw322JeMo/bg8Xh42hyaAJ3/AY/HoH5itk3QLL9r6/tREZzy1zA+Xp7Yt+YXFD4WWjv03ClkpCZj/vz5OnUjIiIw673/oUfPXpgz531ER0cjJCQEb0+chB69emPBl18iNTW1yjGHDx+Gs3u3IisjXVv24M4N3A29jN69e9fYtXFUzLDhw3EqNh/p+WXJUmMyCxEcl4WAgABt2YgxY7E3Ig8l6rIfgCMRmXBv1gxKpfKlylylsQ6AIYBRAOYB+Py/o6p21EANfqmpqSQUiUksMyH/EeOpdaduZGBoSJ07d9apd/nyZTIxVdCAt2fQ+0vXUsC4qSSVyUkqN6GR735Mc376k7oOHEF29g4UHx9f5bjzPvmU5Aoz6jl8HHXuN4QkMhkFBgbW1mVyPIWvFn1JcpGQ+jQ1px5NzElqLKQNf/2lUycvL4+6+nYkJ6WcBjUzI29Hc7K2MKc7d+7Uikx4EYMfY+xvAJkArgIoeexH44fq/sA0JIPfjz/+iB2HT6C1nz+C/t4LiYkCnfsNxbIPJuNhTIz2kY5v5y5o6tcHnQeM0LY9sm0dLhzZhy/W7NKWbVwyH43MxFi69Kcqx75z5w4OHjwIgUCAwYMHv/w7CQfCw8Oxb98+8Pl8DBo0CFZWVuXqEBFOnTqFy5cvw9bWFgMHDoRAIKgVeV5oSy9j7AYRub+IAA1J+Xu90QdNuw6Adzfd6fbXk4Zg2Xdfo2vXrlCr1TAwMMDaoHs6Tj152VmY1tMD64Lua8siboZiy7fzcDPs+ku7Bo7Xhxe19gcxxprXsEyvLaamJshISdQpU6vVSEtO1IbsYoxBIpUiIyVJp156SiKMxTLdsuREmHAx+jlqgQqVnzEWxhi7DqAjgGDG2F3G2PXHyl9Zjh49io6+nWFqZoZ2HTri0KFD1WqXl5eHjz+eBzsHR1haWWPK1Hdw8+ZNTJn6DiytrGHn4AgQ4fCG35AU+wCARvEPbfgNSjMFWrRoAUCj/BMmTMTmHxZoDYMFeblYt/hTKCytUaLSGI0epSZjz+of8c6UyTX/ITRAQkJC0LeXPxRyKZq7Nsaff/xRLiFKg6IiYwAA+8qOito97ahPBr+DBw+SwlxJ7y7+lX49epX+t2Q1mVlYVmkcU6vV1LV7D2rXow8t3naUftxzlnqPfJuMxRLyHzqWftxzlhZvO0rtevShJq5uJJHJyN2jLVnZOVCrNh704MEDnf4KCgpoxKhRJJWbUKu2HUgqk9OIkaPIt3MXUigtqIV3exJLpfTJp5+RWq2uzY+kQRAWFkZyiYimelnQmv7OtLCrLTkrZfTVwi/rWrRaBS9o8NtARG9WVVYZ9WnN38bLG11HT4WHn7+27Pr5M9j762LcuhFWYbugoCAMH/0mFu84CZ6enrZ84aSh8O03FH4BwwAA6pISfDS0K9asXgU9PT2YmpqiRYsWFT5vj46Oxv379+Hq6gobG01Ovjt37iAuLg6tWrWq9ei+DYUxI4dD/+4ZDHIt83ZMyinCB6eTEJeYXKuBVOuSF13zN3uiMz0AHjUhWF1wM+w63L076ZS5t+2EO7duQv0UR5n/CA0NhZtXex3FB4A2vt0Rc++W9j1PTw9uXu0RHh6Orl27omXLlpU62tjb26N79+5axQcAV1dXdOvWjVP8GiQ0+Cpamuta1JUiPiQCfoMNa1bZmv9jxlg2gBaMsazSIxtAMoC9L03CGsbewRFRt3VNFlG3r8PGzr5SDytnZ2c8uH2j3Brx3rWrMLdxQPS9W4h/EAEiwoPbN+Dg4IDLly/rfLFUKhViYmKeK4b+o0ePEBcX17DXqC+Ak7MLItILdMqyClXIyCmApaVlHUlVx1S0HqCytf83VdWp6qhPa/4//vyT7Jwa0ddb/qbNwbH07fZj5Ni4Ka1YsaLSdiqVitzcm1P/t6bRH2dv0foL4TTug4Wkb2BAxhIpGQqEZMA3JLHMhOQmJmQslpDAWEQGfENycHahxYu/JaWFJZkpLUgskdKMme9SYWFhlfKmpqbSgEGDyVgkJrmpghq7NqVjx47V1MfRYDh79iwppCJa0MWW9oxoQn8EOJOnvSlNmzK5rkWrVVDJmr8ypW9T2VFRu6cd9Un51Wo1/fLrr2RpZU3GIjEpLSzpp6VLq2VUS0hIoIGDh5CRkYCMjATk5d2W+EYCeufLpbTx8gNafyGchk57nwwFQpq7dC1tuhpDf5y9Rc28O5LUVKH9wVl5LJg8O3WjGTPfrXLMjr5+1GvkW7Tm3zu06WoMzf15PclNFbXmEfY6s2fPHmrsZE8CQz5JRcY0d85sKioqqmuxapXKlL9Cgx9j7FTpSyMAngCuAWAAWpR22K66s4v6ZPD7D7VajaysLIjFYug9sY6vivz8fKjVaowaNQopRQwzv16hc/6j4f7w7NITQ6bOAQAsmjwMPYaNQ9vufbR1HqUm48MhXZEQHwdjY+OnjnPt2jX06tMPS/b8o2Nr2PHr97A15mFZNbz+OHQhImRlZUEoFFaZIPV14LkMfkTUhYi6AEiA5k7vSUQeAFoDiKsdUV8ePB4PMpnsmRUfAAQCAYyNjRHz8CEcXcs7Pzq4uiMhOlL7Pi0pATbOTXTqyBTmMBIIkJ6e/mRzLTExMbB1blTOyGjj3AQPoqOfWW4OjY+FVCptEIpfFdUJ49WEiLTPwIjoBmOsaWUNXgXu3LmDW7duwdXVtcI49iqVCitXrkR8fDzGjh2Lpk11L7u9jw+OnjiMPm9O0Vr0VcVFCD13CoMmzcLlU39DJJHB1sUVIf+cAN/QCFG3w6CwtAZjDDzGyhmb1Go11q9fjzt37qBjx464ey0YudmZOnv1r587iV4dajZrb1hYGO7fv4/mzZujUaOnhw1TqVQ4e/YssrOz4evrC7lcjvz8fJw+fRpEhC5dutSajzpHLVDReoDK1v5bAPwBoHPp8TuALVW1o3q65s/Pz6cBgwaTqZmSfLr2IjMLS+rdpy/l5OTo1Dt06BAJRWJS2jpQUw8fMjA0ol69e+vUyczMJKFITO17D6BFGw/S53/uJDfPdmQkNCYjgZBatPMjG+fGJDAWEd9IQEKRhFp36kYKSxsSSaT0w48/6vQXEhJCUrkpmZhbkrt3BzI0EpCFlQ25tvSgj1dupsXbjlKfNyeTnb0DpaWl1cjnkZmZST27dyFbCxMK8HUjc1MpjRo+pJwxMjg4mGwsleRqbUpeTkqSGAtp+rRpZCIVUyt7c2plb04mUjEdOHCgRuTiqBnwgk4+RgDeAeBbWnQWwEoiKqi4lS71ac0/94MP8U9wGKZ/vQL6Bnyoioux+ov/oVUjeyz/eRkAzd1XKjfB0Glz0X3oWDDGkJGSiE9G98HHc+fggw8+AACcOHECvfv0hbWjM1Li48Dj8SCWmSA7MwOLt/4NuZkFiAjHtq9H4KofsXT/OQhFYqjVamz+aSH0c9Owd3fZDj5Laxt4+ffHiJkfgTGG3OxMzH9rEOyUChQUFSEzMxM9/f3x6Sfznrpb7HmYPPFtFMVfxeo5ftDX46GgSIUhC46ibY+R+GK+JvKQSqWCg601Rjrpo5O9BACQmFOE2X8/wFutzdDDWeM4czc1H19fSMWd+xHcjsJ6Apeo8zHMlEp8vDoQlnZlUVXTEuMxb4Q/Mh9lgDGGjRs3YtacuVh++JKOg86x7X/hTOA6RNy/BwDo3KULjCwcMe6DL7V1VnwyE66tvdF9SJkDJBHhfwEd8e63K+HkpvHvL8jLxcxeXoiNiYZcLsf169fh1danXNz+4LPHse7rj5GSVPMhnlQqFeQyCe5vGANzeZmH27XwFAxeeAaR0Q81133sGN57exS+6aTQab/tRioyC1SY7GmhLVsenI6A6Z9i5syZNS4vx7PzXAY/xtj20r9hpRt6dI7aEra2yc3OgVimG9BSLJMjLzdH60CTnJwMkVRezjNPYmKKwsIi7fuc7BxITc106hTk5kAs092FxxiDxMQU+bk52jJDgRAGfD7y8vIAACkpKU+N2y+Rm6K4uHbi9qtUKhQVF0Mu1h3TTCZAVnaZrFlZWZAaljeMyo30kVes6xUp1tM8ReGo/1Tm3jur9G9fAP2ecrySdPf3x5m923TKzuzbjq7demg9/MaMGYOEmEg8jLynrUNEOB64AR3atdWWDRkyGKd2b8G961cRuOpH7PlzOSztnXF0+3odT7yHkffwMOIeXNxba8uCzx6HhYWFdvru5+eHEpUKNy79qyPbiV2b0NRV90lBTWFkZIR23p7YeOyOTvnaw7fRq2fZ3gc/Pz/cTMxCSm7Zj1CJmnAoPAPuyrIZQ15xCc4nFKJXr17PJU98fDzGjRuH9u18MHv2bOTk5FTdiOP5qcgY8N8BYAKARlXVq+yoTwa/O3fukLnSgvyHjqFpi36mXsPHk8LMnK5fv65Tb8KECSQUiWnoO+/TlPk/UJPW3iQ3VegY2kpKSshEYUYimZz6jp1K/sPGkaFASIYCITVu5UVT5v9AQ96ZQwKRmKRyE+rUuz9NW7iM+r81jeQmpnTy5EmdMRctWkSGAiH1Gz+N3vlyKbXx7U7GInGtOvRcuXKFzBVyeneIB/01rye93bclWVuaU0REhE69n378gZQyMY1rpaQZ3hbkbmNCUmMjUgj0aLKHkiZ7KMncWJ8cba2eS45Dhw6RkT6PPCyNaXRzBTmbGJFEaEiRkZE1cZkNFrygwW8BgE4AHKAJ5XUWwD9EFFrdH5j6tOYHNKmxflu9GtfDbqCZW1NMmTz5qQa0LVu2YPF33yEnJxedOrTH0qVLIZPJtOcvXryI/oOGYOGmQxBJNUuJpNgH+HC4PwZNfg8x929DJJHBrrEb9qxagrnvz8Hlq8Gwt7PDO1OnwNnZudyYR44cwaeff4G09HS0au6On3/+WWfTT20QGxuL31atRMS922je2hOTJk2GmZlZuXrnz5/H2j9WIyszEw4ujbH0+2/xjrcFwpLyQQBaKIVYdSURu/YdfObAoUoTKfo5GiGgiWbJRET4+WICHknscTX0lV1l1jk1YvBjjAkATALwPgBrIqq2d0x9U/6aYu4HHyAqU4XBU2frlC/7YCpatO+MLo/F55s/ti/++HU5OnXq9GQ3ryR9+vSB+n4Q3nnM2AcAv11Jgtq5LQ4fPlztvuLj42Fva42tQxrDQK9sJRqTWYgPj8cgt1BVSWuOyqhM+at08mGMfQqgAwARgBBolP+fGpXwFUVPTw8lJfnlylWq4nKegyUqFfT1X5/UiAYGBsh9yg7oYiIYPuN1/mdrUT9xH1KrCQxc3oHaojr7+QcBMAVwHMAuAHuJqF7mi87JycGSJUvQrYc/hgwbjiNHjjy1XkREBGa+OwtdunXHtOkzcO/ePWzZsgWubs1gYW2DDh07IjQ0tMrxRgwfjn/379CJ2Rdz7zbCzp+Fm2fZ1ofgs8dRmJsNb++a9cp7Hh4+fIgP5r4P/66dMHni27h+/fmm1HPnzsU/0ZlIyC57+pGQXYQzkY+QlZkJazM5HG0s8PnnnyMlJQUL5s9H107tMXr4UAQFBen0ZWFhAXNTE+y6naYtUxNh6800OLq4YOSwwejaqT0WLfwS6enp2LNnDwb07Y0enX2xYsUK5OTkYM2aNejXuwcG9OuFTZs2PTU2Q3JyMj779BN07dQeY0eNxKVLl57r2l8XqjXtZ4xJoLn7dwQwFEAyEXWs7iAvY9qfl5eHDp18YSQ3R4e+Q5CZlopDf63EzGlT8dGHH2rrhYaGolv3HvDtPwJNWnsjPCwYR7asQVFREQZN+R/sGjXFldNHcO7Qbpw4drTS7LolJSVo5t4c0TExaNu9DwoL8hF89jiU5uYoKCpCGz9/ZKYm4d61K9i/dy/at29fq59BVYSHh8O3YzsM83WAv6c1QsPTsHRnGDZt3YEePXo8c39jx4zB9q2b0dZWDAaG8zGZMDAwgIucjzcayZBTWIKtN9NQoObBx04KHyUfCbnF2BuRiyVLV2DsuHHavs6fP4/unX1hJdJHE1MBriTkoECtB76BHga6iKA0NkBQYiGuJRdCoA8MdBZCaKCH47EFeJirhpOVBDMHuEFVosay3bfg7uWHtes2aPtPSEhAW882cJeo4ankIz5bI8eK3/7A8OHDa+TzrY+8aOhud2gMfn7Q7O6Lhcbg93l1BXgZyv/LL79g/Y49eO/HNdrn8+nJCfh4eA9ERURoo+L49+wNO08/dBsyRtv27P4dOLJ1Lb7aVBbIc+vP3+D+5bOVhvbav38/5nz8KaYuWo7r589AX98Azdp2xKKJg/Hn6t8QFRUFExMTDBo0qG5SMD/B2NEj0FgQi3ljvLRlB89HYd6G27h+8+5zpfa6cuUKlixZArVajaKiItwNOobF3Wy1fWUWqDB5fwR+7eMEU6FmM01URgEWXczAw4QkGBo+Fro8Lw8LFizAnTt34OPjg++++QqLOpnDVlpW5/tzcVAaG2BsK3MAmhnC+0ej8fkUX4zx1+y9yCsoRrO3t2H3gaNo06YNAOC9mTMQdXI7JrQsi450NzUfP13LRUxcwmu1JHucFw3jtRiAGMDPAJqSZrdftRX/ZXHsxEl4+wfofIFNzC3R2L01Lly4oC37558zaNerv07b9r3648Fd3TBeHfsMQkxsbKVjnjhxEh7d+sDS3gk9R7yFbkPGwMLWAW06dkNiYiL+97//Ydy4cfVC8QHg9OnTGNG1sU7ZGz4OiH0YV+nuwsrw9PTE1q1bsX37dtwJC0U3B4nO/0BqpI/mSmPcTi2zjTjKjSAx1MPt27d1+hIKhfj222+xd+9edO3aFUqJQEfxAaC7kwx308o8y3mMoZujFGdCyv5XQiMD9G/vgFOnTmnLThw7gk42unH6migE4KmLERER8VzX/qpTpfITUV8i+o6IgoiodlzNagBzMzOkP+ECS0RISYyDQlHmliqXmyA9KV6nXlpiPIzFEp0wXqmJ8eDz+ZWOqVCYIuOJvgAgLSleZ8z6gqmpHLHJ2TplqZn5UBNqJIClWCZHcl75r0hKbjEk/DIDaHGJGhk5BZXmIzA1NUVadr5OTjsASMkrhuQJb8Pk3GKYyXXlj0nN0/kfKBQKHSclAChUqZGdV9RgU5i/Nok6J0+aiGNb1+JhxF0Ams05hzf9DpHASMfQNmXKZGxY8gXycjRKUJCXiz+++gimSmsUFxUCADLTUrBhyQK4ODrCyaUR5KamGDRkKM6dOwfvtj4QGItgKBBi3fq/cP7IXtwJ0RiOiAhBR/YiPuoe+vbt+5I/gaqZNHUGPv7zMlIzS3MFFKkwZ9V5DB82rEa24n76+Xzsu5uORWdi8eau+5i0LwILz8QiMbsISpFmyl+iJmy+9Qgenp6ws7PTae/v3wNiQ33w9XjwaOEGoUiMbbceaX8AknKKsOl6Ku6l5WHEjrsYuv0upuwPx6F76WhiV+YfsPffCJy/mYhBgwZp+542aza238tFWumPk0pN2HDzEfw6+8Lc3PyFr/2VpCLvn5o8XpaH39p160hmYkJNWrQmSxs7atm6TTkPsd27d5NYKieBSEyNW3qSUCwhkVRGpmbmJDAWkX1jNzIwNCRrWztq7tWeFm08QL8cuUKjZs0jgbGIvLr2pu8DT9KyA0HUa9QEMjQSkJm5klyaupOdkws5uTSi4ODgl3K9z0pJSQm9P/s9kkmMqUMrJ1LIxTRkYEC57czPS1paGklFQhrqZkJ/9nemZb0cqKWFkFwc7EhiLCR3WzMyk4rIr0M7SkpK0mnr4+1NpkJ9mt/ZljYMcqE57azISI+RnZWSzGUiamZrRlKRkAR8PfK0MqafezvSHwHONLipCRnqMTJXyKlFYxtyc7YkJwcbOn/+vE7/arWaFi6YTxJjITW3MyeFRETdO/vW2Nbo+gpexMOvJniZTj55eXkIDg6GVCqFu7t7OSNWaw9PdB87A45NmyMxJhJKWwfERYVj9/KvsPKXFQgLC0Pr1q3Rp28//Lg/CEKRZr1+J+QSVn42Cz/t/Vcnss6iKcPh5dYIU6ZMAZ/PR+vWrV96nvVnJSUlBbdv34adnR0cHBxqrN8l33+Pv/9Ygnc9yqbzxSVqTD0SjwNHjqOwsBBKpRJNmujuVcjPz4dcYozF3ezhZFK2o/FkVCbWXU/DhavXkJycjJMnT+KnxYuwdoAL9Hll/9dFZx+iSOGEVat+g56eHjw8PCr8Hzx69AjXr1+HpaVlhUFLXieey8mHMbYfQIW/DEQUUNG5ukQoFKJjx4qfQt66eQOzvDrAUCCA3Eyz51ymUOKbWzfh6+uLzp074/jx43BybaZVfACIvX8b7m07lQup1bpjV1w7cwjt2lU7pGGdY2Zm9lT33Rcl5MoluMl1lc5Aj4dmSjGioqIwYsSIp7YLCwsDEXQUH9C4CxcUJcLV1RWurq747LPP0MxcqKP4AOBpZYzA+1Hw8fGpUkaZTAZfX98q6zUEKrtFLQHwQyVHvSAuLg6JiWVONmq1Gg8ePEBaWtpT6zs6OSPyVqhOWeSta7BzcNTeLZydnREdfhdFhQXIycxASnwszK3tcP/6VTw5U7obchkuTk6IjY1FUpJu4s3nJSsrC1FRUVVu5b1+/Toen1EREWJiYpCcnFxpu5ycHJw+fVrnc8vNzUVkZCQKCsos6TExMTh79qxOWWU0btoMkVm6zjVqIoSn5cHKygrbtm3TcSoqLCxEZGQk7O3tAQBxWUXIL1YjMacIxSVq3E8rgKG+HjIyMhAVFQUvLy/cT8sv9z+4nZIPE3NdN+P/Po/o6Gid74JKpUJUVBQyMzOrdU1VkZSUhNjYWK1MRISHDx/qfLb1lorWAzV51MaaPyQkhFq18SCZiSlJpDJq16Ej/fnnn+To7EIKcyWJxGLqG9CfkpOTddqtW7eObBydaeGG/bQ5OJa+3nyYHBo1oV9XrtSp59+rF5nb2JHAWERyMyXJzSxIn29IvUZOoN/P3KB1QfdozOzPydBIQK5N3UhuqiCxVEqdfDs/9060vLw8mvj2eJKIhWRrYUpWFma05s8/y9U7evQoKRVSEhrpk0hgQAqZiBYsWEDN3RqTuamEZBJj8u/mRzExMeXaDh44gAz1eSQ10iO+HqPWLdxp1szpJDEWkqWJhEykYvrsk3nU2NGODHiMJIZ6JDDQo2nvTK1S/vj4eDKVSWm6twUFDmtCfw10oV5NzEghk5KhHiMRn0cGPEamEmP6YO4cUphIyd5KQTKJiBRyKZkI9ElowCMzoT6J+DwSGvDIwc6aRAIjUsrFZGWuIKGBHvk7S2nDoEYUOKwxTfEwJ74eo3///VdHliNHjpCznQ0ppCISC42oT88e9POyZaRUmJBSLiaRwIjeGjuG8vLynut/FRUVRX4d2pFYaERysZDcXRvTunXrqIVbE5KLhSQWGlGndm3L7Y582eB54vZrKwCNAAQCuAUg8r+jqnZUi8qfkZFBCnNzmrrgR9p4+QH9dTGS+r89g4yExvTxr5to09UYWnPuLvV9czK179ipXPvfVq8mG1s7MjQ0IitrG1q+YkW5uP1mFpbUddAobbz8T1dvJyOhMYmkcuLp6RGPp0cSEwUZ8Pk045sVtPFKNP11MYJGv/cJOTm7UHFx8TNf19vj36TBXZpSyt4pVHJqFl39fRTZW5nS4cOHtXUyMzNJJOTTj9N9qeDYDCo6PpN+mNaJBIb6tGNBH1KdfJfyjkynL9/uQO5NG1NJSYm27YwZM8jM2IB+6eNIe0e60voBLuRuLiRzEZ/W9HemvSNdaVVfJ7IS88nFxIg2DW5Ee0e60hJ/exLz9einn36q8hpCQkKoUztvMtDXI6GRITVzbUIiPo++97envSNdadPgRtTRTkzGBjy6u3EclZyaRQ8DJ5Kp2JCaKgS0boAL7R3pSr/0cSQzoT41UxrTliEaORZ3tyOZSEgyYwHpMRCPgcRG+uXkunPnDplIRPRFZxvaM6IJbR/amDrYS0kmMKCfejrQ3pGutHFQI/J1VtC40SOf+f9UXFxMLg52NK615kdu94gmNM1LSYZ6PJrdzpJ2j2hCgcOa0FttLMjR1qZOcwNUpvzVsUytBbASgApAFwB/AdhYC5OQarN582Y09WgH335DwdPTg76BAYoK8tFzxFto7uMLxhiMBEKMeHceoh5E49q1azrtJ0+ahJjoB0hLS8XD2BjMmD5dxzB4+PBh5OXlYfyHi2AkNAZjDG6e7RAwfhpMzC3wx9lbWH36OvqNmwqvbm+gfc/+4PF40Dfgo8/YqRBITfD3338/0zU9evQIO3fuwm//84WJRLP2beVihi/He2L5T99r633xxRdo4WSKWUNaw0BfD3p6POQVqjCqWxMM8nUBYwyGfH3MG+MBPvJ1HF02r1+DqZ5K2Eg0jjMygT7e87FEbqEKEkON+cdSzMd0bwsUlaghKn0238hUgLEtzbD0+2+qvI5WrVrhbNBFZGXn4FFWNuJjo/FmCzM0NtU8ShTx9TDD2xIqNSEmSRPxRywwQFZeEea0t4JcoJHDRmKIaV4WKCwugdBAI0dTMyGGNBGj1xu9kZbxCHHxCcjKL8Z7772nI8OqX1agh70x2liKNJ+HPg+FxSqMa6nQ2hXEhnqY0kqOXbt3P7OD09GjR2GoysMgVxkM9DRRmAtVBG8bEfwcpOAxBgM9hgFNZBCzwmfa4fgyqY7yC4joBDSuwNFENB9Anyra1CoxMTGwcNS11KYlJcCukW5obZ6eHmycXBD7FE89xhiMjY2f6tIaFhYGpY0D9J+I7W7byBX5uTkwEgghFEmQlpgAhybNyrW3cmr01DErIykpCQqZMeRiXaNXMwcTPHz4UPs+PDwcrVx0jXWxydnlyhhjcLM30ZEjv6AQdk94zCmE+mAMyFeVaMvspHyk5+tuo7WV8pGdpesgVBlGRkYwMDCASlUMO5numAIDHmRG+rh4W7MuTkjLBSuVRXdMw3Jy2En4iHnwAFKpFBYW5df5ABAdFQEbka5hNi1PVe7aRXw9yI2NqrSRPElsbGy5/lPziuH0xHUCgI2I98zfhZdFdZS/kDHGA3CfMTaDMTYQmu29dYaPjw/Czp3Uccd1bNocl04c0qmXk/UId68Fa/27q0u/fv0QG3EXWRm6RsPLJ/+GmZWt9r2zeytcPHZQxwClKi5C2Pmzz7yDz9HREdn5xbj1QHfMgxei4eVT9iShd+/e2HcuEsWPKatnEyW2n76P9Kx87DobjsMXHyAzpxAnrkbryGGmMMXlON3QWHdS82Gox9Pe5QHgclwO7J9QlEvxubBxcMCRI0ewc+dOrREtNzcX+/btw759+7QJSNPS0rBz504cOXIEQpEEFx/qjpmYU4T0fBVGdtU88nO2lkJfj+FWiu726Mtx2XB4QqGuJheifSe/Sj5JoF2nzghO1f3RcJAZ4kKs7o/Xw6xC5BarkZ+fj+3bt5ebIVaEt7c3QpPyUFxS9v1rZGqEc7HZOt+F4hJCaGJ+vdjN+TSqs7HHC8BtADIACwFIAXxHRBcqa/c4Nf2cX6VSoaOvH/QkpvAfMQElqmLs+3M57t8IQfteA9Bl4Ehkpqdi80+L4Ne+LTZu+OuZx+jQsROi4hIw+r1PYaK0xD/7d+Dk7s1wbOyGIdM/BN/QCEe3rkHYhbNo2c4P3Ye/haLCAhxYuwIOlmbYvTPwmcdc+euvWPLNfHz1tiea2ptg37korNh3G/8GXdQ+k1ar1bCxUKCprQifjW0LA30eFm+6hBNXH4LHY2jvbonsvCLcfJCO9h064e+jx7X9b9myBW+NHYNRzRVobWGMqIwC/BGcjGI1YZKHORqbCnAtMRcbr6fCUI9hqpcSViI+zj3Mxt7b6ZDK5XCylMBEYoRz12MxbMRIBG7bCicTjWttRFoeho0cge1bt6J9cxtkZBfgbkwGcrKz0L+JCTraiZGQU4w/gpOQVViCxVM7obuHHS7fTcL0H09CnwET25jD2cQIIQm52HQ9BXKRAG+1kENpbIBzD3PxT5IaV0KuwdrausLP8dGjR2jTojmaiQrR1c4YmQUqbLydhaTsQgQ0kqKttRBxWUXYdCcHphbWSIyLRRMzEe6n5qJ5qzbYte8ARKLK729DB/ZHRPA5DHIRwlCPh0OROQhJzEVrSxHecDRGUYkau8PzYNPcG3sOHKq0r9qkpiL5SAAQEVV/7ldKbTj55OTk4PslSxC4cxf09Q3Qrq0Xdu7ZhxYduuDetSsQiiSwcnRBVOhFRITff+a0XGq1GlOnTsWuvftQVFgIR0dH/LbyV5w7dw4bNm1GUVExBg4IwDtTp2LlqlXYvWcf+HwDvDl6FN59993nTge1b98+LF/6PeLi4uDTrgM+mvcZGjcu24yTkZEBJwc7NLUVITwuE0QECxNjxCRl49JvI9DIRuOnfvjiA0z44R9ERT/Ucd0NDAzER3PeQ0pyMkRiEaztHaHQzwQVFyE8/hHcHRVIyMhHQlouCgqKkF9UAitTY8Sm5mLdx/4Y7Kf5EbpyNwm+07dhUTc77Xr+flo+5p2IwdkVw+DlqpmS7/4nHBN/OAt9xpCXmwM9Hg8Wdg4wFxTCUm6IG1FpcLaWgcBDcqEE8bEPkJOdAzNzc3z13Q8gIqz4aQmSk1PQuWs3fPL5F9pHg5WRlJSExV8twuGD+yGRSjHpnRnw9fXFN4u+RNC//8La2goyhTlSwoIw28sU+jyGEjXhl+B0NOrcHytX/15p/8XFxVi+/GdsWPsnCgsKMGDIMLwzbTpW/7YKu7ZvBZ/Px+hxb+PdWbOq3CNSm7zoll5PaIx+/3m8ZAJ4m4iuVleAl+HhN/7tCVCb2KL36Ik65Z+N7oW1v618bcJn/fnnnziyZSm2ftpNW/bx6n/BGMPXk3RjD/h/eAjTP/oWAwcOfGpfKpUKMqkYEZvehJmsbGPM9YgUDPz0ACK2vAUAOHo5Gp/9GYSLq0Zq6/y6+xoC94dgto9uurGlFxLQv08rzBzcSlvW6b19+Gzxr9qovs4ONgj81A8tH7NTpGbmw2nUX8h4lPXS8ugpFSaY7yOHtaRMOdPyivHusThk5eQ91xbn+saLbuldA2AaETkQkQOA6dD8GNQrsrKytEE0H0cslb9WIaCzs7NhItJVjuy8YpjLym/MMREbVnrtKpUKxSoVpMa662pTiQDZeWURerLzimAq0e0/O79IZ6fef0gM9ZCTX6RTZiIx0pEjOydX+0RD207Ih0pVApXq5cXry83Lh9hQVwVEfD3kFxQ9NRLQ60Z1IhiUEJE2Zh8R/csYq3cRFQP69sGPK39Hh94DtC64CdGRiLxzo9JoPK8avXv3xuKv5mNMdxccvRwNVYkaduZi/LrnOlo3MsfRy9EQGOqjS2sbHL0UiX7jgLnvvw9zpRJjxozRSQxqZGSEDj7e+OvoHUzsU/bUYvWBMLg5mGDgp/uRlJ4LHzdL/BMWh3+ux+Gfa3HIyiuCs5UUp6MeoYOtCMEJGkNfG0tjnIx8BKfmBWg/bStEAj4mvNEM/16LxYjCQsx9fw4sLK3QpUsX/HnoFuaPL8uBsPHYHbRr61lud2FiYiI2btyI5KRE+HXugl69ej1XZuWn4d+jO45FXsbgpmU3jeNRmejUvi1WrlyJqMgItPVph4EDB1Z7NnL58mXs3rUTfL4hho8YUS65a32iOtP+pQAE0CTsJADDARSg9Fk/EQVXNcjLmPYXFRWhZ+83kJKZA59eA5GVnoKTgRvxzdeLMGnixKo7eIXo3rUzLvz7D3o4y6DPYzgS8QhqMIiN+ZjYxx2Pcgqx9vBNCIyEkBvpw9uch7QiHi7G52HHrj3o1q1syRAaGoqePbpicCdHeDU2xalrSThwPhJ5+fkY1MkFzRxMsOn4XUTGZ4IRoauTFBK+Ho5HZaJAxQBSoYeTDIwBRyMeoYQYBPoMPZykyCoswcmoTAhFYjhbSzGgnQ3CE/Kw91wkDA356N7GGl1bKnHlfhoCz0bh8JHjOk9mTp06haGDB6B/B0c4KoXYdS4WStvG2HvgcI2so8PDw9GxnQ9aKvTQVKqH+1lqnH+YB54eQwszI9gICdfSCXoyJU79cw5SqbTS/ub87z1sXr8GvtZGKCaG0zG5+HLRV5jx7qxK29UmL7rmP1XJaSKirlUJ8LJ29RUXF2PHjh049PffkEllmPD2W2jdunXVDV8hoqOj0crdDUu6WsLMWHM3yipUYdqBKBxcMggdmmvyDyxYdwHbDlzHN93toFe6ESY0MRe/3SpATFyCzt0zPj4ev/++GuF3b8G9RWt8tWgB/vrYHwEdnAAAj7ILYD3od3zbww4OMs10PSqjAPNOxODXPk5ax5yMfBWmHYzEoq62cDbR3MGjHxVizpEHuLdpPOwsNEk+9wdFYvbqEEydNgPXQ6/CpXFTTJw0WceCX1JSAmdHW6ya2Rb+XhoDn6pEjTc+PoRB42dj2rRpNfJ5pqam4o/fV+Na8FU0dW+BPTu3o6MwA92dNIpORFh+NQ2eA8Zj8XffV9jPxYsXMfANfyzpotQ+Nk3KKcL7JxNx6+69Sp9O1CYvtOYnTdiuio4qFf9lYmBggFGjRmHjX39hxfKfXzvFBzRPA9raiLSKDwASQ310d5bi2JVobdm/obEY2NREq/gA0MrCGEYoQXCw7mTNysoKX3wxHxs2b0dTt2YQ8vXQr31ZItPT1+LQVGmsVXwACEnMhZ+9RKv4ACAX6KOzgwQhiXnaMnuZIRqZGuGXPWXP0Pu2cwTUhej9Rh9s2LwdX8xfUE45QkNDITSAVvEBQF+Ph+kBTbEncOszfWaVoVAo8NHH87Blx05MfecdhEdEoouDRHueMYY+TiLs3LGtkl6AXTsD4WttqOMvoRTx4WUjxsGDB2tM3pqkSuVnjCkZY38yxg6XvndjjE2ofdE4noaBgQFUVN4KXawm6D+W8MJAXw+qJ0JgERGKS0oqXb8KBAIUq9R4fEJooMdDcYluX3qMoaik/KyxuISg/4R4KjWBb1CmFERAUXHlchgYGKCouKTcDr7CKtq9CPr6+iAiqJ8Ys1hNMKgiwCefz4fqaXkM1HhpTy+elepY+9cBOALgv3xW9wC8V0vyvLbExcXhg7nvo0snH4x/cxQuX778XP0MHDgQV+KyEf2oUFuWlFOE4xGZ6NKmzPuwrbsVNoelYOP1FHx6Igbfn4vDpuupMBLL0LJlS50+b9y4gUkT3kKXTj7Yv3c31GBYc/im9nyH5pYIT8tDWFKutqy5UoB/Y7PwMKtMjrisIpyNzkLzx5J33kzOQ9SjQkwNaKEtW/f3bSjMlDr+C0/SvHlz8AVibD5eliw1N78YP+28ieGjx1XY7kUwMTGBl6cn9t8vyzJcoibsDs/BqLHjK207fMRInI7N04kTGJlRgND4bAQE1MvQF9Xa1Xe59G/IY2WhVbV7/KhPiTrrgsjISLKyMKNZQz3oyJKB9ON0P1IqZLRnz57n6s+3Qzsy1OdRJwcpdXGSkpE+j+QSERkbGdDoHq7Ux8eRDPRARnqMOtmJ6YvONjTNy4IkhnpkaaHU6evUqVNkZiqlryZ1pKNLBtK8N33IRCYmAV+f2jezpAl9mpFCKiBrS3OSS0TU0UVJvVyVJBcLyVqpJEM9Rh3txNTJTkyGeowM9RgZ6TPq4iChNpbGZMBjZGGmIFsLE5o6oA11b9uIbKyUFBYWVuV1hoSEkKVSQf4+jWnKgNZkrZTTW+Pf1NmpWNNERUWRo50NtbAzo75uSrJVSMm/WxcqKCiosu0PS74nibGAergqqXMjJUmMhbR9+/Zak7U64AUTdZ4GMBjAMSJqwxjzAfAtEVXuYP0Yr2uuvuoyccJ4WKruYMFbZZFmToc+xORll3Av4sEzhf0KDg7GwL7+OP1jPxy7GgtViRq9vO0x4POjGDdlNi5fvgyhUIg9OwPRQg7Mbl+2lo7LKsKsw1EIuR6GZs00j/a8PVpiboCN1nMPAJbvDMXxCD4aubojOjoagwYNwujRo5GZmYk9e/YgOzsbCoUCc2dOxRftTBGSmAcCoY2FMT4+EYN+jWW4l1YIoQEPfRrL8Pk/ydi6Yyfu378PCwsLBAQEwMjIqNy1PY38/Hzs3bsXycnJ8PPzKzdrqQ2Ki4tx8OBBREdHw8vLC+3atau2w09sbCwOHDgAPp+P/v3713kU5xe19rcBsByAO4AbAMwADCGiaud5aujK39jZHjs/9UUzx7KEEUQE62HrcTk4DLa2tpW01uXHH39E5LlN+Hmmrsfil+svosiyM77+WrPtVmKkj9k+lmhjpeujPu1AJDoHDMXGjRuRn58PmUyCnEPvQO8xe0FSei6avb0N6Y+yUBFff/01rm5einEtTHXK14UmQ6jPwzD3si/991ceYeaiZRg2bFi1r5OjZnhRa38wNNl62gOYAqDZsyg+B2CmUCA6SVeRHuUUIr+guMpnx09ibm6O6Cd2vwHAg+Q8mJsrHyth5WLoq9SEjAIV3N3dAWiMVAIjI8Sn5erUe5CYDTNFxTH1AU0cwNTC8nfDhOwiSIweN+6RJq5+LcQM5HgxKlR+xpgXY8wCAIhIBcADwFcAfmCMVf7N4NBh8rR38cmaK4hP1bi4FhSpMHtlEAb0D4BEIqm0bVFREb74/DPY21hCJhVh5/YtuHQ7EdtP3dNawo9cisb+oCicO3sKJjIJrC3NYW5th03XUxGXpXG1LVETNl5LAQB89NFHADRZht9++y28u+Kc1iU3LTMf7/92Hq7NmsPJzgYioQA9uvjiyZnb0KFDcTO1ABcfZmvXkBcfZiM4IReWpe7HaiIcCs+Emm/cIIJmJiQkYNyYUZBLxDA3kWPWjOnIzn7mfXAvj4qMAQCCAZiUvvYFEA/N2n8hgMCK2j3taOgGP7VaTV98/inJJCJq29yBFHIxDR7Ql7KysqpsO2bkMOrVrhFdWzOaEndPom+ndCKFiYycHW2pkb2S3JytyM7agpTmCvp0bFt6GDiRbv81lnr7uJCBHiO+HiMbCZ+MDXgk4vOo7xu9dPovKCig8W+OIhOZiHxaOJJMYkzeHq2pqZWcvve3p02DG9F0bwuSS0R08+ZNnbZBQUHkYGNF9uYycjCXk721JS1atIjMTGTU2MqULE0k1LKZK927d69GP8/6SG5uLjnb29KgZua0pr8zrerrRN1cFNShrVe5EHEvEzyPwY8xdo2IWpa+/gVACmmi+IAxFkpErar7A9PQ1/z/8ejRI9y6dQs2NjblstU8jcjISPh4tUbU5jchMCx7zjx92VmYub+BQYOHQKVS4dy5c/h332ps+6ws0+7MZadgbGSA2cPaYO+/EXCwlMCjsRKN3tyIe+FR5abh8fHxiIqKgpWVFVo1d8fPPax0HHgCb2eA594N6zZs0mmnVqsRGhoKItLmLCgqKkJISAhEIhHc3Nxei91xVbF27Vr8tvBDzPMps4GoiTD7ZDLWbt8NP79q28drlOdd8+sxxv7773cDcPKxc69nStNaRiaToX379tVSfAC4efMmPJta6Sg+AHRuaYlbYaFo1aoVPD09cTPsGjq3UOrUuR2dju6edjCXCzGpX3P08LSHicQIrg7muHfvHp7EysoKHTp0QGZmJswlAh3FB4DmZka4HhpSrh2Px0ObNm10EmXw+Xy0bdsWzZo1axCKDwDXQ0Pg+oT5hscY3BR8hIVVnOm5LqlM+bcAOMMY2wsgH8A/AMAYc4FmT3+D5eHDh4iKiirnffY4JSUluH//PlJSUp57nEaNGiH0XiKKikt0yi/eToFLk6aIjY3FgwcP0Ni1KS7eSQMRITI+E/GpOXCxluH8Dd3Epdl5RbgbnQInJ6cKx7Szs0NSZh6yCnXHvJteiCauT9+hdv78eZw7d+45r1KXrKws3L17t9q5AuoLjV2bIjJH94eOiBD+SFWpM1OdUtF6oPSL7QNgIADjx8oaA2hTWbsnj9dlzR8eHk7tvDxIJhKQQiqipi5O5XLCERHt3LmT7G0tS2PSG9OAfm9Qamrqc43Zy78bDfJtRLE7JlD+0Rn0xwfdSSYxptYtmpGZiYQsFDJyb9qIZFIRWcgEZC42JIlAnyzlQjI2MqDNn/WmwuMzKXLLW9S3Y2MaP3Z0lWO+M3kSediZ0qq+TrRreBP6qKM1mUiM6cqVKzr1AgMDSS4SkNBAE2NfZmxEO3bseK7rLCwspGlTJpNYKCBbMxnJJSL69ptv6nS9/CxkZmaSldKMxrVW0rahjWnjoEbU382cWjZrWqtOSVWBF4nbXxPH66D8RUVF5GBrTRM8LGjn8Ca0Z0QT+qCDFZlKJTpJJ4ODg0mpkNGZn4dQyalZlH14Gr07xIO6dymfP6Aq1Go1+Xi1IS9XCxILDUhfj0cejc1JYKhPn7zpTYXHZ1LxiXdp2Uw/MtTn0bxO1rSnNGb8mJZmZGuppHbebUhfX4/kUjHNfX92tTzVioqKaN5HH5BcIiZ9PR61bu5Gx48f16mTkJBARvo8mu5tQbuGN6Fdw5vQzLYWZKjPo7i4uGe+1tnvvUte9qb010AXbf4AR3MZrV+//pn7qivCw8PpDf/uZKCvT4Z8Axo1bEi5pDEvm8qU/7VL1Flb7Nu3D5/NnIiFHXSdWn4JTkf3CXMwd+4HAIBJE96Cs344Phjpoa2jKlHDcdRGnDgTBFdX12qPGRISgsEBPXFvvSZ8VolaDQN9PXz+ZxCy84vx0wyNEWnh+gs4/+9dTPPSDWX9wZkULF+3BX5+ftDX13/mBKJEhOLi4qfunZ84cSKCD23D/M42OuVfnnmI5v5DsHZt9YM9FRUVwcxUjp+6WUIhLNsEE5KQi72pIgSH3XomuesalUoFxliNBR15EV40jBcHNBtzrI3Lf1xWAsLD6Bjt+4S4GDSxlenU0dfjoZGtKeLi4p55zMZ2JuDxGHg8BgN9zZepmaMCD1PKwmLFJmWXC7UNADZiA8TFxYHP5z9X5mDGWIVBM6Kjo+EgK3/OQWaImJiYp7SomJycHFCJGqZPGBltJHzEJ7wCOe+eQF9fv14oflVwyl9NfHx8EJKoG6udiBCcRmj/WHBQ7/a+2H9BN0lDUnourt1PRKtWrZ5pTA8PD1y88RDpWbrGr+2n7sHL1Vz7vp27Ff59ImZ8oUqNawnZaNu2LWoDf39/nI/NQclj24ZL1ISg2Gz06NGjkpblkcvlMDMzxc0nPBcvxeXU25j3rwPctP8Z6OLbAfG3QjC6uQJ8fYY9t9ORqi/H7fsR2j3bqamp8PZsjX5eSozq5oK41FzMXx+MASPG48uFXz3zmP379cGda0H4bmonKOVC/H4gDDtOh8OjqTXmjWwFA309/Bh4Hf+EPoSHpRA97IXIL1ZjV0QeWvv1xPqNm2v6YwCgeb5vba6ABb8IQ5pqHD533k5HfKEBHialQL+K/e9PEhgYiGmT3sKIxiI4mRghNCkf+yPzcOL02Wf+0eQoo7JpP2fwqyaZmZlkIpPQjIEtybORgprby2n6gBZkIhPT3bt3deomJCTQe7NmUstmTahLp3a0adOm57JaFxYWkrHAgKYGNCe/ltbU0llB7/RvTgqpgPr160dtPVqSR8tmtGjRQoqPj6dP5n1MzV0bkVfrFvTLihWkUqlq6vKfSkZGBr3RuxeZiARkIhJQ7149KSMj47n7O3XqFPXp2Z2aujjS6OFD6caNGzUnbAMFnMHvxdm0aRO2/fYV9nzpr1M+d9U5GDfuhS+/XFjjYy5fvhzLv/sCdzaM1SlfFhiCP0/E4cbt8Bofk+P1gjP41QC5ubmQicobuGTGBsitpbwAGRkZkInKG/JkIkMUFRY+pQUHR/V57ZW/qKgI27Ztw3vv/Q/ff//9M2dk/Y+ePXvi4PkIJKaXbX/NKyjGxhMRsHdwxNz35+CTT+bhxo0bNSU6Jk6ciLDIFNyOLkshrSpR49c919G5e+8aG+dFuHr1Kj766EN89OEH5Xb+cdRvXmvlz87ORodOvli0ZCmSYYwj54PRtFkzXLhQ7RyjWuzt7TF7zgdoN2M3Fm+6gmWBIfCZsRtGIhMs/W4hjNOCoHpwAt06d8Ty5ctqRH4rKyu07+iL9tO24dM/gvDr7mvwmrIFEQlZ+OGHH2pkjBfhywVfoH+fHtCPPwP9hLMY0Ncf87/4rK7F4qgmr/Waf/6CBTh+IRjTvlqh3WBy8fhBHF2/AjfDrj/XppPz589j86YNKCosRKMmTbFy2Xe4snIwpKXT8+jELHhM3YEbt+7Cysqqit4qJy8vD3Y2Vpg7zB27zoYjr0CFzi2tceFuOuZ8/j1GjBjxQv2/CHfu3IFfRx9c+30YzOWagJ3JGXloNXk7Tp4JgpubW53JxlFGg13z796zF92GjtNRcu9ubyAlJQUPHjx4rj7btWuH5St+xW+//4mkxASM7e6iVXwAsLeQoHdbRxw69OJpmf/55x80c1Rg7ghPnP91BK6tGYNls7pgSl9X7N21/YX7fxH27duHYZ2dtYoPAOZyIYZ3dsG+ffvqUDKO6vJaK7+BgQGKi3QNYyUqVYUuq88K39AQ+U/suAOA/CJ1zfTP5yO/sHxaxIJCFQwNqxcAs7bg8/nILyofqD6/qKROU1JzVJ/XWvnfHD0KB9f9qvMDcGz7eri5udVI+qSRI0dh3ZG7iHksPt+Vu0k4HRJTI7HaO3bsiPj0fBy6EKUtS88qwPK9tzFyTM3Frt+2bRuaODvCXCZCCzdXHDt2rFwdIkJgYCD69e6Bzh3bIjExAbvPhusYI+/EpCPwTDiGDh1aY7Jx1CIVOQDU5FFXTj5FRUU0ZNhwUlrZUPfBo8nd04ccHJ3o/v37NTbGsmU/kVwqolH+zWlgZzeSS8XPHY//aZw7d47MFXLq1b4JjXujJZmZSOjDD96vsa2uS5YsISN9Ho1rZUZfdbOj4e6mZKjPo23btunU++jDueTuYkkbPulJh74dQEO6upGTvS3JJMY0tJs7DevuTjKJiNatW1sjcnHUDGjoTj7BwcG4ePEirK2t0bt37xpPnxQXF4fDhzWZY/v16we5XF51o2cgNzcX+/fvR2ZmJrp16wYXF5ca69tELMCUVqZoZyvWlh28l4FDD0sQm6gJRBIXF4fmzVxxd/0omEo1CTiJCH3mHUbPYe9AJpOBiNC3b1+Ym5s/dRyOuqEyg1+DCMfVpk0bndTPNY21tTUm1mIacGNj41qx7KenpyMztwDe1rqx/TvYibE2NEL7PigoCL6t7LSKD2h2/A31tcfpy+exYXPdGh85no/Xes3PUTkikQh6PIb0fF2jYlJOMYwMyu4LSqUSUQmZ5cKWRSXmwNzixR5nctQdnPI3YPh8Plq3aoVfLicir/SpRWaBCquuJqFbrze09Tp27IgSnhBLA0NRUrql+cKtBKw+cBsTJ02pE9k5XpwGsebnqJisrCz4eLZGRGQUzEV8JOcUoXXr1vj3/EWdbblRUVEYNXwwYmMeQCYSID2nCL+sXI2BAwfWofQcVdHg1/wcFSORSHDrXgRu3LiBy5cvo3PnznB0dCxXz9HREecvBePevXvIzs5GixYt6m3eeY7qwSk/BwDA3d1dm8OvMuptGGqOZ4Zb89cARIR79+49t8swB0ddwCn/C3Lu3Dm4NXFBd7/2aOvZEu282zw1Iw4HR32Dm/a/AImJiRjYvy9+e68jAjo4Qa0mrNp3A738u+Hu/UhuTcxRr+Hu/C/AX3/9hYD2Dujf0bk0TjsP0we2gK0pv0Z29XFw1Cac8r8A8XGxaPKEdxwANLaRIj4+vg4k4uCoPpzyvwAdOvpi7/mHUD8Wu76gSIW/L0Wjffv2dSgZB0fVcMr/AgwYMABkpMDwhcdwOvQhDl98gF4fHkTnLt3RsmXLuhaPg6NSOIPfC2BgYICjJ05j6dKfMG9jIPh8PkZMmovJkyfXtWgcHFXCufdycLzGNNgYfhwcHBXDKT8HRwOFU34OjgYKp/wcHA0UTvk5OBoonPJzcDRQOOXn4GigcMrPwdFA4ZSfg6OBwik/B0cDhVN+Do4GCqf8HBwNFE75OTgaKJzyc3A0UDjl5+BooHDKz8HRQOGUn4OjgcIpPwdHA4VTfg6OBspLieHHGEsBEF3rA3FwcDyJPRGZPe3ES1F+Dg6O+gc37efgaKBwys/B0UDhlJ+Do4HCKT8HRwOFU34OjgYKp/wcHA0UTvk5OBoonPK/IjDGPmGM3WSMXWeMhTLG2tZw/50ZYweqW14D4w1gjLk99v40Y+ypCSU5agcuRfcrAGOsHYC+ANoQUSFjTAGAX8divSgDABwAcKuO5WiwcHf+VwNLAKlEVAgARJRKRPEAwBjzYIydYYxdZYwdYYxZlpafZowtK50l3GCMeZeWezPGzjPGQhhjQYyxJtUVgjFmzBhbwxi7VNq+f2n5eMbYLsbY34yx+4yx7x5rM4Exdq+0ze+MsRWMsfYAAgB8Xyqfc2n1oaX17jHGOtXEB8dRCUTEHfX8ACACEArgHoBfAfiVlhsACAJgVvp+OIA1pa9PA/i99LUvgBulryUA9Etfdwews/R1ZwAHnjK2thzA1wDGlL6WlcpjDGA8gEgAUgBG0OzjsAVgBeABAJNSWf8BsKK0/ToAQx4b5zSAH0pfvwHgeF1/7q/7wU37XwGIKIcx5gGgE4AuALYxxj4CcAWAO4BjjDEA0AOQ8FjTLaXtzzLGJIwxGQAxgPWMsUYACBqlrC7+AAIYY++XvjcCYFf6+gQRZQIAY+wWAHsACgBniCi9tHwHgMaV9L+r9O9VAA7PIBfHc8Ap/ysCEZVAc3c8zRgLAzAOGiW5SUTtKmr2lPcLAZwiooGMMYfSPqsLAzCYiO7qFGqMj4WPFZXg+b5b//XxvO05ngFuzf8KwBhrUnqn/o9W0Eyt7wIwKzUIgjFmwBhr9li94aXlHQFklt6ZpQDiSs+Pf0ZRjgCYyUqnGYyx1lXUvwzAjzEmZ4zpAxj82LlsaGYhHHUEp/yvBiJopuq3GGPXAbgBmE9ERQCGAPiWMXYNGrtA+8faFTDGQgCsAjChtOw7AN+Ulj/r3XUhNMuE64yxm6XvK4SI4qCxE1wCcA6a9X9m6emtAOaWGg6dn94DR23C7ed/TWGMnQbwPhFdqWM5RKU2C30Au6ExSO6uS5k4NHB3fo7aZj5jLBTADQBRAPbUqTQcWrg7PwdHA4W783NwNFA45efgaKBwys/B0UDhlJ+Do4HCKT8HRwPl/xTwribEa/QuAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import joblib\n", "\n", "from sklearn import datasets\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.model_selection import train_test_split\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "from clearml import Task\n", "\n", "\n", "# Connecting ClearML with the current process,\n", "# from here on everything is logged automatically\n", "task = Task.init(\n", "    project_name=\"Getting Started\",\n", "    task_name=\"Scikit-Learn\",\n", "    output_uri=True\n", ")\n", "\n", "iris = datasets.load_iris()\n", "X = iris.data\n", "y = iris.target\n", "\n", "X_train, X_test, y_train, y_test = \\\n", "  train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "model = LogisticRegression(solver='liblinear', multi_class='auto')\n", "model.fit(X_train, y_train)\n", "\n", "# Using joblib to save the model will automatically register it to ClearML, too!\n", "joblib.dump(model, 'model.pkl', compress=True)\n", "\n", "loaded_model = joblib.load('model.pkl')\n", "result = loaded_model.score(X_test, y_test)\n", "x_min, x_max = X[:, 0].min() - .5, X[:, 0].max() + .5\n", "y_min, y_max = X[:, 1].min() - .5, X[:, 1].max() + .5\n", "h = .02  # step size in the mesh\n", "xx, yy = np.meshgrid(np.arange(x_min, x_max, h), np.arange(y_min, y_max, h))\n", "plt.figure(1, figsize=(4, 3))\n", "\n", "plt.scatter(X[:, 0], X[:, 1], c=y, edgecolors='k', cmap=plt.cm.Paired)\n", "plt.title(\"Iris Dataset\")\n", "plt.xlabel('Sepal length')\n", "plt.ylabel('Sepal width')\n", "\n", "plt.xlim(xx.min(), xx.max())\n", "plt.ylim(yy.min(), yy.max())\n", "plt.xticks(())\n", "plt.yticks(())\n", "\n", "# Plt.show() will trigger ClearML to log the resulting plot automatically\n", "plt.show()\n", "\n", "# Always close the task when in a notebook! If using a python file, the task is\n", "# closed automatically when the script ends.\n", "task.close()"]}, {"cell_type": "markdown", "metadata": {"id": "AaxJ4QmErwTw"}, "source": ["# ✋ Manual Logging\n", "\n", "Naturally, when our integrations aren't cutting it for you, you can always manually log anything you wish!\n", "\n", "Check our documentation for [a list of examples](https://clear.ml/docs/latest/docs/fundamentals/logger#explicit-reporting-examples) of how to manually log anything else!"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "Rn1Csnat7b5D"}, "source": ["# 🥾 Next Steps\n", "\n", "Now that you have the basics of the experiment manager, take a look at **running this experiment remotely**! Start by setting up a remote agent and then clone and enqueue these experiments using the ClearML orchestration component.\n", "\n", "The [second notebook](https://colab.research.google.com/github/allegroai/clearml/blob/master/docs/tutorials/Getting_Started_2_Setting_Up_Agent.ipynb) in this series will get you started with this."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["<table>\n", "<tbody>\n", "  <tr>\n", "    <td>Step 1: Experiment Management</td>\n", "    <td><a target=\"_blank\" href=\"https://colab.research.google.com/github/allegroai/clearml/blob/master/docs/tutorials/Getting_Started_1_Experiment_Management.ipynb\">\n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/>\n", "</a></td>\n", "  </tr>\n", "  <tr>\n", "    <td><b>NEXT UP -> Step 2: Remote Execution Agent Setup</b></td>\n", "    <td><a target=\"_blank\" href=\"https://colab.research.google.com/github/allegroai/clearml/blob/master/docs/tutorials/Getting_Started_2_Setting_Up_Agent.ipynb\">\n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/>\n", "</a></td>\n", "  </tr>\n", "  <tr>\n", "    <td>Step 3: Remotely Execute Tasks</td>\n", "    <td><a target=\"_blank\" href=\"https://colab.research.google.com/github/allegroai/clearml/blob/master/docs/tutorials/Getting_Started_3_Remote_Execution.ipynb\">\n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/>\n", "</a></td>\n", "  </tr>\n", "</tbody>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"accelerator": "GPU", "colab": {"collapsed_sections": ["TLsgNR5PhPPN", "pkYjS0RTtJ6s", "5iwK3p-X3WIC", "fsHJZGcC3afb", "nXCUDvfZ8kAs"], "provenance": [], "toc_visible": true}, "gpuClass": "standard", "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}